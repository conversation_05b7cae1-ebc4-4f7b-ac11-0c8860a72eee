{"name": "evelin-mobile", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "analyze-bundle": "node scripts/analyze-bundle.js", "performance-check": "npm run analyze-bundle && echo 'Performance analysis complete'", "test": "jest", "test:offline": "jest --config jest.offline.config.js", "test:offline:watch": "jest --config jest.offline.config.js --watch", "test:offline:coverage": "jest --config jest.offline.config.js --coverage", "test:offline:ci": "jest --config jest.offline.config.js --ci --coverage --watchAll=false"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/netinfo": "^11.4.1", "@reduxjs/toolkit": "^2.8.2", "@shopify/flash-list": "^1.7.6", "@supabase/supabase-js": "^2.53.0", "crypto-js": "^4.2.0", "expo": "~53.0.20", "expo-audio": "^0.4.8", "expo-background-task": "~0.2.8", "expo-constants": "^17.1.7", "expo-device": "^7.1.4", "expo-haptics": "^14.1.4", "expo-linear-gradient": "^14.1.5", "expo-location": "^18.1.6", "expo-notifications": "^0.31.4", "expo-secure-store": "^14.2.3", "expo-speech": "^13.1.7", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.10", "expo-task-manager": "^13.1.6", "nativewind": "^4.1.23", "react": "19.0.0", "react-native": "0.79.5", "react-native-maps": "^1.20.1", "react-native-reanimated": "~3.17.4", "react-native-svg": "^15.11.2", "react-native-url-polyfill": "^2.0.0", "react-redux": "^9.2.0", "redux-logger": "^3.0.6", "redux-persist": "^6.0.0", "redux-persist-transform-encrypt": "^5.1.1", "reselect": "^5.1.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@babel/plugin-transform-export-namespace-from": "^7.27.1", "@babel/preset-env": "^7.28.0", "@babel/preset-typescript": "^7.27.1", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.2", "@types/jest": "^30.0.0", "@types/react": "~19.0.10", "@types/react-native": "^0.73.0", "@types/redux-logger": "^3.0.13", "babel-jest": "^30.0.5", "babel-preset-expo": "~13.0.0", "jest": "~29.7.0", "react-test-renderer": "^19.1.1", "typescript": "~5.8.3"}, "private": true}