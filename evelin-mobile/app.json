{"expo": {"name": "<PERSON><PERSON> - Voice Location Helper", "slug": "evelin-mobile", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "statusBarStyle": "dark", "statusBarBackgroundColor": "#f8fafc", "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#3b82f6"}, "ios": {"supportsTablet": true, "infoPlist": {"NSLocationWhenInUseUsageDescription": "This app uses location to provide location-based task reminders.", "NSLocationAlwaysAndWhenInUseUsageDescription": "This app uses location to provide location-based task reminders.", "NSMicrophoneUsageDescription": "This app uses the microphone for voice input to create tasks."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#3b82f6"}, "edgeToEdgeEnabled": false, "statusBarStyle": "dark-content", "statusBarBackgroundColor": "#f8fafc", "statusBarTranslucent": false, "notification": {"icon": "./assets/notification-icon.png"}, "permissions": ["ACCESS_FINE_LOCATION", "ACCESS_COARSE_LOCATION", "ACCESS_BACKGROUND_LOCATION", "RECORD_AUDIO", "RECEIVE_BOOT_COMPLETED", "VIBRATE", "FOREGROUND_SERVICE", "FOREGROUND_SERVICE_LOCATION", "WAKE_LOCK", "android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_BACKGROUND_LOCATION", "android.permission.FOREGROUND_SERVICE", "android.permission.FOREGROUND_SERVICE_LOCATION", "android.permission.WAKE_LOCK"], "package": "com.abisimeon22.evelinmobile"}, "web": {"favicon": "./assets/favicon.png"}, "notification": {"icon": "./assets/notification-icon.png", "color": "#3b82f6"}, "plugins": [["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow <PERSON><PERSON> to use your location to provide location-based task reminders."}], "expo-background-task"], "extra": {"supabaseUrl": "https://cwsygtogkpqdijbkvvns.supabase.co", "supabaseAnonKey": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImN3c3lndG9na3BxZGlqYmt2dm5zIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzU3NzI0NzQsImV4cCI6MjA1MTM0ODQ3NH0.cwQSygtogkpqdijbkvvnsAnonKeyExample", "eas": {"projectId": "ab4c1164-75c2-449a-92ba-dbb3c445a5dc"}}}}