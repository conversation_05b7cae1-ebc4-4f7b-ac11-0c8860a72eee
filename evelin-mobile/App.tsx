import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  SafeAreaView,
  ActivityIndicator,
} from 'react-native';
import { StatusBar as ExpoStatusBar } from 'expo-status-bar';
import { User } from '@supabase/supabase-js';

import { Task } from './src/types/task';
import { EvelinMobileApp } from './src/components/EvelinMobileApp';
import { AuthScreen } from './src/components/AuthScreen';
import { initializeSupabase, signOutSupabase } from './src/services/supabase';
import { ReduxProvider } from './src/components/ReduxProvider';
import { SecurityUtils } from './src/utils/securityUtils';
import { TokenRotationService } from './src/services/tokenRotationService';
import { initializeGoogleMapsService } from './src/services/googleMapsService';
import { privacyService } from './src/services/privacyService';
import { apiConfigService } from './src/services/apiConfigService';

export default function App() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [securityInitialized, setSecurityInitialized] = useState(false);

  useEffect(() => {
    const initializeApp = async () => {
      try {
        // Initialize security services first
        await SecurityUtils.initializeSecurity();
        setSecurityInitialized(true);

        // Initialize Supabase with secure configuration
        const supabase = await initializeSupabase();

        // Initialize token rotation service
        await TokenRotationService.initialize();

        // Initialize API configuration service
        await apiConfigService.initialize();

        // Initialize Google Maps service
        await initializeGoogleMapsService();

        // Initialize privacy service
        // TODO: Debug privacy service initialization issue
        // await privacyService.initialize();

        // Get initial session
        const { data: { session } } = await supabase.auth.getSession();
        setUser(session?.user ?? null);

        // Listen for auth changes
        const { data: { subscription } } = supabase.auth.onAuthStateChange((_event, session) => {
          setUser(session?.user ?? null);
        });

        setLoading(false);

        // Cleanup function
        return () => {
          subscription.unsubscribe();
          TokenRotationService.cleanup();
        };
      } catch (error) {
        console.error('❌ Failed to initialize app:', error);
        setLoading(false);
      }
    };

    initializeApp();
  }, []);

  const handleSignOut = async () => {
    try {
      await signOutSupabase();
      await SecurityUtils.clearAllSensitiveData();
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const handleAuthSuccess = (user: User) => {
    setUser(user);
  };

  if (loading) {
    return (
      <ReduxProvider>
        <SafeAreaView style={styles.container}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color="#3b82f6" />
            <Text style={styles.loadingText}>
              {securityInitialized ? 'Loading Evelin...' : 'Initializing Security...'}
            </Text>
          </View>
        </SafeAreaView>
      </ReduxProvider>
    );
  }

  if (!user) {
    return (
      <ReduxProvider>
        <AuthScreen onAuthSuccess={handleAuthSuccess} />
      </ReduxProvider>
    );
  }

  return (
    <ReduxProvider>
      <EvelinMobileApp user={user} onSignOut={handleSignOut} />
    </ReduxProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 18,
    color: '#374151',
  },

});
