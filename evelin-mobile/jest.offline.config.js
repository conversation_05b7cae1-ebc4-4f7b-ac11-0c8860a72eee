module.exports = {
  setupFilesAfterEnv: ['<rootDir>/src/__tests__/setup/offline.setup.js'],
  testMatch: ['<rootDir>/src/__tests__/offline/**/*.test.{js,ts,tsx}'],
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx', 'json', 'node'],
  transform: {
    '^.+\\.(js|jsx|ts|tsx)$': ['babel-jest', { configFile: './babel.config.js' }],
  },
  transformIgnorePatterns: [
    'node_modules/(?!(react-native|@react-native|@react-navigation|react-native-vector-icons|@shopify/flash-list|expo|@expo)/)',
  ],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/src/$1',
  },
  collectCoverageFrom: [
    'src/services/offline*.{js,ts}',
    'src/services/conflictResolution*.{js,ts}',
    'src/services/dataVersioning*.{js,ts}',
    'src/store/api/*.{js,ts}',
    '!src/**/*.d.ts',
    '!src/**/*.test.{js,ts}',
  ],
  coverageDirectory: 'coverage/offline',
  coverageReporters: ['text', 'lcov', 'html'],
  testEnvironment: 'node',
  verbose: true,
  testTimeout: 30000, // 30 seconds for complex offline operations
};
