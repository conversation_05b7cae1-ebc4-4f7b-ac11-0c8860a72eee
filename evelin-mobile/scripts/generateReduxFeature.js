#!/usr/bin/env node

/**
 * Redux Feature Generator
 * 
 * Generates a complete Redux feature with consistent structure:
 * - types.ts (TypeScript interfaces and types)
 * - actions.ts (async thunks and action creators)
 * - slice.ts (Redux slice with reducers)
 * - selectors.ts (memoized selectors)
 * - index.ts (feature exports)
 * 
 * Usage: node scripts/generateReduxFeature.js <featureName>
 * Example: node scripts/generateReduxFeature.js notifications
 */

const fs = require('fs');
const path = require('path');

// Get feature name from command line arguments
const featureName = process.argv[2];

if (!featureName) {
  console.error('❌ Please provide a feature name');
  console.log('Usage: node scripts/generateReduxFeature.js <featureName>');
  process.exit(1);
}

// Capitalize first letter for types and interfaces
const capitalizedName = featureName.charAt(0).toUpperCase() + featureName.slice(1);
const upperCaseName = featureName.toUpperCase();

// Create feature directory
const featureDir = path.join(__dirname, '..', 'src', 'store', 'features', featureName);

if (!fs.existsSync(featureDir)) {
  fs.mkdirSync(featureDir, { recursive: true });
}

// Template for types file
const typesTemplate = `// ${capitalizedName} types and interfaces

export interface ${capitalizedName}State {
  loading: boolean;
  error: string | null;
  // Add your state properties here
}

// Action payload types
export interface Add${capitalizedName}Payload {
  // Define payload structure
}

export interface Update${capitalizedName}Payload {
  id: string;
  // Define update payload structure
}

export interface Delete${capitalizedName}Payload {
  id: string;
}

// API response types
export interface ${capitalizedName}ApiResponse {
  // Define API response structure
}

// Export/import types
export interface ${capitalizedName}ExportData {
  exportDate: string;
  version: string;
  // Add export data structure
}

export interface ${capitalizedName}ImportResult {
  imported: number;
  skipped: number;
  errors: string[];
}
`;

// Template for actions file
const actionsTemplate = `import { createAsyncThunk } from '@reduxjs/toolkit';
import { 
  Add${capitalizedName}Payload,
  Update${capitalizedName}Payload,
  Delete${capitalizedName}Payload,
  ${capitalizedName}ApiResponse
} from './${featureName}Types';

// Async thunks for ${featureName} operations
export const add${capitalizedName}Async = createAsyncThunk(
  '${featureName}/add${capitalizedName}Async',
  async (payload: Add${capitalizedName}Payload): Promise<${capitalizedName}ApiResponse> => {
    // Implement your async logic here
    throw new Error('Not implemented');
  }
);

export const update${capitalizedName}Async = createAsyncThunk(
  '${featureName}/update${capitalizedName}Async',
  async (payload: Update${capitalizedName}Payload): Promise<${capitalizedName}ApiResponse> => {
    // Implement your async logic here
    throw new Error('Not implemented');
  }
);

export const delete${capitalizedName}Async = createAsyncThunk(
  '${featureName}/delete${capitalizedName}Async',
  async (payload: Delete${capitalizedName}Payload): Promise<string> => {
    // Implement your async logic here
    throw new Error('Not implemented');
  }
);

export const fetch${capitalizedName}Async = createAsyncThunk(
  '${featureName}/fetch${capitalizedName}Async',
  async (): Promise<${capitalizedName}ApiResponse[]> => {
    // Implement your async logic here
    throw new Error('Not implemented');
  }
);

// Export all action types for use in reducers
export const ${featureName}ActionTypes = {
  add${capitalizedName}Async,
  update${capitalizedName}Async,
  delete${capitalizedName}Async,
  fetch${capitalizedName}Async,
} as const;
`;

// Template for slice file
const sliceTemplate = `import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { ${capitalizedName}State } from './${featureName}Types';
import {
  add${capitalizedName}Async,
  update${capitalizedName}Async,
  delete${capitalizedName}Async,
  fetch${capitalizedName}Async,
} from './${featureName}Actions';

// Initial state
const initialState: ${capitalizedName}State = {
  loading: false,
  error: null,
  // Initialize your state properties here
};

// Create the slice
const ${featureName}Slice = createSlice({
  name: '${featureName}',
  initialState,
  reducers: {
    // Synchronous actions
    clear${capitalizedName}Error: (state) => {
      state.error = null;
    },
    reset${capitalizedName}State: (state) => {
      return initialState;
    },
    // Add more synchronous actions as needed
  },
  extraReducers: (builder) => {
    // Add ${capitalizedName}
    builder
      .addCase(add${capitalizedName}Async.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(add${capitalizedName}Async.fulfilled, (state, action) => {
        state.loading = false;
        // Handle successful addition
      })
      .addCase(add${capitalizedName}Async.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to add ${featureName}';
      });

    // Update ${capitalizedName}
    builder
      .addCase(update${capitalizedName}Async.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(update${capitalizedName}Async.fulfilled, (state, action) => {
        state.loading = false;
        // Handle successful update
      })
      .addCase(update${capitalizedName}Async.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to update ${featureName}';
      });

    // Delete ${capitalizedName}
    builder
      .addCase(delete${capitalizedName}Async.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(delete${capitalizedName}Async.fulfilled, (state, action) => {
        state.loading = false;
        // Handle successful deletion
      })
      .addCase(delete${capitalizedName}Async.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to delete ${featureName}';
      });

    // Fetch ${capitalizedName}
    builder
      .addCase(fetch${capitalizedName}Async.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetch${capitalizedName}Async.fulfilled, (state, action) => {
        state.loading = false;
        // Handle successful fetch
      })
      .addCase(fetch${capitalizedName}Async.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch ${featureName}';
      });
  },
});

// Export actions
export const {
  clear${capitalizedName}Error,
  reset${capitalizedName}State,
} = ${featureName}Slice.actions;

// Export reducer
export default ${featureName}Slice.reducer;

// Export slice for store configuration
export { ${featureName}Slice };
`;

// Template for selectors file
const selectorsTemplate = `import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../../index';

// Base selectors
export const select${capitalizedName}State = (state: RootState) => state.${featureName};
export const select${capitalizedName}Loading = (state: RootState) => state.${featureName}.loading;
export const select${capitalizedName}Error = (state: RootState) => state.${featureName}.error;

// Memoized selectors
export const selectHas${capitalizedName}Error = createSelector(
  [select${capitalizedName}Error],
  (error) => !!error
);

export const selectIs${capitalizedName}Loading = createSelector(
  [select${capitalizedName}Loading],
  (loading) => loading
);

// Add more selectors as needed based on your state structure
`;

// Template for index file
const indexTemplate = `// Export all ${featureName}-related functionality from a single entry point

// Types
export type {
  ${capitalizedName}State,
  Add${capitalizedName}Payload,
  Update${capitalizedName}Payload,
  Delete${capitalizedName}Payload,
  ${capitalizedName}ApiResponse,
  ${capitalizedName}ExportData,
  ${capitalizedName}ImportResult,
} from './${featureName}Types';

// Actions (async thunks)
export {
  add${capitalizedName}Async,
  update${capitalizedName}Async,
  delete${capitalizedName}Async,
  fetch${capitalizedName}Async,
  ${featureName}ActionTypes,
} from './${featureName}Actions';

// Slice and synchronous actions
export {
  default as ${featureName}Reducer,
  ${featureName}Slice,
  clear${capitalizedName}Error,
  reset${capitalizedName}State,
} from './${featureName}Slice';

// Selectors
export {
  select${capitalizedName}State,
  select${capitalizedName}Loading,
  select${capitalizedName}Error,
  selectHas${capitalizedName}Error,
  selectIs${capitalizedName}Loading,
} from './${featureName}Selectors';

// Feature name constant
export const ${upperCaseName}_FEATURE_KEY = '${featureName}' as const;
`;

// Write all files
const files = [
  { name: `${featureName}Types.ts`, content: typesTemplate },
  { name: `${featureName}Actions.ts`, content: actionsTemplate },
  { name: `${featureName}Slice.ts`, content: sliceTemplate },
  { name: `${featureName}Selectors.ts`, content: selectorsTemplate },
  { name: 'index.ts', content: indexTemplate },
];

files.forEach(file => {
  const filePath = path.join(featureDir, file.name);
  fs.writeFileSync(filePath, file.content);
  console.log(`✅ Created ${file.name}`);
});

console.log(`\n🎉 Redux feature '${featureName}' generated successfully!`);
console.log(`📁 Location: ${featureDir}`);
console.log(`\n📝 Next steps:`);
console.log(`1. Implement the actual logic in ${featureName}Actions.ts`);
console.log(`2. Define your state structure in ${featureName}Types.ts`);
console.log(`3. Add the reducer to your store configuration`);
console.log(`4. Create selectors for your specific use cases`);
