#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

/**
 * Bundle Analysis Script for React Native App
 * Analyzes bundle size, lazy loading effectiveness, and performance metrics
 */

class BundleAnalyzer {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.srcDir = path.join(this.projectRoot, 'src');
    this.componentsDir = path.join(this.srcDir, 'components');
    this.results = {
      totalFiles: 0,
      lazyLoadedFiles: 0,
      bundleSize: 0,
      componentSizes: [],
      recommendations: [],
    };
  }

  async analyze() {
    console.log('🔍 Starting Bundle Analysis...\n');
    
    try {
      await this.analyzeFileStructure();
      await this.analyzeLazyLoading();
      await this.generateRecommendations();
      await this.generateReport();
    } catch (error) {
      console.error('❌ Analysis failed:', error.message);
      process.exit(1);
    }
  }

  async analyzeFileStructure() {
    console.log('📁 Analyzing file structure...');
    
    const files = this.getAllFiles(this.srcDir, ['.tsx', '.ts']);
    this.results.totalFiles = files.length;
    
    // Analyze component sizes
    for (const file of files) {
      const stats = fs.statSync(file);
      const relativePath = path.relative(this.srcDir, file);
      const size = stats.size;
      
      this.results.componentSizes.push({
        file: relativePath,
        size: size,
        sizeKB: Math.round(size / 1024 * 100) / 100,
      });
    }
    
    // Sort by size
    this.results.componentSizes.sort((a, b) => b.size - a.size);
    
    console.log(`   ✅ Found ${files.length} TypeScript files`);
  }

  async analyzeLazyLoading() {
    console.log('⚡ Analyzing lazy loading implementation...');
    
    const lazyScreensFile = path.join(this.componentsDir, 'lazy', 'LazyScreens.tsx');
    
    if (fs.existsSync(lazyScreensFile)) {
      const content = fs.readFileSync(lazyScreensFile, 'utf8');
      
      // Count lazy-loaded components
      const lazyComponents = content.match(/export const Lazy\w+/g) || [];
      this.results.lazyLoadedFiles = lazyComponents.length;
      
      console.log(`   ✅ Found ${lazyComponents.length} lazy-loaded components`);
      
      // Check for preloading strategies
      const hasPreloading = content.includes('preloadCriticalScreens');
      if (hasPreloading) {
        console.log('   ✅ Preloading strategies implemented');
      } else {
        this.results.recommendations.push({
          type: 'performance',
          message: 'Consider implementing preloading strategies for critical components',
        });
      }
    } else {
      console.log('   ⚠️  Lazy loading not implemented');
      this.results.recommendations.push({
        type: 'critical',
        message: 'Implement lazy loading for better performance',
      });
    }
  }

  async generateRecommendations() {
    console.log('💡 Generating recommendations...');
    
    // Large file recommendations
    const largeFiles = this.results.componentSizes.filter(f => f.sizeKB > 10);
    if (largeFiles.length > 0) {
      this.results.recommendations.push({
        type: 'optimization',
        message: `Consider splitting large files: ${largeFiles.map(f => f.file).join(', ')}`,
      });
    }
    
    // Lazy loading coverage
    const lazyLoadingCoverage = (this.results.lazyLoadedFiles / this.results.totalFiles) * 100;
    if (lazyLoadingCoverage < 30) {
      this.results.recommendations.push({
        type: 'performance',
        message: `Low lazy loading coverage (${Math.round(lazyLoadingCoverage)}%). Consider lazy loading more components.`,
      });
    }
    
    // Check for performance monitoring
    const perfMonitorFile = path.join(this.componentsDir, 'PerformanceMonitor.tsx');
    if (!fs.existsSync(perfMonitorFile)) {
      this.results.recommendations.push({
        type: 'monitoring',
        message: 'Consider adding performance monitoring for production insights',
      });
    }
    
    console.log(`   ✅ Generated ${this.results.recommendations.length} recommendations`);
  }

  async generateReport() {
    console.log('\n📊 Bundle Analysis Report');
    console.log('=' .repeat(50));
    
    // File Statistics
    console.log('\n📁 File Statistics:');
    console.log(`   Total Files: ${this.results.totalFiles}`);
    console.log(`   Lazy Loaded: ${this.results.lazyLoadedFiles}`);
    console.log(`   Coverage: ${Math.round((this.results.lazyLoadedFiles / this.results.totalFiles) * 100)}%`);
    
    // Largest Files
    console.log('\n📏 Largest Files:');
    this.results.componentSizes.slice(0, 10).forEach((file, index) => {
      const icon = file.sizeKB > 20 ? '🔴' : file.sizeKB > 10 ? '🟡' : '🟢';
      console.log(`   ${index + 1}. ${icon} ${file.file} (${file.sizeKB} KB)`);
    });
    
    // Recommendations
    if (this.results.recommendations.length > 0) {
      console.log('\n💡 Recommendations:');
      this.results.recommendations.forEach((rec, index) => {
        const icon = rec.type === 'critical' ? '🔴' : rec.type === 'performance' ? '🟡' : '🔵';
        console.log(`   ${index + 1}. ${icon} ${rec.message}`);
      });
    } else {
      console.log('\n✅ No recommendations - bundle is well optimized!');
    }
    
    // Performance Tips
    console.log('\n🚀 Performance Tips:');
    console.log('   • Use lazy loading for screens not immediately needed');
    console.log('   • Implement component preloading for better UX');
    console.log('   • Monitor bundle size regularly');
    console.log('   • Consider code splitting for large utilities');
    console.log('   • Use React.memo for expensive components');
    
    // Save detailed report
    const reportPath = path.join(this.projectRoot, 'bundle-analysis-report.json');
    fs.writeFileSync(reportPath, JSON.stringify(this.results, null, 2));
    console.log(`\n📄 Detailed report saved to: ${reportPath}`);
  }

  getAllFiles(dir, extensions) {
    let files = [];
    
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
      const fullPath = path.join(dir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip node_modules and other irrelevant directories
        if (!['node_modules', '.git', '.expo', 'dist', 'build'].includes(item)) {
          files = files.concat(this.getAllFiles(fullPath, extensions));
        }
      } else if (extensions.some(ext => item.endsWith(ext))) {
        files.push(fullPath);
      }
    }
    
    return files;
  }
}

// Performance benchmarking
class PerformanceBenchmark {
  static async measureStartupTime() {
    console.log('\n⏱️  Measuring startup performance...');
    
    try {
      // This would typically measure actual app startup time
      // For now, we'll simulate the measurement
      const startTime = Date.now();
      
      // Simulate app initialization
      await new Promise(resolve => setTimeout(resolve, 100));
      
      const endTime = Date.now();
      const startupTime = endTime - startTime;
      
      console.log(`   Simulated startup time: ${startupTime}ms`);
      
      if (startupTime > 3000) {
        console.log('   🔴 Startup time is slow - consider more aggressive lazy loading');
      } else if (startupTime > 1500) {
        console.log('   🟡 Startup time is moderate - room for improvement');
      } else {
        console.log('   🟢 Startup time is good');
      }
      
      return startupTime;
    } catch (error) {
      console.log('   ⚠️  Could not measure startup time:', error.message);
      return null;
    }
  }
  
  static generateOptimizationSuggestions() {
    console.log('\n🎯 Optimization Suggestions:');
    console.log('   1. Implement React.lazy() for all non-critical screens');
    console.log('   2. Use Suspense boundaries with meaningful loading states');
    console.log('   3. Preload components based on user navigation patterns');
    console.log('   4. Consider using React.memo for expensive re-renders');
    console.log('   5. Implement virtual scrolling for large lists');
    console.log('   6. Use image lazy loading and optimization');
    console.log('   7. Monitor bundle size with each deployment');
  }
}

// Main execution
async function main() {
  const analyzer = new BundleAnalyzer();
  await analyzer.analyze();
  
  await PerformanceBenchmark.measureStartupTime();
  PerformanceBenchmark.generateOptimizationSuggestions();
  
  console.log('\n🎉 Bundle analysis complete!');
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { BundleAnalyzer, PerformanceBenchmark };
