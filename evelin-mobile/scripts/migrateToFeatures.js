#!/usr/bin/env node

/**
 * Redux Feature Migration Script
 * 
 * This script helps migrate from slice-based to feature-based Redux architecture.
 * It updates import statements throughout the codebase to use the new feature structure.
 * 
 * Usage: node scripts/migrateToFeatures.js
 */

const fs = require('fs');
const path = require('path');

// Migration mappings from old slice imports to new feature imports
const MIGRATION_MAPPINGS = {
  // Tasks
  "from './slices/tasksSlice'": "from './features/tasks'",
  "from '../slices/tasksSlice'": "from '../features/tasks'",
  "from '../../slices/tasksSlice'": "from '../../features/tasks'",
  "from '../../../slices/tasksSlice'": "from '../../../features/tasks'",
  
  // User
  "from './slices/userSlice'": "from './features/user'",
  "from '../slices/userSlice'": "from '../features/user'",
  "from '../../slices/userSlice'": "from '../../features/user'",
  "from '../../../slices/userSlice'": "from '../../../features/user'",
  
  // Location
  "from './slices/locationSlice'": "from './features/location'",
  "from '../slices/locationSlice'": "from '../features/location'",
  "from '../../slices/locationSlice'": "from '../../features/location'",
  "from '../../../slices/locationSlice'": "from '../../../features/location'",
  
  // Notifications
  "from './slices/notificationsSlice'": "from './features/notifications'",
  "from '../slices/notificationsSlice'": "from '../features/notifications'",
  "from '../../slices/notificationsSlice'": "from '../../features/notifications'",
  "from '../../../slices/notificationsSlice'": "from '../../../features/notifications'",
  
  // Settings
  "from './slices/settingsSlice'": "from './features/settings'",
  "from '../slices/settingsSlice'": "from '../features/settings'",
  "from '../../slices/settingsSlice'": "from '../../features/settings'",
  "from '../../../slices/settingsSlice'": "from '../../../features/settings'",
  
  // UI
  "from './slices/uiSlice'": "from './features/ui'",
  "from '../slices/uiSlice'": "from '../features/ui'",
  "from '../../slices/uiSlice'": "from '../../features/ui'",
  "from '../../../slices/uiSlice'": "from '../../../features/ui'",
  
  // Network
  "from './slices/networkSlice'": "from './features/network'",
  "from '../slices/networkSlice'": "from '../features/network'",
  "from '../../slices/networkSlice'": "from '../../features/network'",
  "from '../../../slices/networkSlice'": "from '../../../features/network'",
};

// File extensions to process
const FILE_EXTENSIONS = ['.ts', '.tsx', '.js', '.jsx'];

// Directories to exclude from processing
const EXCLUDE_DIRS = ['node_modules', '.git', 'dist', 'build', '.expo'];

/**
 * Recursively find all files with specified extensions
 */
function findFiles(dir, extensions = FILE_EXTENSIONS) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        // Skip excluded directories
        if (!EXCLUDE_DIRS.includes(item)) {
          traverse(fullPath);
        }
      } else if (stat.isFile()) {
        // Check if file has one of the target extensions
        const ext = path.extname(item);
        if (extensions.includes(ext)) {
          files.push(fullPath);
        }
      }
    }
  }
  
  traverse(dir);
  return files;
}

/**
 * Update import statements in a file
 */
function updateImportsInFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    
    // Apply each migration mapping
    for (const [oldImport, newImport] of Object.entries(MIGRATION_MAPPINGS)) {
      if (content.includes(oldImport)) {
        content = content.replace(new RegExp(oldImport.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), newImport);
        modified = true;
      }
    }
    
    // Write back if modified
    if (modified) {
      fs.writeFileSync(filePath, content, 'utf8');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error processing file ${filePath}:`, error.message);
    return false;
  }
}

/**
 * Main migration function
 */
function runMigration() {
  console.log('🚀 Starting Redux feature migration...\n');
  
  const srcDir = path.join(__dirname, '..', 'src');
  
  if (!fs.existsSync(srcDir)) {
    console.error('❌ Source directory not found:', srcDir);
    process.exit(1);
  }
  
  // Find all TypeScript/JavaScript files
  console.log('📁 Scanning for files...');
  const files = findFiles(srcDir);
  console.log(`Found ${files.length} files to process\n`);
  
  let processedCount = 0;
  let modifiedCount = 0;
  
  // Process each file
  for (const file of files) {
    const relativePath = path.relative(srcDir, file);
    
    // Skip files in the features directory (already migrated)
    if (relativePath.startsWith('store/features/')) {
      continue;
    }
    
    const wasModified = updateImportsInFile(file);
    processedCount++;
    
    if (wasModified) {
      modifiedCount++;
      console.log(`✅ Updated: ${relativePath}`);
    }
  }
  
  console.log(`\n📊 Migration Summary:`);
  console.log(`   Files processed: ${processedCount}`);
  console.log(`   Files modified: ${modifiedCount}`);
  console.log(`   Files unchanged: ${processedCount - modifiedCount}`);
  
  if (modifiedCount > 0) {
    console.log(`\n🎉 Migration completed successfully!`);
    console.log(`\n📝 Next steps:`);
    console.log(`1. Update your store configuration to use feature reducers`);
    console.log(`2. Test your application to ensure everything works`);
    console.log(`3. Remove old slice files when ready`);
    console.log(`4. Update any remaining manual imports`);
  } else {
    console.log(`\n✨ No files needed migration - you're all set!`);
  }
}

// Run the migration
if (require.main === module) {
  runMigration();
}

module.exports = { runMigration, updateImportsInFile, findFiles };
