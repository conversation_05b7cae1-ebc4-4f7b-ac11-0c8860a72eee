{"totalFiles": 36, "lazyLoadedFiles": 12, "bundleSize": 0, "componentSizes": [{"file": "components/EvelinMobileApp.tsx", "size": 18979, "sizeKB": 18.53}, {"file": "components/LocationSettingsScreen.tsx", "size": 14816, "sizeKB": 14.47}, {"file": "components/SettingsScreen.tsx", "size": 13244, "sizeKB": 12.93}, {"file": "components/AccountScreen.tsx", "size": 12258, "sizeKB": 11.97}, {"file": "services/backgroundLocationService.ts", "size": 12005, "sizeKB": 11.72}, {"file": "services/googleMapsService.ts", "size": 11530, "sizeKB": 11.26}, {"file": "services/intelligentLocationService.ts", "size": 11177, "sizeKB": 10.92}, {"file": "components/TaskEditModal.tsx", "size": 10671, "sizeKB": 10.42}, {"file": "components/UserProfileScreen.tsx", "size": 10151, "sizeKB": 9.91}, {"file": "services/appSettingsService.ts", "size": 9988, "sizeKB": 9.75}, {"file": "components/PerformanceMonitor.tsx", "size": 9911, "sizeKB": 9.68}, {"file": "components/WelcomeScreen.tsx", "size": 9616, "sizeKB": 9.39}, {"file": "components/AuthScreen.tsx", "size": 9340, "sizeKB": 9.12}, {"file": "components/FuturisticVoiceButton.tsx", "size": 9087, "sizeKB": 8.87}, {"file": "components/AboutScreen.tsx", "size": 7780, "sizeKB": 7.6}, {"file": "components/HamburgerMenu.tsx", "size": 7150, "sizeKB": 6.98}, {"file": "utils/taskParser.ts", "size": 6826, "sizeKB": 6.67}, {"file": "components/TaskCard.tsx", "size": 6532, "sizeKB": 6.38}, {"file": "components/lazy/LazyScreens.tsx", "size": 6222, "sizeKB": 6.08}, {"file": "services/locationService.ts", "size": 5952, "sizeKB": 5.81}, {"file": "utils/lazyLoader.tsx", "size": 5947, "sizeKB": 5.81}, {"file": "components/VersionScreen.tsx", "size": 5760, "sizeKB": 5.63}, {"file": "components/LocationPermission.tsx", "size": 5037, "sizeKB": 4.92}, {"file": "components/TaskList.tsx", "size": 4990, "sizeKB": 4.87}, {"file": "components/VoiceButton.tsx", "size": 4761, "sizeKB": 4.65}, {"file": "hooks/useTasks.ts", "size": 4665, "sizeKB": 4.56}, {"file": "components/ManualTaskInput.tsx", "size": 4560, "sizeKB": 4.45}, {"file": "components/DistanceSettings.tsx", "size": 4532, "sizeKB": 4.43}, {"file": "services/notificationService.ts", "size": 3983, "sizeKB": 3.89}, {"file": "services/taskService.ts", "size": 3949, "sizeKB": 3.86}, {"file": "services/voiceService.ts", "size": 3122, "sizeKB": 3.05}, {"file": "hooks/useGeolocation.ts", "size": 2900, "sizeKB": 2.83}, {"file": "components/TaskHistoryScreen.tsx", "size": 2371, "sizeKB": 2.32}, {"file": "hooks/useVoiceRecognition.ts", "size": 2204, "sizeKB": 2.15}, {"file": "types/task.ts", "size": 672, "sizeKB": 0.66}, {"file": "services/supabase.ts", "size": 614, "sizeKB": 0.6}], "recommendations": [{"type": "optimization", "message": "Consider splitting large files: components/EvelinMobileApp.tsx, components/LocationSettingsScreen.tsx, components/SettingsScreen.tsx, components/AccountScreen.tsx, services/backgroundLocationService.ts, services/googleMapsService.ts, services/intelligentLocationService.ts, components/TaskEditModal.tsx"}]}