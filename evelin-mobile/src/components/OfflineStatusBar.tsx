import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  Alert,
} from 'react-native';
import { useSelector, useDispatch } from 'react-redux';
import { RootState, AppDispatch } from '../store';
import { 
  selectIsOnline, 
  selectIsOffline, 
  selectConnectionQuality,
  selectPendingOperations,
  selectFailedOperations,
} from '../store/slices/networkSlice';
import { offlineManager } from '../services/offlineManager';

interface OfflineStatusBarProps {
  onPress?: () => void;
  showDetails?: boolean;
}

export const OfflineStatusBar: React.FC<OfflineStatusBarProps> = ({
  onPress,
  showDetails = false,
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const isOnline = useSelector(selectIsOnline);
  const isOffline = useSelector(selectIsOffline);
  const connectionQuality = useSelector(selectConnectionQuality);
  const pendingOperations = useSelector(selectPendingOperations);
  const failedOperations = useSelector(selectFailedOperations);
  
  const [slideAnim] = useState(new Animated.Value(-100));
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Show/hide based on connection status or pending operations
    const shouldShow = isOffline || pendingOperations > 0 || failedOperations > 0;
    
    if (shouldShow !== isVisible) {
      setIsVisible(shouldShow);
      
      Animated.timing(slideAnim, {
        toValue: shouldShow ? 0 : -100,
        duration: 300,
        useNativeDriver: true,
      }).start();
    }
  }, [isOffline, pendingOperations, failedOperations, isVisible, slideAnim]);

  const getStatusInfo = () => {
    if (isOffline) {
      return {
        text: 'Offline Mode',
        subtext: pendingOperations > 0 ? `${pendingOperations} pending` : 'No connection',
        color: '#ef4444',
        icon: '📵',
      };
    }
    
    if (failedOperations > 0) {
      return {
        text: 'Sync Issues',
        subtext: `${failedOperations} failed operations`,
        color: '#f59e0b',
        icon: '⚠️',
      };
    }
    
    if (pendingOperations > 0) {
      return {
        text: 'Syncing...',
        subtext: `${pendingOperations} operations`,
        color: '#3b82f6',
        icon: '🔄',
      };
    }
    
    // Connection quality indicator
    if (connectionQuality === 'poor') {
      return {
        text: 'Poor Connection',
        subtext: 'Limited functionality',
        color: '#f59e0b',
        icon: '📶',
      };
    }
    
    return null;
  };

  const handlePress = async () => {
    if (onPress) {
      onPress();
      return;
    }

    // Default press behavior
    if (isOffline) {
      Alert.alert(
        'Offline Mode',
        'You are currently offline. Changes will be synced when connection is restored.',
        [{ text: 'OK' }]
      );
    } else if (failedOperations > 0) {
      Alert.alert(
        'Sync Issues',
        `${failedOperations} operations failed to sync. Would you like to retry?`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Retry',
            onPress: async () => {
              try {
                await offlineManager.retryFailedOperations();
              } catch (error) {
                Alert.alert('Error', 'Failed to retry operations');
              }
            },
          },
        ]
      );
    } else if (pendingOperations > 0) {
      Alert.alert(
        'Syncing',
        `${pendingOperations} operations are being synced with the server.`,
        [{ text: 'OK' }]
      );
    }
  };

  const statusInfo = getStatusInfo();
  
  if (!statusInfo) {
    return null;
  }

  return (
    <Animated.View
      style={[
        styles.container,
        { 
          backgroundColor: statusInfo.color,
          transform: [{ translateY: slideAnim }],
        },
      ]}
    >
      <TouchableOpacity
        style={styles.touchable}
        onPress={handlePress}
        activeOpacity={0.8}
      >
        <View style={styles.content}>
          <Text style={styles.icon}>{statusInfo.icon}</Text>
          <View style={styles.textContainer}>
            <Text style={styles.mainText}>{statusInfo.text}</Text>
            {showDetails && (
              <Text style={styles.subText}>{statusInfo.subtext}</Text>
            )}
          </View>
          {(pendingOperations > 0 || failedOperations > 0) && (
            <View style={styles.badge}>
              <Text style={styles.badgeText}>
                {pendingOperations + failedOperations}
              </Text>
            </View>
          )}
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  touchable: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  icon: {
    fontSize: 16,
    marginRight: 8,
  },
  textContainer: {
    flex: 1,
    alignItems: 'center',
  },
  mainText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
  subText: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 12,
    textAlign: 'center',
    marginTop: 2,
  },
  badge: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 12,
    minWidth: 24,
    height: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: 8,
  },
  badgeText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
});

export default OfflineStatusBar;
