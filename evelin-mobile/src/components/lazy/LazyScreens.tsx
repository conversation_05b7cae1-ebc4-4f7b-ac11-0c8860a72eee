import React from 'react';
import { createLazyScreen } from '../../utils/lazyLoader';

// Lazy-loaded screen components with custom loading messages
export const LazySettingsScreen = createLazyScreen(
  () => import('../SettingsScreen').then(module => ({ default: module.SettingsScreen })) as any,
  'Loading Settings...',
  true // Preload this critical component
);

export const LazyTaskHistoryScreen = createLazyScreen(
  () => import('../TaskHistoryScreen').then(module => ({ default: module.TaskHistoryScreen })) as any,
  'Loading Task History...',
  true // Preload this critical component
);

export const LazyAccountScreen = createLazyScreen(
  () => import('../AccountScreen').then(module => ({ default: module.AccountScreen })) as any,
  'Loading Account...'
);

export const LazyVersionScreen = createLazyScreen(
  () => import('../VersionScreen').then(module => ({ default: module.VersionScreen })) as any,
  'Loading Version Info...'
);

export const LazyAboutScreen = createLazyScreen(
  () => import('../AboutScreen').then(module => ({ default: module.AboutScreen })) as any,
  'Loading About...'
);

export const LazyLocationSettingsScreen = createLazyScreen(
  () => import('../LocationSettingsScreen').then(module => ({ default: module.default })) as any,
  'Loading Location Settings...'
);

export const LazyUserProfileScreen = createLazyScreen(
  () => import('../UserProfileScreen').then(module => ({ default: module.default })) as any,
  'Loading Profile...'
);

export const LazyWelcomeScreen = createLazyScreen(
  () => import('../WelcomeScreen').then(module => ({ default: module.default })) as any,
  'Preparing Welcome...',
  true // Preload for first-time users
);

// Bundle splitting for heavy components (when they exist)
// export const LazyMapComponent = createLazyScreen(
//   () => import('../MapComponent'),
//   'Loading Map...'
// );

// Utility components that can be lazy-loaded
export const LazyHamburgerMenu = createLazyScreen(
  () => import('../HamburgerMenu').then(module => ({ default: module.HamburgerMenu })) as any,
  'Loading Menu...',
  true // Preload for better UX
);

// Advanced lazy loading with dynamic imports based on conditions
export const createConditionalLazyScreen = (
  condition: () => boolean,
  trueImport: () => Promise<any>,
  falseImport: () => Promise<any>,
  loadingMessage?: string
) => {
  return createLazyScreen(
    () => condition() ? trueImport() : falseImport(),
    loadingMessage
  );
};

// Screen preloading strategies
export const ScreenPreloadStrategies = {
  // Preload screens that are likely to be accessed soon
  preloadCriticalScreens: async () => {
    const criticalScreens = [
      () => import('../SettingsScreen'),
      () => import('../TaskHistoryScreen'),
      () => import('../HamburgerMenu'),
    ];
    
    await Promise.allSettled(criticalScreens.map(loader => loader()));
    console.log('✅ Critical screens preloaded');
  },
  
  // Preload screens based on user behavior
  preloadBasedOnUsage: async (userPreferences: string[]) => {
    const screenMap: { [key: string]: () => Promise<any> } = {
      'settings': () => import('../SettingsScreen'),
      'account': () => import('../AccountScreen'),
      'history': () => import('../TaskHistoryScreen'),
      'about': () => import('../AboutScreen'),
      'version': () => import('../VersionScreen'),
    };
    
    const preloadPromises = userPreferences
      .filter(pref => screenMap[pref])
      .map(pref => screenMap[pref]());
    
    await Promise.allSettled(preloadPromises);
    console.log('✅ User preference screens preloaded');
  },
  
  // Preload screens during idle time
  preloadDuringIdle: () => {
    if ('requestIdleCallback' in window) {
      (window as any).requestIdleCallback(() => {
        import('../AboutScreen');
        import('../VersionScreen');
      });
    } else {
      // Fallback for environments without requestIdleCallback
      setTimeout(() => {
        import('../AboutScreen');
        import('../VersionScreen');
      }, 2000);
    }
  },
};

// Memory management for lazy components
export class LazyComponentManager {
  private static loadedComponents = new Map<string, any>();
  private static componentUsage = new Map<string, number>();
  
  static trackComponentUsage(componentName: string) {
    const currentUsage = this.componentUsage.get(componentName) || 0;
    this.componentUsage.set(componentName, currentUsage + 1);
  }
  
  static getComponentUsageStats() {
    return Array.from(this.componentUsage.entries())
      .sort(([, a], [, b]) => b - a)
      .map(([name, usage]) => ({ name, usage }));
  }
  
  static clearUnusedComponents() {
    // In a real implementation, you might want to clear components
    // that haven't been used recently to free up memory
    const unusedComponents = Array.from(this.componentUsage.entries())
      .filter(([, usage]) => usage === 0)
      .map(([name]) => name);
    
    unusedComponents.forEach(name => {
      this.loadedComponents.delete(name);
      this.componentUsage.delete(name);
    });
    
    console.log(`🧹 Cleared ${unusedComponents.length} unused components`);
  }
}

// Performance monitoring for lazy loading
export const LazyLoadingMetrics = {
  loadTimes: new Map<string, number>(),
  
  startLoadTimer: (componentName: string) => {
    LazyLoadingMetrics.loadTimes.set(`${componentName}_start`, Date.now());
  },
  
  endLoadTimer: (componentName: string) => {
    const startTime = LazyLoadingMetrics.loadTimes.get(`${componentName}_start`);
    if (startTime) {
      const loadTime = Date.now() - startTime;
      LazyLoadingMetrics.loadTimes.set(componentName, loadTime);
      console.log(`⏱️ ${componentName} loaded in ${loadTime}ms`);
    }
  },
  
  getAverageLoadTime: () => {
    const loadTimes = Array.from(LazyLoadingMetrics.loadTimes.values())
      .filter(time => typeof time === 'number' && time > 0);
    
    if (loadTimes.length === 0) return 0;
    
    const total = loadTimes.reduce((sum, time) => sum + time, 0);
    return Math.round(total / loadTimes.length);
  },
  
  getLoadTimeReport: () => {
    const report: { [key: string]: number } = {};
    LazyLoadingMetrics.loadTimes.forEach((time, key) => {
      if (typeof time === 'number' && !key.includes('_start')) {
        report[key] = time;
      }
    });
    return report;
  },
};
