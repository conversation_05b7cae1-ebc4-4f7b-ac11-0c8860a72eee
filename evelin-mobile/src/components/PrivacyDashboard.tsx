/**
 * Privacy Dashboard Component
 * Overview of privacy settings and quick access to privacy controls
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { privacyService } from '../services/privacyService';
import { LocationDataSummary, LocationPrivacySettings } from '../types/privacy';

interface PrivacyDashboardProps {
  onNavigateToPrivacyControls: () => void;
  onNavigateToDataRetention: () => void;
}

export default function PrivacyDashboard({ 
  onNavigateToPrivacyControls, 
  onNavigateToDataRetention 
}: PrivacyDashboardProps) {
  const [settings, setSettings] = useState<LocationPrivacySettings | null>(null);
  const [dataSummary, setDataSummary] = useState<LocationDataSummary | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadPrivacyData();
  }, []);

  const loadPrivacyData = async () => {
    try {
      setLoading(true);
      const [privacySettings, summary] = await Promise.all([
        privacyService.getPrivacySettings(),
        privacyService.getLocationDataSummary(),
      ]);
      setSettings(privacySettings);
      setDataSummary(summary);
    } catch (error) {
      console.error('Error loading privacy data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getPrivacyScore = (): { score: number; level: string; color: string } => {
    if (!settings) return { score: 0, level: 'Unknown', color: '#666' };

    let score = 0;
    const maxScore = 10;

    // Data collection controls (3 points)
    if (!settings.locationDataCollection.enabled) score += 1;
    if (!settings.locationDataCollection.backgroundTracking) score += 1;
    if (settings.locationDataCollection.onlyWhenAppActive) score += 1;

    // Data usage controls (3 points)
    if (!settings.dataUsage.allowAnalytics) score += 1;
    if (settings.dataUsage.anonymizeData) score += 1;
    if (!settings.dataUsage.allowThirdPartyIntegration) score += 1;

    // Advanced controls (2 points)
    if (settings.advancedControls.encryptLocationData) score += 1;
    if (settings.advancedControls.enablePrivacyMode) score += 1;

    // Auto-purge settings (2 points)
    if (settings.autoPurge.enabled) score += 1;
    if (settings.dataRetention.locationHistory !== 'forever') score += 1;

    const percentage = (score / maxScore) * 100;
    
    if (percentage >= 80) return { score: percentage, level: 'Excellent', color: '#34C759' };
    if (percentage >= 60) return { score: percentage, level: 'Good', color: '#FF9500' };
    if (percentage >= 40) return { score: percentage, level: 'Fair', color: '#FF9500' };
    return { score: percentage, level: 'Needs Improvement', color: '#FF3B30' };
  };

  const formatDataSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleQuickPurge = () => {
    Alert.alert(
      'Quick Data Cleanup',
      'Delete old location data based on your retention settings?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clean Up',
          onPress: async () => {
            try {
              await privacyService.enforceRetentionPolicies();
              await loadPrivacyData(); // Refresh data
              Alert.alert('Success', 'Data cleanup completed');
            } catch (error) {
              console.error('Error during quick purge:', error);
              Alert.alert('Error', 'Failed to clean up data');
            }
          },
        },
      ]
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="small" color="#007AFF" />
        <Text style={styles.loadingText}>Loading privacy data...</Text>
      </View>
    );
  }

  const privacyScore = getPrivacyScore();

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Privacy Overview</Text>

      {/* Privacy Score */}
      <View style={styles.scoreCard}>
        <View style={styles.scoreHeader}>
          <Text style={styles.scoreTitle}>Privacy Score</Text>
          <View style={[styles.scoreBadge, { backgroundColor: privacyScore.color }]}>
            <Text style={styles.scoreText}>{Math.round(privacyScore.score)}%</Text>
          </View>
        </View>
        <Text style={[styles.scoreLevel, { color: privacyScore.color }]}>
          {privacyScore.level}
        </Text>
      </View>

      {/* Data Summary */}
      {dataSummary && (
        <View style={styles.summaryCard}>
          <Text style={styles.summaryTitle}>Your Data</Text>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Total Entries:</Text>
            <Text style={styles.summaryValue}>{dataSummary.totalEntries.toLocaleString()}</Text>
          </View>
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Storage Used:</Text>
            <Text style={styles.summaryValue}>{formatDataSize(dataSummary.storageSize)}</Text>
          </View>
          {dataSummary.oldestEntry && (
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Oldest Data:</Text>
              <Text style={styles.summaryValue}>
                {new Date(dataSummary.oldestEntry).toLocaleDateString()}
              </Text>
            </View>
          )}
        </View>
      )}

      {/* Quick Actions */}
      <View style={styles.actionsContainer}>
        <TouchableOpacity style={styles.actionButton} onPress={onNavigateToPrivacyControls}>
          <Ionicons name="shield-checkmark-outline" size={20} color="#007AFF" />
          <Text style={styles.actionText}>Privacy Controls</Text>
          <Ionicons name="chevron-forward" size={16} color="#C7C7CC" />
        </TouchableOpacity>

        <TouchableOpacity style={styles.actionButton} onPress={onNavigateToDataRetention}>
          <Ionicons name="time-outline" size={20} color="#007AFF" />
          <Text style={styles.actionText}>Data Retention</Text>
          <Ionicons name="chevron-forward" size={16} color="#C7C7CC" />
        </TouchableOpacity>

        <TouchableOpacity style={styles.actionButton} onPress={handleQuickPurge}>
          <Ionicons name="trash-outline" size={20} color="#FF9500" />
          <Text style={[styles.actionText, { color: '#FF9500' }]}>Quick Cleanup</Text>
          <Ionicons name="chevron-forward" size={16} color="#C7C7CC" />
        </TouchableOpacity>
      </View>

      {/* Privacy Tips */}
      <View style={styles.tipsCard}>
        <Text style={styles.tipsTitle}>Privacy Tips</Text>
        <View style={styles.tip}>
          <Ionicons name="bulb-outline" size={16} color="#FF9500" />
          <Text style={styles.tipText}>
            Enable auto-purge to automatically delete old location data
          </Text>
        </View>
        <View style={styles.tip}>
          <Ionicons name="bulb-outline" size={16} color="#FF9500" />
          <Text style={styles.tipText}>
            Turn on data anonymization to protect your identity
          </Text>
        </View>
        <View style={styles.tip}>
          <Ionicons name="bulb-outline" size={16} color="#FF9500" />
          <Text style={styles.tipText}>
            Disable background tracking when not needed
          </Text>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    padding: 20,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  loadingText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#666',
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 20,
  },
  scoreCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  scoreHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  scoreTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
  },
  scoreBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  scoreText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#fff',
  },
  scoreLevel: {
    fontSize: 14,
    fontWeight: '500',
  },
  summaryCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  summaryTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 6,
  },
  summaryLabel: {
    fontSize: 14,
    color: '#666',
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1a1a1a',
  },
  actionsContainer: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  actionText: {
    flex: 1,
    fontSize: 16,
    color: '#1a1a1a',
    marginLeft: 12,
  },
  tipsCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tipsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 12,
  },
  tip: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  tipText: {
    flex: 1,
    fontSize: 14,
    color: '#666',
    marginLeft: 8,
    lineHeight: 20,
  },
});
