import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import Constants from 'expo-constants';
import { backgroundLocationService } from '../services/backgroundLocationService';
import { appSettingsService } from '../services/appSettingsService';
import * as Location from 'expo-location';

interface LocationSettingsScreenProps {
  onBack: () => void;
}

interface LocationSettings {
  backgroundEnabled: boolean;
  homeLocation?: { latitude: number; longitude: number; name: string };
  workLocation?: { latitude: number; longitude: number; name: string };
  batteryOptimization: boolean;
  onlyWhenMoving: boolean;
  quietHours: { start: string; end: string; enabled: boolean };
}

export default function LocationSettingsScreen({ onBack }: LocationSettingsScreenProps) {
  const [settings, setSettings] = useState<LocationSettings>({
    backgroundEnabled: false,
    batteryOptimization: true,
    onlyWhenMoving: true,
    quietHours: { start: '22:00', end: '07:00', enabled: true },
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    try {
      const currentSettings = await backgroundLocationService.getLocationSettings();
      setSettings(currentSettings);
    } catch (error) {
      console.error('Error loading settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateSetting = async (key: keyof LocationSettings, value: any) => {
    try {
      const newSettings = { ...settings, [key]: value };
      setSettings(newSettings);
      await backgroundLocationService.updateLocationSettings({ [key]: value });
    } catch (error) {
      console.error('Error updating setting:', error);
      Alert.alert('Error', 'Failed to update setting');
    }
  };

  const checkLocationPermissions = async (): Promise<boolean> => {
    try {
      const { mobileLocationService } = await import('../services/locationService');
      const hasLocation = await mobileLocationService.hasLocationPermission();
      const hasBackground = await mobileLocationService.hasBackgroundLocationPermission();

      console.log('📍 Permission check - Location:', hasLocation, 'Background:', hasBackground);
      return hasLocation && hasBackground;
    } catch (error) {
      console.error('Error checking location permissions:', error);
      return false;
    }
  };

  const requestLocationPermissions = async (): Promise<boolean> => {
    try {
      const { mobileLocationService } = await import('../services/locationService');

      // Request foreground permission first
      const foregroundPermission = await mobileLocationService.requestLocationPermission();
      console.log('📍 Foreground permission result:', foregroundPermission);

      if (foregroundPermission === 'granted') {
        // Request background permission
        const backgroundPermission = await mobileLocationService.requestBackgroundLocationPermission();
        console.log('📍 Background permission result:', backgroundPermission);

        return backgroundPermission === 'granted';
      }

      return false;
    } catch (error) {
      console.error('Error requesting location permissions:', error);
      return false;
    }
  };

  const handleBackgroundLocationToggle = async (enabled: boolean) => {
    if (enabled) {
      Alert.alert(
        'Enable Background Tracking',
        'This allows location-based reminders when you arrive at or leave specific places.\n\n• Get notified when near your tasks\n• Works even when app is closed\n• Battery optimized tracking\n• You can disable anytime',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Enable',
            onPress: async () => {
              try {
                console.log('🔄 Attempting to enable background tracking...');

                // First check if we already have permissions
                const hasPermissions = await checkLocationPermissions();

                if (hasPermissions) {
                  // Permissions already granted, start tracking
                  const success = await backgroundLocationService.startBackgroundLocationTracking(true);
                  if (success) {
                    updateSetting('backgroundEnabled', true);
                    Alert.alert(
                      'Background Tracking Enabled! ✅',
                      'You\'ll now receive notifications when near task locations, even when the app is closed.',
                      [{ text: 'Got it!' }]
                    );
                  } else {
                    throw new Error('Failed to start background tracking');
                  }
                } else {
                  // Need to request permissions
                  const permissionGranted = await requestLocationPermissions();

                  if (permissionGranted) {
                    // Permissions granted, start tracking
                    const success = await backgroundLocationService.startBackgroundLocationTracking(true);
                    if (success) {
                      updateSetting('backgroundEnabled', true);
                      Alert.alert(
                        'Background Tracking Enabled! ✅',
                        'You\'ll now receive notifications when near task locations, even when the app is closed.',
                        [{ text: 'Got it!' }]
                      );
                    } else {
                      throw new Error('Failed to start background tracking after permission grant');
                    }
                  } else {
                    // Permissions denied, direct to settings
                    Alert.alert(
                      'Location Permission Required',
                      'To enable background tracking, please:\n\n1. Tap "Open Settings" below\n2. Find "Location" or "Permissions"\n3. Enable "Location" for this app\n4. Select "Allow all the time" for background access',
                      [
                        { text: 'Cancel', style: 'cancel' },
                        {
                          text: 'Open Settings',
                          onPress: async () => {
                            await appSettingsService.openLocationSettings();
                            // Check permissions again after user returns
                            setTimeout(async () => {
                              const hasPermissionsNow = await checkLocationPermissions();
                              if (hasPermissionsNow) {
                                // Try to enable tracking again
                                const success = await backgroundLocationService.startBackgroundLocationTracking(true);
                                if (success) {
                                  updateSetting('backgroundEnabled', true);
                                  Alert.alert(
                                    'Background Tracking Enabled! ✅',
                                    'Location permissions granted successfully. Background tracking is now active.',
                                    [{ text: 'Great!' }]
                                  );
                                }
                              }
                            }, 2000);
                          }
                        }
                      ]
                    );
                  }
                }
              } catch (error) {
                console.error('Error enabling background tracking:', error);
                Alert.alert(
                  'Error',
                  'Failed to enable background tracking. Please check your device settings and try again.',
                  [{ text: 'OK' }]
                );
              }
            },
          },
        ]
      );
    } else {
      // Disable background tracking
      Alert.alert(
        'Disable Background Tracking',
        'This will turn off location-based reminders. You won\'t get notified when near your tasks.',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Disable',
            style: 'destructive',
            onPress: async () => {
              await backgroundLocationService.stopBackgroundLocationTracking();
              updateSetting('backgroundEnabled', false);
            }
          }
        ]
      );
    }
  };

  const setHomeLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please enable location permissions to set home location.');
        return;
      }

      const location = await Location.getCurrentPositionAsync({});
      const { latitude, longitude } = location.coords;

      Alert.prompt(
        'Set Home Location',
        'Enter a name for this location:',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Save',
            onPress: (name) => {
              if (name) {
                updateSetting('homeLocation', { latitude, longitude, name });
                Alert.alert('Success', `Home location set as "${name}"`);
              }
            },
          },
        ],
        'plain-text',
        'Home'
      );
    } catch (error) {
      console.error('Error setting home location:', error);
      Alert.alert('Error', 'Failed to get current location');
    }
  };

  const setWorkLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please enable location permissions to set work location.');
        return;
      }

      const location = await Location.getCurrentPositionAsync({});
      const { latitude, longitude } = location.coords;

      Alert.prompt(
        'Set Work Location',
        'Enter a name for this location:',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Save',
            onPress: (name) => {
              if (name) {
                updateSetting('workLocation', { latitude, longitude, name });
                Alert.alert('Success', `Work location set as "${name}"`);
              }
            },
          },
        ],
        'plain-text',
        'Work'
      );
    } catch (error) {
      console.error('Error setting work location:', error);
      Alert.alert('Error', 'Failed to get current location');
    }
  };

  const updateQuietHours = (field: 'start' | 'end', value: string) => {
    const newQuietHours = { ...settings.quietHours, [field]: value };
    updateSetting('quietHours', newQuietHours);
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <Text style={styles.loadingText}>Loading settings...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={onBack} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#374151" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Location Settings</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Background Tracking */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Background Tracking</Text>
          
          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Enable Background Tracking</Text>
              <Text style={styles.settingDescription}>
                Get notifications when near task locations, even when app is closed
              </Text>
            </View>
            <Switch
              value={settings.backgroundEnabled}
              onValueChange={handleBackgroundLocationToggle}
              trackColor={{ false: '#e5e7eb', true: '#3b82f6' }}
              thumbColor={settings.backgroundEnabled ? '#ffffff' : '#f3f4f6'}
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Battery Optimization</Text>
              <Text style={styles.settingDescription}>
                Reduce battery usage with smart location tracking
              </Text>
            </View>
            <Switch
              value={settings.batteryOptimization}
              onValueChange={(value) => updateSetting('batteryOptimization', value)}
              trackColor={{ false: '#e5e7eb', true: '#3b82f6' }}
              thumbColor={settings.batteryOptimization ? '#ffffff' : '#f3f4f6'}
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Only When Moving</Text>
              <Text style={styles.settingDescription}>
                Only track location when you're moving, not when stationary
              </Text>
            </View>
            <Switch
              value={settings.onlyWhenMoving}
              onValueChange={(value) => updateSetting('onlyWhenMoving', value)}
              trackColor={{ false: '#e5e7eb', true: '#3b82f6' }}
              thumbColor={settings.onlyWhenMoving ? '#ffffff' : '#f3f4f6'}
            />
          </View>
        </View>

        {/* Home & Work Locations */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Smart Locations</Text>
          <Text style={styles.sectionDescription}>
            Set home and work locations to optimize battery usage and reduce unnecessary tracking
          </Text>

          <TouchableOpacity style={styles.locationButton} onPress={setHomeLocation}>
            <Ionicons name="home" size={20} color="#3b82f6" />
            <View style={styles.locationInfo}>
              <Text style={styles.locationLabel}>Home Location</Text>
              <Text style={styles.locationValue}>
                {settings.homeLocation ? settings.homeLocation.name : 'Not set'}
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#9ca3af" />
          </TouchableOpacity>

          <TouchableOpacity style={styles.locationButton} onPress={setWorkLocation}>
            <Ionicons name="business" size={20} color="#3b82f6" />
            <View style={styles.locationInfo}>
              <Text style={styles.locationLabel}>Work Location</Text>
              <Text style={styles.locationValue}>
                {settings.workLocation ? settings.workLocation.name : 'Not set'}
              </Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color="#9ca3af" />
          </TouchableOpacity>
        </View>

        {/* Quiet Hours */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quiet Hours</Text>
          
          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Enable Quiet Hours</Text>
              <Text style={styles.settingDescription}>
                Disable notifications during specified hours
              </Text>
            </View>
            <Switch
              value={settings.quietHours.enabled}
              onValueChange={(value) => updateSetting('quietHours', { ...settings.quietHours, enabled: value })}
              trackColor={{ false: '#e5e7eb', true: '#3b82f6' }}
              thumbColor={settings.quietHours.enabled ? '#ffffff' : '#f3f4f6'}
            />
          </View>

          {settings.quietHours.enabled && (
            <View style={styles.timeInputContainer}>
              <View style={styles.timeInput}>
                <Text style={styles.timeLabel}>Start Time</Text>
                <TextInput
                  style={styles.timeField}
                  value={settings.quietHours.start}
                  onChangeText={(value) => updateQuietHours('start', value)}
                  placeholder="22:00"
                  keyboardType="numeric"
                />
              </View>
              <View style={styles.timeInput}>
                <Text style={styles.timeLabel}>End Time</Text>
                <TextInput
                  style={styles.timeField}
                  value={settings.quietHours.end}
                  onChangeText={(value) => updateQuietHours('end', value)}
                  placeholder="07:00"
                  keyboardType="numeric"
                />
              </View>
            </View>
          )}
        </View>

        {/* Privacy Notice */}
        <View style={styles.privacySection}>
          <Ionicons name="shield-checkmark" size={24} color="#10b981" />
          <Text style={styles.privacyTitle}>Privacy & Battery</Text>
          <Text style={styles.privacyText}>
            • Location data stays on your device{'\n'}
            • Smart tracking reduces battery usage{'\n'}
            • No tracking when at home/work{'\n'}
            • Respects quiet hours{'\n'}
            • Can be disabled anytime
          </Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
    paddingTop: Constants.statusBarHeight,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  loadingText: {
    textAlign: 'center',
    marginTop: 50,
    fontSize: 16,
    color: '#6b7280',
  },
  section: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 4,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 16,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  settingInfo: {
    flex: 1,
    marginRight: 12,
  },
  settingLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111827',
    marginBottom: 2,
  },
  settingDescription: {
    fontSize: 14,
    color: '#6b7280',
  },
  locationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  locationInfo: {
    flex: 1,
    marginLeft: 12,
  },
  locationLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111827',
  },
  locationValue: {
    fontSize: 14,
    color: '#6b7280',
    marginTop: 2,
  },
  timeInputContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 12,
  },
  timeInput: {
    flex: 1,
    marginHorizontal: 4,
  },
  timeLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 4,
  },
  timeField: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 16,
    backgroundColor: '#ffffff',
  },
  privacySection: {
    backgroundColor: '#f0fdf4',
    borderRadius: 12,
    padding: 16,
    marginTop: 16,
    marginBottom: 32,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#bbf7d0',
  },
  privacyTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#065f46',
    marginTop: 8,
    marginBottom: 8,
  },
  privacyText: {
    fontSize: 14,
    color: '#047857',
    textAlign: 'center',
    lineHeight: 20,
  },
});
