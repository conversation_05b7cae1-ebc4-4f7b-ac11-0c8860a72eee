import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';

interface DistanceSettingsProps {
  defaultDistance: number;
  onDistanceChange: (distance: number) => void;
  isVisible: boolean;
  onToggle: () => void;
}

export const DistanceSettings: React.FC<DistanceSettingsProps> = ({
  defaultDistance,
  onDistanceChange,
  isVisible,
  onToggle,
}) => {
  const distanceOptions = [50, 100, 200, 500, 1000];

  return (
    <View style={styles.container}>
      <TouchableOpacity style={styles.toggleButton} onPress={onToggle}>
        <Text style={styles.toggleButtonText}>
          ⚙️ Notification Distance: {defaultDistance}m
        </Text>
        <Text style={styles.toggleIcon}>{isVisible ? '▼' : '▶'}</Text>
      </TouchableOpacity>

      {isVisible && (
        <View style={styles.settingsCard}>
          <Text style={styles.title}>Default Notification Distance</Text>
          <Text style={styles.subtitle}>
            How close you need to be to get reminders
          </Text>

          <View style={styles.optionsContainer}>
            {distanceOptions.map((distance) => (
              <TouchableOpacity
                key={distance}
                style={[
                  styles.optionButton,
                  defaultDistance === distance && styles.optionButtonActive,
                ]}
                onPress={() => onDistanceChange(distance)}
              >
                <Text
                  style={[
                    styles.optionText,
                    defaultDistance === distance && styles.optionTextActive,
                  ]}
                >
                  {distance}m
                </Text>
                <Text
                  style={[
                    styles.optionDescription,
                    defaultDistance === distance && styles.optionDescriptionActive,
                  ]}
                >
                  {distance <= 100 ? 'Very Close' : 
                   distance <= 200 ? 'Close' :
                   distance <= 500 ? 'Nearby' : 'Far'}
                </Text>
              </TouchableOpacity>
            ))}
          </View>

          <View style={styles.infoContainer}>
            <Text style={styles.infoTitle}>💡 Distance Guide:</Text>
            <Text style={styles.infoText}>
              • 50m: Same building/block{'\n'}
              • 100m: Across the street{'\n'}
              • 200m: Few blocks away{'\n'}
              • 500m: Walking distance{'\n'}
              • 1000m: Short drive/bike ride
            </Text>
          </View>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 20,
    paddingVertical: 8,
  },
  toggleButton: {
    backgroundColor: '#f3f4f6',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  toggleButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
  },
  toggleIcon: {
    fontSize: 12,
    color: '#6b7280',
  },
  settingsCard: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    marginTop: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 12,
    color: '#6b7280',
    marginBottom: 16,
  },
  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },
  optionButton: {
    backgroundColor: '#f9fafb',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    alignItems: 'center',
    minWidth: 80,
  },
  optionButtonActive: {
    backgroundColor: '#dbeafe',
    borderColor: '#3b82f6',
  },
  optionText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 2,
  },
  optionTextActive: {
    color: '#1e40af',
  },
  optionDescription: {
    fontSize: 10,
    color: '#9ca3af',
  },
  optionDescriptionActive: {
    color: '#3730a3',
  },
  infoContainer: {
    backgroundColor: '#f0f9ff',
    borderRadius: 8,
    padding: 12,
  },
  infoTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: '#1e40af',
    marginBottom: 6,
  },
  infoText: {
    fontSize: 11,
    color: '#1e40af',
    lineHeight: 16,
  },
});
