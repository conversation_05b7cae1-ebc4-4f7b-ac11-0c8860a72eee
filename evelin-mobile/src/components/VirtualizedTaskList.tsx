import React, { memo, useCallback, useMemo, useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  RefreshControl,
  ViewToken,
} from 'react-native';
import { FlashList, ListRenderItem } from '@shopify/flash-list';
import { Task } from '../types/task';
import { TaskCard } from './TaskCard';
import {
  useFilteredTasks,
  useSortedTasks,
  keyExtractor,
} from '../utils/performanceOptimizations';
import {
  TaskFlashList,
  FlashListEmptyComponent,
  FlashListFooterComponent,
  FlashListItemSeparator,
} from './FlashListComponents';

interface VirtualizedTaskListProps {
  tasks: Task[];
  onCompleteTask: (taskId: string) => void;
  onDeleteTask: (taskId: string) => void;
  onEditTask: (taskId: string, updates: Partial<Task>) => void;
  onShowOnMap?: (task: Task) => void;
  userLocation?: { latitude: number; longitude: number };
  calculateDistance?: (lat1: number, lon1: number, lat2: number, lon2: number) => number;
  showOnlyActive?: boolean;
  maxTasks?: number;
  searchQuery?: string;
  sortBy?: 'date' | 'priority' | 'distance' | 'alphabetical';
  onRefresh?: () => void;
  refreshing?: boolean;
  onLoadMore?: () => void;
  hasMore?: boolean;
  loading?: boolean;
}

export const VirtualizedTaskList: React.FC<VirtualizedTaskListProps> = memo(({
  tasks,
  onCompleteTask,
  onDeleteTask,
  onEditTask,
  onShowOnMap,
  userLocation,
  calculateDistance,
  showOnlyActive = false,
  maxTasks,
  searchQuery = '',
  sortBy = 'date',
  onRefresh,
  refreshing = false,
  onLoadMore,
  hasMore = false,
  loading = false,
}) => {
  // State for viewability tracking
  const [viewableItems, setViewableItems] = useState<string[]>([]);
  const flashListRef = useRef<any>(null);

  // Memoized filtering and sorting
  const filteredTasks = useFilteredTasks(tasks, {
    status: showOnlyActive ? 'active' : 'all',
    search: searchQuery,
    location: userLocation,
    maxDistance: 5000, // 5km radius
  });

  const sortedTasks = useSortedTasks(filteredTasks, sortBy, userLocation);

  // Apply max tasks limit if specified
  const displayTasks = useMemo(() => {
    return maxTasks ? sortedTasks.slice(0, maxTasks) : sortedTasks;
  }, [sortedTasks, maxTasks]);

  // Memoized distance calculations
  const tasksWithDistance = useMemo(() => {
    if (!userLocation || !calculateDistance) return displayTasks;
    
    return displayTasks.map(task => ({
      ...task,
      distance: task.coordinates 
        ? calculateDistance(
            userLocation.latitude,
            userLocation.longitude,
            task.coordinates.lat,
            task.coordinates.lng
          )
        : undefined,
    }));
  }, [displayTasks, userLocation, calculateDistance]);

  // Optimized render item callback
  const renderItem: ListRenderItem<Task & { distance?: number }> = useCallback(({ item, index }) => {
    return (
      <View style={styles.itemContainer}>
        <TaskCard
          task={item}
          onComplete={onCompleteTask}
          onDelete={onDeleteTask}
          onEdit={onEditTask}
          onShowOnMap={onShowOnMap}
          distance={item.distance}
        />
      </View>
    );
  }, [onCompleteTask, onDeleteTask, onEditTask, onShowOnMap]);

  // Viewability configuration
  const viewabilityConfig = useMemo(() => ({
    itemVisiblePercentThreshold: 50,
    minimumViewTime: 100,
  }), []);

  // Handle viewable items change
  const onViewableItemsChanged = useCallback(({ viewableItems: newViewableItems }: {
    viewableItems: ViewToken[];
  }) => {
    const visibleIds = newViewableItems.map(item => item.item.id);
    setViewableItems(visibleIds);
  }, []);

  // Handle end reached for pagination
  const handleEndReached = useCallback(() => {
    if (onLoadMore && hasMore && !loading) {
      onLoadMore();
    }
  }, [onLoadMore, hasMore, loading]);

  // Memoized refresh control
  const refreshControl = useMemo(() => (
    onRefresh ? (
      <RefreshControl
        refreshing={refreshing}
        onRefresh={onRefresh}
        colors={['#3b82f6']}
        tintColor="#3b82f6"
      />
    ) : undefined
  ), [onRefresh, refreshing]);

  // Memoized empty component
  const emptyComponent = useMemo(() => (
    <FlashListEmptyComponent
      message={
        searchQuery
          ? `No tasks found for "${searchQuery}"`
          : showOnlyActive
            ? 'No active tasks'
            : 'No tasks yet'
      }
    />
  ), [searchQuery, showOnlyActive]);

  // Memoized footer component
  const footerComponent = useMemo(() => (
    <FlashListFooterComponent loading={loading} hasMore={hasMore} />
  ), [loading, hasMore]);

  // Scroll to top function
  const scrollToTop = useCallback(() => {
    flashListRef.current?.scrollToOffset({ offset: 0, animated: true });
  }, []);



  return (
    <View style={styles.container}>
      <TaskFlashList
        ref={flashListRef}
        tasks={tasksWithDistance}
        renderTask={renderItem}
        keyExtractor={keyExtractor}
        ItemSeparatorComponent={FlashListItemSeparator}
        ListEmptyComponent={emptyComponent}
        ListFooterComponent={footerComponent}
        refreshControl={refreshControl}
        onEndReached={handleEndReached}
        onEndReachedThreshold={0.5}
        showsVerticalScrollIndicator={false}
        style={styles.list}
        contentContainerStyle={displayTasks.length === 0 ? styles.emptyContainer : undefined}
        extraData={{ viewableItems }}
      />


    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  list: {
    flex: 1,
  },
  itemContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  emptyContainer: {
    flexGrow: 1,
    justifyContent: 'center',
  },

});

// Custom comparison for memo optimization
VirtualizedTaskList.displayName = 'VirtualizedTaskList';
