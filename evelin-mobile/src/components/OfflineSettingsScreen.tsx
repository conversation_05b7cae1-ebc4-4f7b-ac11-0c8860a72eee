import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Switch,
  StyleSheet,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useSelector } from 'react-redux';
import { RootState } from '../store';
import { 
  selectNetworkState,
  selectQueueStats,
  selectIsOnline,
  selectConnectionQuality,
} from '../store/slices/networkSlice';
import { offlineManager, OfflineManagerConfig } from '../services/offlineManager';
import { offlineStorageService } from '../services/offlineStorageService';

interface OfflineSettingsScreenProps {
  onBack: () => void;
}

export const OfflineSettingsScreen: React.FC<OfflineSettingsScreenProps> = ({
  onBack,
}) => {
  const networkState = useSelector(selectNetworkState);
  const queueStats = useSelector(selectQueueStats);
  const isOnline = useSelector(selectIsOnline);
  const connectionQuality = useSelector(selectConnectionQuality);

  const [config, setConfig] = useState<OfflineManagerConfig>(offlineManager.getConfig());
  const [storageInfo, setStorageInfo] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);

  useEffect(() => {
    loadStorageInfo();
  }, []);

  const loadStorageInfo = async () => {
    try {
      const info = await offlineManager.getStorageInfo();
      setStorageInfo(info);
    } catch (error) {
      console.error('Failed to load storage info:', error);
    }
  };

  const updateConfig = (updates: Partial<OfflineManagerConfig>) => {
    const newConfig = { ...config, ...updates };
    setConfig(newConfig);
    offlineManager.updateConfig(updates);
  };

  const handleSyncNow = async () => {
    if (!isOnline) {
      Alert.alert('Offline', 'Cannot sync while offline. Please check your connection.');
      return;
    }

    setIsSyncing(true);
    try {
      const success = await offlineManager.syncNow();
      if (success) {
        Alert.alert('Success', 'Sync completed successfully!');
        await loadStorageInfo();
      } else {
        Alert.alert('Error', 'Sync failed. Please try again.');
      }
    } catch (error) {
      Alert.alert('Error', 'An error occurred during sync.');
    } finally {
      setIsSyncing(false);
    }
  };

  const handleRetryFailed = async () => {
    if (queueStats.failedOperations === 0) {
      Alert.alert('Info', 'No failed operations to retry.');
      return;
    }

    setIsLoading(true);
    try {
      await offlineManager.retryFailedOperations();
      Alert.alert('Success', 'Retrying failed operations...');
    } catch (error) {
      Alert.alert('Error', 'Failed to retry operations.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClearOfflineData = () => {
    Alert.alert(
      'Clear Offline Data',
      'This will remove all offline data and pending operations. This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear',
          style: 'destructive',
          onPress: async () => {
            setIsLoading(true);
            try {
              await offlineManager.clearOfflineData();
              await loadStorageInfo();
              Alert.alert('Success', 'Offline data cleared successfully.');
            } catch (error) {
              Alert.alert('Error', 'Failed to clear offline data.');
            } finally {
              setIsLoading(false);
            }
          },
        },
      ]
    );
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getConnectionStatusColor = () => {
    if (!isOnline) return '#ef4444';
    switch (connectionQuality) {
      case 'excellent': return '#10b981';
      case 'good': return '#3b82f6';
      case 'fair': return '#f59e0b';
      case 'poor': return '#ef4444';
      default: return '#6b7280';
    }
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={onBack} style={styles.backButton}>
          <Text style={styles.backButtonText}>← Back</Text>
        </TouchableOpacity>
        <Text style={styles.title}>Offline Settings</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Connection Status */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Connection Status</Text>
          <View style={styles.statusCard}>
            <View style={styles.statusRow}>
              <Text style={styles.statusLabel}>Status:</Text>
              <View style={styles.statusIndicator}>
                <View 
                  style={[
                    styles.statusDot, 
                    { backgroundColor: getConnectionStatusColor() }
                  ]} 
                />
                <Text style={styles.statusText}>
                  {isOnline ? `Online (${connectionQuality})` : 'Offline'}
                </Text>
              </View>
            </View>
            <View style={styles.statusRow}>
              <Text style={styles.statusLabel}>Type:</Text>
              <Text style={styles.statusValue}>{networkState.type}</Text>
            </View>
          </View>
        </View>

        {/* Queue Statistics */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Sync Queue</Text>
          <View style={styles.statsGrid}>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>{queueStats.pendingOperations}</Text>
              <Text style={styles.statLabel}>Pending</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={[styles.statNumber, { color: '#ef4444' }]}>
                {queueStats.failedOperations}
              </Text>
              <Text style={styles.statLabel}>Failed</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={[styles.statNumber, { color: '#10b981' }]}>
                {queueStats.completedOperations}
              </Text>
              <Text style={styles.statLabel}>Completed</Text>
            </View>
          </View>
        </View>

        {/* Storage Information */}
        {storageInfo && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Storage</Text>
            <View style={styles.infoCard}>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Tasks Stored:</Text>
                <Text style={styles.infoValue}>{storageInfo.tasksCount}</Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Storage Size:</Text>
                <Text style={styles.infoValue}>{formatBytes(storageInfo.storageSize)}</Text>
              </View>
              <View style={styles.infoRow}>
                <Text style={styles.infoLabel}>Conflicts:</Text>
                <Text style={[styles.infoValue, { color: storageInfo.conflicts > 0 ? '#ef4444' : '#10b981' }]}>
                  {storageInfo.conflicts}
                </Text>
              </View>
              {storageInfo.lastSync && (
                <View style={styles.infoRow}>
                  <Text style={styles.infoLabel}>Last Sync:</Text>
                  <Text style={styles.infoValue}>
                    {new Date(storageInfo.lastSync).toLocaleString()}
                  </Text>
                </View>
              )}
            </View>
          </View>
        )}

        {/* Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Settings</Text>
          
          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>Auto Sync</Text>
              <Text style={styles.settingDescription}>
                Automatically sync when connection is available
              </Text>
            </View>
            <Switch
              value={config.autoSync}
              onValueChange={(value) => updateConfig({ autoSync: value })}
              trackColor={{ false: '#d1d5db', true: '#3b82f6' }}
              thumbColor={config.autoSync ? '#ffffff' : '#f3f4f6'}
            />
          </View>

          <View style={styles.settingItem}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingTitle}>Background Sync</Text>
              <Text style={styles.settingDescription}>
                Sync data when app is in background
              </Text>
            </View>
            <Switch
              value={config.enableBackgroundSync}
              onValueChange={(value) => updateConfig({ enableBackgroundSync: value })}
              trackColor={{ false: '#d1d5db', true: '#3b82f6' }}
              thumbColor={config.enableBackgroundSync ? '#ffffff' : '#f3f4f6'}
            />
          </View>
        </View>

        {/* Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Actions</Text>
          
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: '#3b82f6' }]}
            onPress={handleSyncNow}
            disabled={!isOnline || isSyncing}
          >
            {isSyncing ? (
              <ActivityIndicator color="white" size="small" />
            ) : (
              <Text style={styles.actionButtonText}>Sync Now</Text>
            )}
          </TouchableOpacity>

          {queueStats.failedOperations > 0 && (
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: '#f59e0b' }]}
              onPress={handleRetryFailed}
              disabled={isLoading}
            >
              <Text style={styles.actionButtonText}>
                Retry Failed ({queueStats.failedOperations})
              </Text>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: '#ef4444' }]}
            onPress={handleClearOfflineData}
            disabled={isLoading}
          >
            <Text style={styles.actionButtonText}>Clear Offline Data</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  backButton: {
    marginRight: 16,
  },
  backButtonText: {
    fontSize: 16,
    color: '#3b82f6',
    fontWeight: '500',
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1f2937',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 12,
  },
  statusCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  statusRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusLabel: {
    fontSize: 14,
    color: '#6b7280',
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  statusText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1f2937',
  },
  statusValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1f2937',
    textTransform: 'capitalize',
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statCard: {
    flex: 1,
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 4,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#3b82f6',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#6b7280',
    textAlign: 'center',
  },
  infoCard: {
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  infoLabel: {
    fontSize: 14,
    color: '#6b7280',
  },
  infoValue: {
    fontSize: 14,
    fontWeight: '500',
    color: '#1f2937',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    borderRadius: 12,
    padding: 16,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  settingInfo: {
    flex: 1,
    marginRight: 16,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1f2937',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    color: '#6b7280',
  },
  actionButton: {
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginBottom: 8,
  },
  actionButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});
