import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { StatusBar as ExpoStatusBar } from 'expo-status-bar';
import Constants from 'expo-constants';
import { Task } from '../types/task';
import { TaskList } from './TaskList';

interface TaskHistoryScreenProps {
  onBack: () => void;
  tasks: Task[];
  onCompleteTask: (taskId: string) => void;
  onDeleteTask: (taskId: string) => void;
  onEditTask: (taskId: string, updates: Partial<Task>) => void;
  onShowOnMap?: (task: Task) => void;
  userLocation?: { latitude: number; longitude: number };
  calculateDistance?: (lat1: number, lon1: number, lat2: number, lon2: number) => number;
}

export const TaskHistoryScreen: React.FC<TaskHistoryScreenProps> = ({
  onBack,
  tasks,
  onCompleteTask,
  onDeleteTask,
  onEditTask,
  onShowOnMap,
  userLocation,
  calculateDistance,
}) => {
  return (
    <View style={styles.container}>
        
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={onBack} style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back</Text>
          </TouchableOpacity>
          <Text style={styles.title}>Task History</Text>
          <View style={styles.headerSpacer} />
        </View>

        {/* Task List */}
        <TaskList
          tasks={tasks}
          onCompleteTask={onCompleteTask}
          onDeleteTask={onDeleteTask}
          onEditTask={onEditTask}
          onShowOnMap={onShowOnMap}
          userLocation={userLocation}
          calculateDistance={calculateDistance}
          showOnlyActive={false}
          maxTasks={0}
          useScrollView={false}
        />

    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
    paddingTop: Constants.statusBarHeight,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  backButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  backButtonText: {
    fontSize: 16,
    color: '#3b82f6',
    fontWeight: '500',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  headerSpacer: {
    width: 60,
  },
});
