import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  Animated,
} from 'react-native';

interface FuturisticVoiceButtonProps {
  isListening: boolean;
  isProcessing: boolean;
  transcript: string;
  isSupported: boolean;
  onStartListening: () => void;
  onStopListening: () => void;
  onTranscriptSubmit: (transcript: string) => void;
  error?: string | null;
}

export const FuturisticVoiceButton: React.FC<FuturisticVoiceButtonProps> = ({
  isListening,
  isProcessing,
  transcript,
  isSupported,
  onStartListening,
  onStopListening,
  onTranscriptSubmit,
  error,
}) => {
  // Animation values for the wave effect
  const wave1 = useRef(new Animated.Value(1)).current;
  const wave2 = useRef(new Animated.Value(1)).current;
  const wave3 = useRef(new Animated.Value(1)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    if (isListening) {
      // Start wave animations
      const createWaveAnimation = (animValue: Animated.Value, delay: number) => {
        return Animated.loop(
          Animated.sequence([
            Animated.timing(animValue, {
              toValue: 1.3,
              duration: 800,
              delay,
              useNativeDriver: true,
            }),
            Animated.timing(animValue, {
              toValue: 1,
              duration: 800,
              useNativeDriver: true,
            }),
          ])
        );
      };

      const wave1Anim = createWaveAnimation(wave1, 0);
      const wave2Anim = createWaveAnimation(wave2, 200);
      const wave3Anim = createWaveAnimation(wave3, 400);

      wave1Anim.start();
      wave2Anim.start();
      wave3Anim.start();

      // Pulse animation for the main button
      const pulseAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.1,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      );
      pulseAnimation.start();

      return () => {
        wave1Anim.stop();
        wave2Anim.stop();
        wave3Anim.stop();
        pulseAnimation.stop();
      };
    } else {
      // Reset animations
      wave1.setValue(1);
      wave2.setValue(1);
      wave3.setValue(1);
      pulseAnim.setValue(1);
    }
  }, [isListening]);

  const handlePress = () => {
    if (isListening) {
      onStopListening();
    } else {
      onStartListening();
    }
  };

  const handleSubmitTranscript = () => {
    if (transcript.trim()) {
      onTranscriptSubmit(transcript);
    }
  };

  if (!isSupported) {
    return (
      <View style={styles.container}>
        <View style={styles.card}>
          <Text style={styles.errorText}>
            Voice recognition is not supported on this device
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.card}>
        <View style={styles.header}>
          <Text style={styles.title}>🎙️ Voice Assistant</Text>
          <Text style={styles.subtitle}>
            {isListening ? 'Listening...' : isProcessing ? 'Processing...' : 'Tap to speak'}
          </Text>
        </View>

        <View style={styles.voiceButtonContainer}>
          {/* Wave animations */}
          {isListening && (
            <>
              <Animated.View
                style={[
                  styles.wave,
                  styles.wave3,
                  { transform: [{ scale: wave3 }] }
                ]}
              />
              <Animated.View
                style={[
                  styles.wave,
                  styles.wave2,
                  { transform: [{ scale: wave2 }] }
                ]}
              />
              <Animated.View
                style={[
                  styles.wave,
                  styles.wave1,
                  { transform: [{ scale: wave1 }] }
                ]}
              />
            </>
          )}

          {/* Main voice button */}
          <Animated.View style={{ transform: [{ scale: pulseAnim }] }}>
            <TouchableOpacity
              style={[
                styles.voiceButton,
                isListening && styles.voiceButtonActive,
                isProcessing && styles.voiceButtonProcessing,
              ]}
              onPress={handlePress}
              disabled={isProcessing}
            >
              {isProcessing ? (
                <ActivityIndicator size="large" color="#fff" />
              ) : (
                <View style={styles.buttonInner}>
                  <View style={[
                    styles.buttonCore,
                    isListening && styles.buttonCoreActive
                  ]} />
                </View>
              )}
            </TouchableOpacity>
          </Animated.View>
        </View>

        {error && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>❌ {error}</Text>
          </View>
        )}

        {transcript && (
          <View style={styles.transcriptContainer}>
            <Text style={styles.transcriptLabel}>Transcript:</Text>
            <Text style={styles.transcriptText}>{transcript}</Text>
            
            <View style={styles.transcriptActions}>
              <TouchableOpacity
                style={styles.submitButton}
                onPress={handleSubmitTranscript}
              >
                <Text style={styles.submitButtonText}>✨ Add Task</Text>
              </TouchableOpacity>
            </View>
          </View>
        )}

        <Text style={styles.hint}>
          Try saying: "Buy milk from Lidl" or "Call bank at 2pm"
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 20,
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 8,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 14,
    color: '#6b7280',
  },
  voiceButtonContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    height: 120,
    marginBottom: 24,
  },
  wave: {
    position: 'absolute',
    borderRadius: 50,
    borderWidth: 2,
  },
  wave1: {
    width: 80,
    height: 80,
    borderColor: '#3b82f6',
    opacity: 0.3,
  },
  wave2: {
    width: 100,
    height: 100,
    borderColor: '#3b82f6',
    opacity: 0.2,
  },
  wave3: {
    width: 120,
    height: 120,
    borderColor: '#3b82f6',
    opacity: 0.1,
  },
  voiceButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#3b82f6',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#3b82f6',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  voiceButtonActive: {
    backgroundColor: '#ef4444',
    shadowColor: '#ef4444',
  },
  voiceButtonProcessing: {
    backgroundColor: '#6b7280',
    shadowColor: '#6b7280',
  },
  buttonInner: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  buttonCore: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#fff',
  },
  buttonCoreActive: {
    backgroundColor: '#fff',
    shadowColor: '#fff',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.8,
    shadowRadius: 4,
    elevation: 4,
  },
  errorContainer: {
    backgroundColor: '#fef2f2',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#fecaca',
  },
  errorText: {
    color: '#dc2626',
    fontSize: 14,
    textAlign: 'center',
  },
  transcriptContainer: {
    backgroundColor: '#f0f9ff',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: '#bfdbfe',
  },
  transcriptLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1e40af',
    marginBottom: 12,
  },
  transcriptText: {
    fontSize: 16,
    color: '#1f2937',
    lineHeight: 24,
    marginBottom: 16,
  },
  transcriptActions: {
    flexDirection: 'row',
    justifyContent: 'center',
  },
  submitButton: {
    backgroundColor: '#10b981',
    borderRadius: 12,
    paddingHorizontal: 24,
    paddingVertical: 12,
    shadowColor: '#10b981',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  hint: {
    fontSize: 12,
    color: '#9ca3af',
    textAlign: 'center',
    fontStyle: 'italic',
    lineHeight: 18,
  },
});
