import React, { memo, useMemo, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import { Task } from '../types/task';
import { TaskCard } from './TaskCard';
import { VirtualizedTaskList } from './VirtualizedTaskList';
import { PERFORMANCE_CONSTANTS } from '../utils/performanceOptimizations';
import {
  TaskFlashList,
  FlashListEmptyComponent,
} from './FlashListComponents';

interface TaskListProps {
  tasks: Task[];
  onCompleteTask: (taskId: string) => void;
  onDeleteTask: (taskId: string) => void;
  onEditTask: (taskId: string, updates: Partial<Task>) => void;
  onShowOnMap?: (task: Task) => void;
  onViewAllTasks?: () => void;
  userLocation?: {
    latitude: number;
    longitude: number;
  };
  calculateDistance?: (lat1: number, lon1: number, lat2: number, lon2: number) => number;
  showOnlyActive?: boolean;
  maxTasks?: number;
  useScrollView?: boolean; // New prop to determine if we're inside a ScrollView
}

export const TaskList: React.FC<TaskListProps> = memo(({
  tasks = [], // Provide default empty array
  onCompleteTask,
  onDeleteTask,
  onEditTask,
  onShowOnMap,
  onViewAllTasks,
  userLocation,
  calculateDistance,
  showOnlyActive = true,
  maxTasks = 2,
  useScrollView = false,
}) => {
  // Early validation to prevent hooks rule violations
  if (!tasks) {
    console.warn('TaskList: tasks prop is null/undefined, using empty array');
  }



  // Memoized distance calculation
  const getTaskDistance = useCallback((task: Task): number | undefined => {
    if (!userLocation || !task.coordinates || !calculateDistance) {
      return undefined;
    }

    return calculateDistance(
      userLocation.latitude,
      userLocation.longitude,
      task.coordinates.lat,
      task.coordinates.lng
    );
  }, [userLocation, calculateDistance]);

  // Memoized render task function
  const renderTask = useCallback(({ item: task }: { item: Task }) => (
    <TaskCard
      task={task}
      onComplete={onCompleteTask}
      onDelete={onDeleteTask}
      onEdit={onEditTask}
      onShowOnMap={onShowOnMap}
      distance={getTaskDistance(task)}
    />
  ), [onCompleteTask, onDeleteTask, onEditTask, onShowOnMap, getTaskDistance]);

  // Memoized empty component
  const renderEmpty = useCallback(() => (
    <View style={styles.emptyContainer}>
      <Text style={styles.emptyIcon}>📝</Text>
      <Text style={styles.emptyTitle}>No tasks yet</Text>
      <Text style={styles.emptySubtitle}>
        Add your first task using voice or manual input
      </Text>
    </View>
  ), []);

  // Memoized filtered tasks
  const filteredTasks = useMemo(() => {
    let filtered = showOnlyActive ? tasks.filter(task => !task.completed) : tasks;

    // Apply max tasks limit if specified
    if (maxTasks) {
      filtered = filtered.slice(0, maxTasks);
    }

    return filtered;
  }, [tasks, showOnlyActive, maxTasks]);

  // Memoized task counts
  const taskCounts = useMemo(() => {
    const activeTasks = tasks.filter(t => !t.completed);
    const completedCount = tasks.filter(t => t.completed).length;
    const hasMoreTasks = showOnlyActive && maxTasks && activeTasks.length > maxTasks;

    return {
      activeTasks,
      activeCount: activeTasks.length,
      completedCount,
      hasMoreTasks,
    };
  }, [tasks, showOnlyActive, maxTasks]);

  // Memoized header component - removed for cleaner UI
  const renderHeader = useCallback(() => {
    if (tasks.length === 0) return null;

    // Only show "View All" button if there are more tasks
    if (showOnlyActive && taskCounts.hasMoreTasks) {
      return (
        <View style={styles.viewAllContainer}>
          <TouchableOpacity style={styles.viewAllButton} onPress={onViewAllTasks}>
            <Text style={styles.viewAllText}>View All Tasks ({taskCounts.activeCount})</Text>
          </TouchableOpacity>
        </View>
      );
    }

    return null;
  }, [tasks.length, taskCounts, showOnlyActive, onViewAllTasks]);

  // Determine if we should use virtualization
  const shouldUseVirtualization = useMemo(() => {
    return !useScrollView && tasks.length >= PERFORMANCE_CONSTANTS.VIRTUALIZATION_THRESHOLD;
  }, [useScrollView, tasks.length]);

  // If we should use virtualization, render the virtualized list
  if (shouldUseVirtualization) {
    return (
      <VirtualizedTaskList
        tasks={filteredTasks}
        onCompleteTask={onCompleteTask}
        onDeleteTask={onDeleteTask}
        onEditTask={onEditTask}
        onShowOnMap={onShowOnMap}
        userLocation={userLocation}
        calculateDistance={calculateDistance}
        showOnlyActive={showOnlyActive}
        maxTasks={maxTasks}
      />
    );
  }

  // Use different rendering based on whether we're inside a ScrollView
  if (useScrollView) {
    return (
      <View style={styles.container}>
        {renderHeader()}
        {filteredTasks.length === 0 ? (
          renderEmpty()
        ) : (
          filteredTasks.map((task) => (
            <View key={task.id}>
              {renderTask({ item: task })}
            </View>
          ))
        )}
      </View>
    );
  }

  // Use FlashList when not inside ScrollView (for better performance)
  return (
    <View style={styles.container}>
      <TaskFlashList
        tasks={filteredTasks}
        renderTask={renderTask}
        keyExtractor={(item) => item.id}
        ListHeaderComponent={renderHeader}
        ListEmptyComponent={renderEmpty}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={filteredTasks.length === 0 ? styles.emptyContentContainer : undefined}
        config="SMALL_LIST"
      />
    </View>
  );
});

// Custom comparison for memo optimization
TaskList.displayName = 'TaskList';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingTop: 8,
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  headerText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    textAlign: 'center',
    marginBottom: 12,
  },
  viewAllContainer: {
    paddingHorizontal: 20,
    paddingVertical: 8,
    alignItems: 'center',
  },
  viewAllButton: {
    backgroundColor: '#3b82f6',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
    alignSelf: 'center',
  },
  viewAllText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  emptyContentContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
    paddingVertical: 60,
  },
  emptyContainer: {
    alignItems: 'center',
  },
  emptyIcon: {
    fontSize: 48,
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#6b7280',
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#9ca3af',
    textAlign: 'center',
    lineHeight: 24,
  },
});
