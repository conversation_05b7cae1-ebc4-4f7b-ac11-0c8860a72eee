/**
 * Privacy Controls Screen
 * Comprehensive privacy settings and data management interface
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  Switch,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { privacyService } from '../services/privacyService';
import { 
  LocationPrivacySettings, 
  LocationDataSummary, 
  DataPurgeLog,
  LocationDataType,
  DataRetentionPeriod 
} from '../types/privacy';

interface PrivacyControlsScreenProps {
  onBack: () => void;
}

export default function PrivacyControlsScreen({ onBack }: PrivacyControlsScreenProps) {
  const [settings, setSettings] = useState<LocationPrivacySettings | null>(null);
  const [dataSummary, setDataSummary] = useState<LocationDataSummary | null>(null);
  const [loading, setLoading] = useState(true);
  const [purging, setPurging] = useState(false);

  useEffect(() => {
    loadPrivacyData();
  }, []);

  const loadPrivacyData = async () => {
    try {
      setLoading(true);
      const [privacySettings, summary] = await Promise.all([
        privacyService.getPrivacySettings(),
        privacyService.getLocationDataSummary(),
      ]);
      setSettings(privacySettings);
      setDataSummary(summary);
    } catch (error) {
      console.error('Error loading privacy data:', error);
      Alert.alert('Error', 'Failed to load privacy settings');
    } finally {
      setLoading(false);
    }
  };

  const updateSettings = async (updates: Partial<LocationPrivacySettings>) => {
    if (!settings) return;
    
    try {
      const newSettings = { ...settings, ...updates };
      await privacyService.updatePrivacySettings(newSettings);
      setSettings(newSettings);
    } catch (error) {
      console.error('Error updating privacy settings:', error);
      Alert.alert('Error', 'Failed to update privacy settings');
    }
  };

  const handleDataPurge = (dataTypes: LocationDataType[]) => {
    Alert.alert(
      'Confirm Data Deletion',
      `Are you sure you want to permanently delete ${dataTypes.join(', ')}? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              setPurging(true);
              await privacyService.purgeDataTypes(dataTypes);
              await loadPrivacyData(); // Refresh data summary
              Alert.alert('Success', 'Data has been permanently deleted');
            } catch (error) {
              console.error('Error purging data:', error);
              Alert.alert('Error', 'Failed to delete data');
            } finally {
              setPurging(false);
            }
          },
        },
      ]
    );
  };

  const handleExportData = async () => {
    try {
      const dataTypes: LocationDataType[] = ['location_history', 'visited_places', 'search_history'];
      const exportRequest = await privacyService.exportUserData(dataTypes);
      
      if (exportRequest.status === 'completed') {
        Alert.alert('Export Complete', 'Your data has been prepared for export. Check your downloads folder.');
      } else {
        Alert.alert('Export Failed', 'Failed to export your data. Please try again.');
      }
    } catch (error) {
      console.error('Error exporting data:', error);
      Alert.alert('Error', 'Failed to export data');
    }
  };

  const formatDataSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatRetentionPeriod = (period: DataRetentionPeriod): string => {
    const labels = {
      '1_hour': '1 Hour',
      '24_hours': '24 Hours',
      '7_days': '7 Days',
      '30_days': '30 Days',
      '90_days': '90 Days',
      '1_year': '1 Year',
      'forever': 'Forever',
    };
    return labels[period];
  };

  if (loading || !settings || !dataSummary) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Loading privacy settings...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={onBack} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#007AFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Privacy Controls</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Data Collection Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Data Collection</Text>
          
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Location Data Collection</Text>
              <Text style={styles.settingDescription}>Allow app to collect location data</Text>
            </View>
            <Switch
              value={settings.locationDataCollection.enabled}
              onValueChange={(value) => updateSettings({
                locationDataCollection: { ...settings.locationDataCollection, enabled: value }
              })}
            />
          </View>

          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Background Tracking</Text>
              <Text style={styles.settingDescription}>Track location when app is in background</Text>
            </View>
            <Switch
              value={settings.locationDataCollection.backgroundTracking}
              onValueChange={(value) => updateSettings({
                locationDataCollection: { ...settings.locationDataCollection, backgroundTracking: value }
              })}
              disabled={!settings.locationDataCollection.enabled}
            />
          </View>

          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>High Accuracy Mode</Text>
              <Text style={styles.settingDescription}>Use GPS for more precise location</Text>
            </View>
            <Switch
              value={settings.locationDataCollection.highAccuracyMode}
              onValueChange={(value) => updateSettings({
                locationDataCollection: { ...settings.locationDataCollection, highAccuracyMode: value }
              })}
              disabled={!settings.locationDataCollection.enabled}
            />
          </View>
        </View>

        {/* Data Usage Settings */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Data Usage</Text>
          
          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Analytics</Text>
              <Text style={styles.settingDescription}>Help improve the app with usage analytics</Text>
            </View>
            <Switch
              value={settings.dataUsage.allowAnalytics}
              onValueChange={(value) => updateSettings({
                dataUsage: { ...settings.dataUsage, allowAnalytics: value }
              })}
            />
          </View>

          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Personalization</Text>
              <Text style={styles.settingDescription}>Use data to personalize your experience</Text>
            </View>
            <Switch
              value={settings.dataUsage.allowPersonalization}
              onValueChange={(value) => updateSettings({
                dataUsage: { ...settings.dataUsage, allowPersonalization: value }
              })}
            />
          </View>

          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Anonymize Data</Text>
              <Text style={styles.settingDescription}>Remove personal identifiers from stored data</Text>
            </View>
            <Switch
              value={settings.dataUsage.anonymizeData}
              onValueChange={(value) => updateSettings({
                dataUsage: { ...settings.dataUsage, anonymizeData: value }
              })}
            />
          </View>
        </View>

        {/* Data Summary */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Your Data</Text>
          
          <View style={styles.dataCard}>
            <View style={styles.dataRow}>
              <Text style={styles.dataLabel}>Total Entries:</Text>
              <Text style={styles.dataValue}>{dataSummary.totalEntries.toLocaleString()}</Text>
            </View>
            <View style={styles.dataRow}>
              <Text style={styles.dataLabel}>Storage Used:</Text>
              <Text style={styles.dataValue}>{formatDataSize(dataSummary.storageSize)}</Text>
            </View>
            {dataSummary.oldestEntry && (
              <View style={styles.dataRow}>
                <Text style={styles.dataLabel}>Oldest Data:</Text>
                <Text style={styles.dataValue}>
                  {new Date(dataSummary.oldestEntry).toLocaleDateString()}
                </Text>
              </View>
            )}
          </View>

          {/* Data Type Breakdown */}
          <View style={styles.dataBreakdown}>
            <Text style={styles.dataBreakdownTitle}>Data Breakdown</Text>
            {Object.entries(dataSummary.dataTypes).map(([type, data]) => (
              data.count > 0 && (
                <View key={type} style={styles.dataTypeRow}>
                  <Text style={styles.dataTypeLabel}>
                    {type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </Text>
                  <Text style={styles.dataTypeValue}>
                    {data.count} items • {formatDataSize(data.size)}
                  </Text>
                </View>
              )
            ))}
          </View>
        </View>

        {/* Data Management Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Data Management</Text>
          
          <TouchableOpacity 
            style={styles.actionButton}
            onPress={handleExportData}
          >
            <Ionicons name="download-outline" size={20} color="#007AFF" />
            <Text style={styles.actionButtonText}>Export My Data</Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={[styles.actionButton, styles.dangerButton]}
            onPress={() => handleDataPurge(['location_history'])}
            disabled={purging}
          >
            <Ionicons name="trash-outline" size={20} color="#FF3B30" />
            <Text style={[styles.actionButtonText, styles.dangerText]}>
              {purging ? 'Deleting...' : 'Delete Location History'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={[styles.actionButton, styles.dangerButton]}
            onPress={() => handleDataPurge(['visited_places'])}
            disabled={purging}
          >
            <Ionicons name="location-outline" size={20} color="#FF3B30" />
            <Text style={[styles.actionButtonText, styles.dangerText]}>
              {purging ? 'Deleting...' : 'Delete Visited Places'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={[styles.actionButton, styles.dangerButton]}
            onPress={() => handleDataPurge(['search_history'])}
            disabled={purging}
          >
            <Ionicons name="search-outline" size={20} color="#FF3B30" />
            <Text style={[styles.actionButtonText, styles.dangerText]}>
              {purging ? 'Deleting...' : 'Delete Search History'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity 
            style={[styles.actionButton, styles.dangerButton]}
            onPress={() => handleDataPurge([
              'location_history', 
              'visited_places', 
              'search_history', 
              'current_location',
              'geofences',
              'route_data'
            ])}
            disabled={purging}
          >
            <Ionicons name="nuclear-outline" size={20} color="#FF3B30" />
            <Text style={[styles.actionButtonText, styles.dangerText]}>
              {purging ? 'Deleting...' : 'Delete All Location Data'}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e1e5e9',
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1a1a1a',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 16,
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  settingInfo: {
    flex: 1,
    marginRight: 16,
  },
  settingLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1a1a1a',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    color: '#666',
  },
  dataCard: {
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  dataRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  dataLabel: {
    fontSize: 14,
    color: '#666',
  },
  dataValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1a1a1a',
  },
  dataBreakdown: {
    marginTop: 8,
  },
  dataBreakdownTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 12,
  },
  dataTypeRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: '#f8f9fa',
    borderRadius: 6,
    marginBottom: 8,
  },
  dataTypeLabel: {
    fontSize: 14,
    color: '#1a1a1a',
    fontWeight: '500',
  },
  dataTypeValue: {
    fontSize: 12,
    color: '#666',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: '#e1e5e9',
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#007AFF',
    marginLeft: 8,
  },
  dangerButton: {
    backgroundColor: '#fff5f5',
    borderColor: '#fed7d7',
  },
  dangerText: {
    color: '#FF3B30',
  },
});
