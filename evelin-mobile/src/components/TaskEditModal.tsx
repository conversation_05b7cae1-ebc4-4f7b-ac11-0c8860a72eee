import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Modal,
  SafeAreaView,
  ScrollView,
  Alert,
} from 'react-native';
import { StatusBar as ExpoStatusBar } from 'expo-status-bar';
import { Task } from '../types/task';

interface TaskEditModalProps {
  task: Task | null;
  isVisible: boolean;
  onClose: () => void;
  onSave: (taskId: string, updates: Partial<Task>) => Promise<void>;
}

const locationOptions = [
  { value: 'lidl', label: 'Lidl', category: 'grocery' },
  { value: 'tesco', label: 'Tesco', category: 'grocery' },
  { value: 'asda', label: 'ASDA', category: 'grocery' },
  { value: 'sainsburys', label: 'Sainsburys', category: 'grocery' },
  { value: 'morrisons', label: 'Morrisons', category: 'grocery' },
  { value: 'aldi', label: 'Aldi', category: 'grocery' },
  { value: 'waitrose', label: 'Waitrose', category: 'grocery' },
  { value: 'pharmacy', label: 'Any Pharmacy', category: 'pharmacy' },
  { value: 'boots', label: 'Boots', category: 'pharmacy' },
  { value: 'superdrug', label: 'Superdrug', category: 'pharmacy' },
  { value: 'bank', label: 'Any Bank/ATM', category: 'finance' },
  { value: 'barclays', label: 'Barclays', category: 'finance' },
  { value: 'lloyds', label: 'Lloyds', category: 'finance' },
  { value: 'hsbc', label: 'HSBC', category: 'finance' },
  { value: 'petrol station', label: 'Petrol Station', category: 'fuel' },
  { value: 'shell', label: 'Shell', category: 'fuel' },
  { value: 'bp', label: 'BP', category: 'fuel' },
  { value: 'post office', label: 'Post Office', category: 'postal' },
  { value: 'gym', label: 'Gym', category: 'fitness' },
  { value: 'restaurant', label: 'Restaurant', category: 'food' },
  { value: 'mcdonalds', label: 'McDonalds', category: 'food' },
  { value: 'starbucks', label: 'Starbucks', category: 'food' },
];

const distanceOptions = [
  { value: 50, label: '50 meters' },
  { value: 100, label: '100 meters' },
  { value: 200, label: '200 meters' },
  { value: 500, label: '500 meters' },
  { value: 1000, label: '1 kilometer' },
  { value: 2000, label: '2 kilometers' },
];

export const TaskEditModal: React.FC<TaskEditModalProps> = ({
  task,
  isVisible,
  onClose,
  onSave,
}) => {
  const [text, setText] = useState('');
  const [location, setLocation] = useState('');
  const [customLocation, setCustomLocation] = useState('');
  const [category, setCategory] = useState('');
  const [notificationDistance, setNotificationDistance] = useState(200);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (task) {
      setText(task.text);
      setLocation(task.location || '');
      setCustomLocation('');
      setCategory(task.category || '');
      setNotificationDistance(task.notificationDistance || 200);
    }
  }, [task]);

  const handleSave = async () => {
    if (!task || !text.trim()) {
      Alert.alert('Error', 'Task text is required');
      return;
    }

    setSaving(true);
    try {
      const updates: Partial<Task> = {
        text: text.trim(),
        location: customLocation.trim() || location || undefined,
        category: category || undefined,
        notificationDistance,
      };

      await onSave(task.id, updates);
      onClose();
    } catch (error) {
      Alert.alert('Error', 'Failed to update task. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleLocationSelect = (selectedLocation: string) => {
    setLocation(selectedLocation);
    setCustomLocation('');
  };

  if (!task) return null;

  return (
    <Modal
      visible={isVisible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <View style={styles.container}>
        <ExpoStatusBar style="dark" />
        <SafeAreaView style={styles.safeArea}>
          
          {/* Header */}
          <View style={styles.header}>
            <TouchableOpacity onPress={onClose} style={styles.cancelButton}>
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.title}>Edit Task</Text>
            <TouchableOpacity 
              onPress={handleSave} 
              style={[styles.saveButton, saving && styles.disabledButton]}
              disabled={saving}
            >
              <Text style={styles.saveButtonText}>
                {saving ? 'Saving...' : 'Save'}
              </Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            
            {/* Task Text */}
            <View style={styles.section}>
              <Text style={styles.label}>Task Description</Text>
              <TextInput
                style={styles.textInput}
                value={text}
                onChangeText={setText}
                placeholder="What do you need to do?"
                multiline
                numberOfLines={3}
                textAlignVertical="top"
              />
            </View>

            {/* Location */}
            <View style={styles.section}>
              <Text style={styles.label}>Location (Optional)</Text>
              <TextInput
                style={styles.input}
                value={customLocation}
                onChangeText={setCustomLocation}
                placeholder="Type custom location or select below"
              />
              
              <Text style={styles.subLabel}>Quick Select:</Text>
              <View style={styles.locationGrid}>
                {locationOptions.map((option) => (
                  <TouchableOpacity
                    key={option.value}
                    style={[
                      styles.locationOption,
                      location === option.value && styles.locationOptionSelected,
                    ]}
                    onPress={() => handleLocationSelect(option.value)}
                  >
                    <Text
                      style={[
                        styles.locationOptionText,
                        location === option.value && styles.locationOptionTextSelected,
                      ]}
                    >
                      {option.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

            {/* Category */}
            <View style={styles.section}>
              <Text style={styles.label}>Category (Optional)</Text>
              <TextInput
                style={styles.input}
                value={category}
                onChangeText={setCategory}
                placeholder="e.g., Shopping, Health, Work"
              />
            </View>

            {/* Notification Distance */}
            <View style={styles.section}>
              <Text style={styles.label}>Notification Distance</Text>
              <Text style={styles.subLabel}>
                How close you need to be to get reminded
              </Text>
              <View style={styles.distanceGrid}>
                {distanceOptions.map((option) => (
                  <TouchableOpacity
                    key={option.value}
                    style={[
                      styles.distanceOption,
                      notificationDistance === option.value && styles.distanceOptionSelected,
                    ]}
                    onPress={() => setNotificationDistance(option.value)}
                  >
                    <Text
                      style={[
                        styles.distanceOptionText,
                        notificationDistance === option.value && styles.distanceOptionTextSelected,
                      ]}
                    >
                      {option.label}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>

          </ScrollView>
          
        </SafeAreaView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
  },
  safeArea: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  cancelButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    color: '#6b7280',
    fontWeight: '500',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  saveButton: {
    backgroundColor: '#3b82f6',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  disabledButton: {
    opacity: 0.6,
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    marginVertical: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
  },
  subLabel: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 12,
    marginTop: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: '#fff',
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    backgroundColor: '#fff',
    minHeight: 80,
  },
  locationGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  locationOption: {
    backgroundColor: '#f9fafb',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    marginBottom: 8,
  },
  locationOptionSelected: {
    backgroundColor: '#dbeafe',
    borderColor: '#3b82f6',
  },
  locationOptionText: {
    fontSize: 14,
    color: '#374151',
  },
  locationOptionTextSelected: {
    color: '#1e40af',
    fontWeight: '500',
  },
  distanceGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  distanceOption: {
    backgroundColor: '#f9fafb',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    alignItems: 'center',
    minWidth: 100,
  },
  distanceOptionSelected: {
    backgroundColor: '#dbeafe',
    borderColor: '#3b82f6',
  },
  distanceOptionText: {
    fontSize: 14,
    color: '#374151',
    fontWeight: '500',
  },
  distanceOptionTextSelected: {
    color: '#1e40af',
    fontWeight: '600',
  },
});
