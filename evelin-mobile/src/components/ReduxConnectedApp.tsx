import React, { useEffect, useCallback } from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import { User } from '@supabase/supabase-js';

// Redux hooks and actions
import { useAppDispatch, useAppSelector, useTasks, useUI, useUser } from '../store/hooks';
import { useCreateTaskMutation, useUpdateTaskMutation, useDeleteTaskMutation } from '../store/api/tasksApi';
import { setCurrentScreen, showToast, toggleHamburgerMenu } from '../store/slices/uiSlice';
import { updateProfileLocal, incrementTasksCreated, incrementTasksCompleted } from '../store/slices/userSlice';


// Components
import { EvelinMobileApp } from './EvelinMobileApp';

interface ReduxConnectedAppProps {
  user: User;
  onSignOut: () => void;
}

export const ReduxConnectedApp: React.FC<ReduxConnectedAppProps> = ({ user, onSignOut }) => {
  const dispatch = useAppDispatch();

  // Redux state
  const { tasks, loading: tasksLoading, error: tasksError } = useTasks();
  const { currentScreen, overlays } = useUI();
  const { profile } = useUser();

  // RTK Query mutations
  const [createTask] = useCreateTaskMutation();
  const [updateTaskMutation] = useUpdateTaskMutation();
  const [deleteTaskMutation] = useDeleteTaskMutation();
  


  // Initialize user profile if not exists
  useEffect(() => {
    if (!profile && user) {
      dispatch(updateProfileLocal({
        id: user.id,
        email: user.email || '',
        firstName: '',
        lastName: '',
        preferences: {
          theme: 'system',
          language: 'en',
          timezone: 'UTC',
          notifications: {
            email: true,
            push: true,
            sms: false,
          },
        },
        subscription: {
          plan: 'free',
          features: ['basic_tasks', 'voice_input'],
        },
        stats: {
          tasksCreated: 0,
          tasksCompleted: 0,
          streakDays: 0,
          joinedAt: new Date().toISOString(),
          lastActiveAt: new Date().toISOString(),
        },
      }));
    }
  }, [profile, user, dispatch]);

  // Task management functions
  const handleAddTask = useCallback(async (taskData: any) => {
    try {
      // Ensure required fields are present
      const taskToCreate = {
        text: taskData.text || '',
        location: taskData.location,
        category: taskData.category,
        completed: taskData.completed || false,
        notificationTriggered: taskData.notificationTriggered || false,
        notificationDistance: taskData.notificationDistance,
        coordinates: taskData.coordinates,
      };
      await createTask(taskToCreate).unwrap();
      dispatch(incrementTasksCreated());
      dispatch(showToast({
        message: 'Task created successfully!',
        type: 'success',
        duration: 3000,
      }));
    } catch (error) {
      console.error('Error adding task:', error);
      dispatch(showToast({
        message: 'Failed to create task',
        type: 'error',
        duration: 3000,
      }));
    }
  }, [createTask, dispatch]);

  const handleUpdateTask = useCallback(async (taskId: string, updates: any) => {
    try {
      await updateTaskMutation({ id: taskId, updates }).unwrap();

      // Track completion
      if (updates.completed === true) {
        dispatch(incrementTasksCompleted());
        dispatch(showToast({
          message: 'Task completed! 🎉',
          type: 'success',
          duration: 3000,
        }));
      }
    } catch (error) {
      console.error('Error updating task:', error);
      dispatch(showToast({
        message: 'Failed to update task',
        type: 'error',
        duration: 3000,
      }));
    }
  }, [updateTaskMutation, dispatch]);

  const handleDeleteTask = useCallback(async (taskId: string) => {
    try {
      await deleteTaskMutation(taskId).unwrap();
      dispatch(showToast({
        message: 'Task deleted',
        type: 'info',
        duration: 2000,
      }));
    } catch (error) {
      console.error('Error deleting task:', error);
      dispatch(showToast({
        message: 'Failed to delete task',
        type: 'error',
        duration: 3000,
      }));
    }
  }, [deleteTaskMutation, dispatch]);

  // Navigation functions
  const handleNavigateToScreen = useCallback((screen: any) => {
    dispatch(setCurrentScreen(screen));
    dispatch(toggleHamburgerMenu(false));
  }, [dispatch]);

  const handleBackToMain = useCallback(() => {
    dispatch(setCurrentScreen('main'));
  }, [dispatch]);

  // Error handling
  useEffect(() => {
    if (tasksError) {
      dispatch(showToast({
        message: typeof tasksError === 'string' ? tasksError : 'An error occurred',
        type: 'error',
        duration: 5000,
      }));
    }
  }, [tasksError, dispatch]);

  // Convert Redux state to props for the original component
  const appProps = {
    user,
    onSignOut,
    
    // Tasks
    tasks,
    loading: tasksLoading,
    onAddTask: handleAddTask,
    onUpdateTask: handleUpdateTask,
    onDeleteTask: handleDeleteTask,
    
    // Navigation
    currentScreen,
    onNavigateToScreen: handleNavigateToScreen,
    onBackToMain: handleBackToMain,
    
    // UI state
    showHamburgerMenu: overlays.showHamburgerMenu,
    showVoiceInput: overlays.showVoiceInput,
    showLocationSettings: overlays.showLocationSettings,
    showUserProfile: overlays.showUserProfile,
    showWelcome: overlays.showWelcome,

    
    // User profile
    userProfile: profile,
  };

  return (
    <View style={styles.container}>
      <EvelinMobileApp {...appProps} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
