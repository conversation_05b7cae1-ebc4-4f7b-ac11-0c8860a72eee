import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { StatusBar as ExpoStatusBar } from 'expo-status-bar';
import Constants from 'expo-constants';

interface AboutScreenProps {
  onBack: () => void;
}

export const AboutScreen: React.FC<AboutScreenProps> = ({ onBack }) => {
  return (
    <View style={styles.container}>
        
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={onBack} style={styles.backButton}>
            <Text style={styles.backButtonText}>← Back</Text>
          </TouchableOpacity>
          <Text style={styles.title}>About</Text>
          <View style={styles.headerSpacer} />
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          
          {/* App Description */}
          <View style={styles.section}>
            <View style={styles.logoContainer}>
              <Text style={styles.logo}>✨</Text>
              <Text style={styles.appName}><PERSON><PERSON></Text>
            </View>
            
            <Text style={styles.tagline}>
              Your Location-Based To-Do List Companion
            </Text>
            
            <Text style={styles.description}>
              <PERSON><PERSON> is an intelligent task management app that combines the power of voice recognition, 
              location awareness, and smart notifications to help you stay organized and never forget 
              important tasks when you're near the right place.
            </Text>
          </View>

          {/* Features */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Key Features</Text>
            
            <View style={styles.featureItem}>
              <Text style={styles.featureIcon}>🎙️</Text>
              <View style={styles.featureContent}>
                <Text style={styles.featureTitle}>Voice Input</Text>
                <Text style={styles.featureDescription}>
                  Create tasks naturally using voice commands with our futuristic interface
                </Text>
              </View>
            </View>
            
            <View style={styles.featureItem}>
              <Text style={styles.featureIcon}>📍</Text>
              <View style={styles.featureContent}>
                <Text style={styles.featureTitle}>Location Awareness</Text>
                <Text style={styles.featureDescription}>
                  Get reminded about tasks when you're near the right location
                </Text>
              </View>
            </View>
            
            <View style={styles.featureItem}>
              <Text style={styles.featureIcon}>🔔</Text>
              <View style={styles.featureContent}>
                <Text style={styles.featureTitle}>Smart Notifications</Text>
                <Text style={styles.featureDescription}>
                  Receive timely reminders based on your proximity to task locations
                </Text>
              </View>
            </View>
            
            <View style={styles.featureItem}>
              <Text style={styles.featureIcon}>🗺️</Text>
              <View style={styles.featureContent}>
                <Text style={styles.featureTitle}>Google Maps Integration</Text>
                <Text style={styles.featureDescription}>
                  Find places easily and get directions when you need them
                </Text>
              </View>
            </View>
          </View>

          {/* Company Info */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>About the Company</Text>
            
            <Text style={styles.companyDescription}>
              Evelin is developed by a passionate team dedicated to creating innovative productivity 
              solutions that seamlessly integrate with your daily life. We believe technology should 
              be intuitive, helpful, and enhance your natural workflows.
            </Text>
            
            <View style={styles.contactItem}>
              <Text style={styles.contactLabel}>Website</Text>
              <Text style={styles.contactValue}>www.evelin.app</Text>
            </View>
            
            <View style={styles.contactItem}>
              <Text style={styles.contactLabel}>Support</Text>
              <Text style={styles.contactValue}><EMAIL></Text>
            </View>
            
            <View style={styles.contactItem}>
              <Text style={styles.contactLabel}>Privacy Policy</Text>
              <Text style={styles.contactValue}>Available on our website</Text>
            </View>
          </View>

          {/* Copyright */}
          <View style={styles.section}>
            <Text style={styles.copyright}>
              © 2024 Evelin. All rights reserved.
            </Text>
            <Text style={styles.version}>
              Version 1.0.0 • Made with ❤️
            </Text>
          </View>

        </ScrollView>

    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
    paddingTop: Constants.statusBarHeight,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  backButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  backButtonText: {
    fontSize: 16,
    color: '#3b82f6',
    fontWeight: '500',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  headerSpacer: {
    width: 60,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    marginVertical: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  logo: {
    fontSize: 48,
    marginBottom: 8,
    color: '#3b82f6',
  },
  appName: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#3b82f6',
    marginBottom: 8,
  },
  tagline: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
    marginBottom: 16,
    fontStyle: 'italic',
  },
  description: {
    fontSize: 14,
    color: '#374151',
    lineHeight: 22,
    textAlign: 'center',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 16,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  featureIcon: {
    fontSize: 24,
    marginRight: 16,
    marginTop: 2,
  },
  featureContent: {
    flex: 1,
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 4,
  },
  featureDescription: {
    fontSize: 14,
    color: '#6b7280',
    lineHeight: 20,
  },
  companyDescription: {
    fontSize: 14,
    color: '#374151',
    lineHeight: 22,
    marginBottom: 20,
  },
  contactItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  contactLabel: {
    fontSize: 16,
    color: '#6b7280',
  },
  contactValue: {
    fontSize: 16,
    fontWeight: '500',
    color: '#3b82f6',
  },
  copyright: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
    marginBottom: 8,
  },
  version: {
    fontSize: 12,
    color: '#9ca3af',
    textAlign: 'center',
  },
});
