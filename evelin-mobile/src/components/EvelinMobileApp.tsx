import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  SafeAreaView,
  Alert,
} from 'react-native';
import { StatusBar as ExpoStatusBar } from 'expo-status-bar';
import * as SystemUI from 'expo-system-ui';
import Constants from 'expo-constants';
import { User } from '@supabase/supabase-js';

import { Task } from '../types/task';
import { FuturisticVoiceButton } from './FuturisticVoiceButton';
import { TaskList } from './TaskList';
import { LocationPermission } from './LocationPermission';
import { ManualTaskInput } from './ManualTaskInput';
import { DistanceSettings } from './DistanceSettings';
// Keep critical components that need to be immediately available
import { TaskEditModal } from './TaskEditModal';
import { MainScreenFlashList } from './MainScreenFlashList';

// Lazy-loaded components for better performance
import {
  LazyHamburgerMenu,
  LazySettingsScreen,
  LazyTaskHistoryScreen,
  LazyAccountScreen,
  LazyVersionScreen,
  LazyAboutScreen,
  LazyLocationSettingsScreen,
  LazyUserProfileScreen,
  LazyWelcomeScreen,
  ScreenPreloadStrategies,
} from './lazy/LazyScreens';
import { ComponentPreloader } from '../utils/lazyLoader';


import { useVoiceRecognition } from '../hooks/useVoiceRecognition';
import { useGeolocation } from '../hooks/useGeolocation';
import { parseTaskFromSpeech } from '../utils/taskParser';
import { mobileLocationService } from '../services/locationService';
import { mobileNotificationService } from '../services/notificationService';
import { backgroundLocationService } from '../services/backgroundLocationService';
import { appSettingsService } from '../services/appSettingsService';
import { aiLocationService } from '../services/aiLocationService';
import { offlineManager } from '../services/offlineManager';

import { memoryManager, MemoryManager } from '../utils/memoryManager';

// Redux imports
import { useAppDispatch, useTasks } from '../store/hooks';
import { useCreateTaskMutation, useUpdateTaskMutation, useDeleteTaskMutation } from '../store/api/tasksApi';
import { showToast } from '../store/slices/uiSlice';

// Import offline demo for testing (only in development)
if (__DEV__) {
  import('../utils/offlineDemo');
}

interface EvelinMobileAppProps {
  user: User;
  onSignOut: () => void;
}

export const EvelinMobileApp: React.FC<EvelinMobileAppProps> = ({ user, onSignOut }) => {
  const dispatch = useAppDispatch();

  const [defaultNotificationDistance, setDefaultNotificationDistance] = useState(200);
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [showManualInput, setShowManualInput] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [editingTask, setEditingTask] = useState<Task | null>(null);
  const [googleMapsInitialized, setGoogleMapsInitialized] = useState(false);
  const [showHamburgerMenu, setShowHamburgerMenu] = useState(false);
  const [currentScreen, setCurrentScreen] = useState('main');
  const [showLocationSettings, setShowLocationSettings] = useState(false);

  // Debug showLocationSettings state changes
  useEffect(() => {
    console.log('🔍 showLocationSettings state changed:', showLocationSettings);
  }, [showLocationSettings]);
  const [showUserProfile, setShowUserProfile] = useState(false);
  const [showWelcome, setShowWelcome] = useState(false);
  const [userProfile, setUserProfile] = useState<any>(null);

  const [componentsPreloaded, setComponentsPreloaded] = useState(false);

  // Use RTK Query hooks for task management
  const { tasks, loading: tasksLoading } = useTasks();
  const [createTask] = useCreateTaskMutation();
  const [updateTaskMutation] = useUpdateTaskMutation();
  const [deleteTaskMutation] = useDeleteTaskMutation();
  
  const {
    isListening,
    isProcessing,
    transcript,
    isSupported,
    startListening,
    stopListening,
    clearTranscript,
    error: voiceError,
  } = useVoiceRecognition();

  const {
    latitude,
    longitude,
    accuracy,
    error: locationError,
    permission: locationPermission,
    isTrackingEnabled,
    requestPermission: requestLocationPermission,
    toggleLocationTracking,
  } = useGeolocation();

  // Wrapper functions for RTK Query mutations to match expected interface
  const addTask = useCallback(async (taskData: Partial<Task>) => {
    try {
      // Ensure required fields are present
      const taskToCreate: Omit<Task, 'id' | 'createdAt'> = {
        text: taskData.text || '',
        location: taskData.location,
        category: taskData.category,
        completed: taskData.completed || false,
        notificationTriggered: taskData.notificationTriggered || false,
        notificationDistance: taskData.notificationDistance,
        coordinates: taskData.coordinates,
      };
      await createTask(taskToCreate).unwrap();
    } catch (error) {
      console.error('Error adding task:', error);
      throw error;
    }
  }, [createTask]);

  const updateTask = useCallback(async (taskId: string, updates: Partial<Task>) => {
    try {
      await updateTaskMutation({ id: taskId, updates }).unwrap();
    } catch (error) {
      console.error('Error updating task:', error);
      throw error;
    }
  }, [updateTaskMutation]);

  const deleteTask = useCallback(async (taskId: string) => {
    try {
      await deleteTaskMutation(taskId).unwrap();
    } catch (error) {
      console.error('Error deleting task:', error);
      throw error;
    }
  }, [deleteTaskMutation]);

  // Initialize mobile-specific features
  useEffect(() => {
    initializeMobileFeatures();

    // Configure system UI to disable edge-to-edge
    SystemUI.setBackgroundColorAsync('#f8fafc');

    // Cleanup function for when app unmounts
    return () => {
      console.log('🧹 App unmounting - performing cleanup');

      // Cleanup services
      memoryManager.cleanup().catch(error =>
        console.error('Error during memory manager cleanup:', error)
      );

      backgroundLocationService.cleanup().catch(error =>
        console.error('Error during background location service cleanup:', error)
      );

      offlineManager.clearOfflineData().catch((error: any) =>
        console.error('Error during offline manager cleanup:', error)
      );
    };
  }, []);

  const initializeMobileFeatures = async () => {
    try {
      // Initialize memory manager first
      await memoryManager.initialize();

      // Register cleanup tasks for memory management
      memoryManager.registerCleanupTask(MemoryManager.createLocationCleanupTask());
      memoryManager.registerCleanupTask(MemoryManager.createOfflineDataCleanupTask());
      memoryManager.registerCleanupTask({
        id: 'background-location-cleanup',
        name: 'Background Location Service Cleanup',
        priority: 'high',
        cleanup: async () => {
          if (backgroundLocationService.shouldPerformCleanup()) {
            await backgroundLocationService.cleanup();
          }
        },
      });

      // Initialize app settings service
      await appSettingsService.initialize();

      // Initialize background location service
      await backgroundLocationService.initialize();

      // Initialize offline manager
      await offlineManager.initialize({
        autoSync: true,
        syncInterval: 5 * 60 * 1000, // 5 minutes
        maxRetries: 3,
        enableBackgroundSync: true,
        conflictResolution: 'server-wins',
      });

      // Load user profile and check if we should show welcome
      const profile = await appSettingsService.loadUserProfile();
      setUserProfile(profile as any);

      if (!profile?.firstName) {
        setShowWelcome(true);
      }

      // Preload critical components in the background
      preloadCriticalComponents();

      // Request permissions
      const locationPermission = await mobileLocationService.requestLocationPermission();
      const notificationPermission = await mobileNotificationService.requestPermissions();

      console.log('Mobile permissions:', {
        location: locationPermission,
        notifications: notificationPermission,
      });

      // Show user-friendly message if notifications aren't available
      if (!notificationPermission && __DEV__) {
        console.log('ℹ️ Running in Expo Go - notifications are limited. For full notification support, use a development build.');
      }

      // Setup notification handler
      mobileNotificationService.setupNotificationResponseHandler((data) => {
        console.log('Notification pressed:', data);
      });

      setGoogleMapsInitialized(true);
    } catch (error) {
      console.error('Error initializing mobile features:', error);
    }
  };

  // Location-based notifications
  useEffect(() => {
    if (latitude && longitude && tasks.length > 0) {
      checkNearbyTasks();
    }
  }, [latitude, longitude, tasks]);

  // Periodic background location resolution for tasks without coordinates
  useEffect(() => {
    const backgroundResolutionInterval = setInterval(() => {
      if (latitude && longitude) {
        const tasksWithoutCoordinates = tasks.filter(task =>
          !task.completed &&
          !task.notificationTriggered &&
          !task.coordinates &&
          task.location
        );

        if (tasksWithoutCoordinates.length > 0) {
          console.log(`⏰ Periodic background check: ${tasksWithoutCoordinates.length} tasks need location resolution`);
          checkNearbyTasks(); // This will handle both coordinate and non-coordinate tasks
        }
      }
    }, 60000); // Check every minute

    return () => clearInterval(backgroundResolutionInterval);
  }, [latitude, longitude, tasks]);

  const checkNearbyTasks = async () => {
    if (!latitude || !longitude) return;

    try {
      // Check tasks with exact coordinates first
      const nearbyTasks = tasks.filter(task => {
        // Skip tasks with temporary IDs (optimistic updates)
        if (task.id.startsWith('temp-')) {
          return false;
        }

        if (task.completed || task.notificationTriggered || !task.coordinates) {
          return false;
        }

        const distance = mobileLocationService.calculateDistance(
          latitude,
          longitude,
          task.coordinates.lat,
          task.coordinates.lng
        );

        const notificationDistance = task.notificationDistance || defaultNotificationDistance;
        return distance < notificationDistance;
      });

      for (const task of nearbyTasks) {
        const distance = mobileLocationService.calculateDistance(
          latitude,
          longitude,
          task.coordinates!.lat,
          task.coordinates!.lng
        );

        // Show notification
        await mobileNotificationService.showLocationNotification(task, distance);

        // Mark as notified
        await updateTask(task.id, { notificationTriggered: true });
      }

      // Check tasks without coordinates but with location names
      const tasksWithoutCoordinates = tasks.filter(task =>
        !task.completed &&
        !task.notificationTriggered &&
        !task.coordinates &&
        task.location
      );

      if (tasksWithoutCoordinates.length > 0) {
        console.log(`🔍 Background location resolution: checking ${tasksWithoutCoordinates.length} tasks without coordinates`);
      }

      for (const task of tasksWithoutCoordinates) {
        try {
          // Skip tasks with temporary IDs (optimistic updates)
          if (task.id.startsWith('temp-')) {
            continue;
          }

          // Try to resolve location now that user has moved
          const searchRadius = task.notificationDistance || defaultNotificationDistance;
          console.log(`🎯 Attempting to resolve location for task: "${task.text}" (location: "${task.location}") within ${searchRadius * 2}m`);

          const specificResult = await aiLocationService.findSpecificPlace(
            task.location!,
            searchRadius * 2 // Use larger radius for background resolution
          );

          if (specificResult) {
            console.log(`✅ Resolved location for existing task: ${task.location} -> ${specificResult.name} at ${specificResult.address}`);

            // Update task with found coordinates
            await updateTask(task.id, {
              coordinates: specificResult.coordinates,
              location: specificResult.name,
              category: specificResult.category
            });

            // Check if user is now nearby
            const distance = mobileLocationService.calculateDistance(
              latitude,
              longitude,
              specificResult.coordinates.lat,
              specificResult.coordinates.lng
            );

            console.log(`📏 Distance to resolved location: ${distance}m (notification radius: ${searchRadius}m)`);

            if (distance < searchRadius) {
              console.log(`🔔 User is within notification range! Triggering notification for: ${task.text}`);
              await mobileNotificationService.showLocationNotification(task, distance);
              await updateTask(task.id, { notificationTriggered: true });
            } else {
              console.log(`📍 Location resolved but user not yet in range. Will notify when closer.`);
            }
          } else {
            console.log(`❌ Still no location found for "${task.location}" - will try again later`);
          }
        } catch (error) {
          // Log but don't throw - location might be resolvable later
          console.log(`⚠️ Could not resolve location for task "${task.location}":`, error instanceof Error ? error.message : error);
        }
      }
    } catch (error) {
      console.error('Error checking nearby tasks:', error);
    }
  };

  const handleVoiceInput = useCallback(async (transcript: string) => {
    if (!transcript.trim()) return;

    try {
      console.log('🎙️ VOICE INPUT HANDLER - Processing:', transcript, 'with default radius:', defaultNotificationDistance);
      const parsedTask = await parseTaskFromSpeech(transcript, defaultNotificationDistance);
      console.log('📝 VOICE INPUT HANDLER - Parsed task result:', parsedTask);

      // Set default notification distance if not set by intelligent service
      if (!parsedTask.notificationDistance) {
        parsedTask.notificationDistance = defaultNotificationDistance;
      }

      await addTask(parsedTask);
      clearTranscript();

      // Show success message
      dispatch(showToast({
        message: 'Voice task created successfully!',
        type: 'success',
        duration: 3000,
      }));
    } catch (error) {
      console.error('Error processing voice input:', error);

      // For location-related errors, still create the task but inform the user
      if (error instanceof Error && error.message.includes('find')) {
        console.warn('Could not find exact location for voice task, but creating task anyway');

        // Create a basic task from the transcript
        const basicTask = {
          id: Math.random().toString(36).substring(2) + Date.now().toString(36),
          text: transcript,
          completed: false,
          createdAt: new Date(),
          notificationDistance: defaultNotificationDistance,
        };

        await addTask(basicTask);
        clearTranscript();

        dispatch(showToast({
          message: 'Voice task created - we\'ll notify you when you\'re near relevant locations!',
          type: 'info',
          duration: 4000,
        }));
      } else {
        // For other errors, show error message
        const errorMessage = error instanceof Error ? error.message : 'Failed to create task from voice input';

        dispatch(showToast({
          message: errorMessage,
          type: 'error',
          duration: 5000,
        }));
      }
    }
  }, [defaultNotificationDistance, addTask, clearTranscript, dispatch]);

  const handleManualTaskSubmit = async (taskData: Partial<Task>) => {
    try {
      console.log('📝 Processing manual task:', taskData);

      // Set default notification distance
      if (!taskData.notificationDistance) {
        taskData.notificationDistance = defaultNotificationDistance;
      }

      // Create task immediately for instant UI feedback (optimistic update)
      await addTask(taskData);
      setShowManualInput(false);

      // Show initial success message
      dispatch(showToast({
        message: `Task created! ${taskData.location ? `Looking for "${taskData.location}"...` : ''}`,
        type: 'success',
        duration: 2000,
      }));

      // Do location resolution in the background if task has location
      // The background location resolution in the periodic effect will handle updating
      // tasks without coordinates, so we don't need to do it here
      if (taskData.location) {
        console.log(`🔍 Task created, background location resolution will find "${taskData.location}"`);
      }
    } catch (error) {
      console.error('Error adding manual task:', error);
      dispatch(showToast({
        message: 'Failed to create task. Please try again.',
        type: 'error',
        duration: 3000,
      }));
    }
  };

  // Wrapper function to match the expected interface for ManualInputData
  const handleManualInputSubmit = (text: string, location: string, distance: number) => {
    const taskData: Partial<Task> = {
      text,
      location,
      notificationDistance: distance,
    };
    handleManualTaskSubmit(taskData);
  };

  const handleCompleteTask = useCallback(async (taskId: string) => {
    try {
      const task = tasks.find(t => t.id === taskId);
      if (!task) return;

      await updateTask(taskId, { completed: !task.completed });
    } catch (error) {
      console.error('Error updating task:', error);
    }
  }, [tasks, updateTask]);

  const handleDeleteTask = useCallback(async (taskId: string) => {
    try {
      await deleteTask(taskId);
    } catch (error) {
      console.error('Error deleting task:', error);
    }
  }, [deleteTask]);

  const handleEditTask = useCallback((taskId: string, updates: Partial<Task>) => {
    const task = tasks.find(t => t.id === taskId);
    if (task) {
      setEditingTask({ ...task, ...updates });
    }
  }, [tasks]);

  const handleSaveEditedTask = async (taskId: string, updates: Partial<Task>) => {
    try {
      await updateTask(taskId, updates);
      setEditingTask(null);
    } catch (error) {
      console.error('Error updating task:', error);
    }
  };

  const openInGoogleMaps = (task: Task) => {
    if (task.coordinates) {
      const { lat, lng } = task.coordinates;
      const placeName = task.location || 'location';
      const url = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}&travelmode=walking`;
      
      // In React Native, we would use Linking.openURL(url)
      console.log('Opening Google Maps:', url);
    }
  };

  const calculateDistance = useCallback((lat1: number, lon1: number, lat2: number, lon2: number): number => {
    return mobileLocationService.calculateDistance(lat1, lon1, lat2, lon2);
  }, []);

  const handleMenuNavigation = useCallback((screen: string) => {
    setCurrentScreen(screen);
    setShowHamburgerMenu(false);
  }, []);

  const handleBackToMain = useCallback(() => {
    setCurrentScreen('main');
  }, []);

  // Memoized values for better performance
  const userLocation = useMemo(() =>
    latitude && longitude ? { latitude, longitude } : undefined,
    [latitude, longitude]
  );

  const handleViewAllTasks = useCallback(() => {
    setShowHamburgerMenu(true);
  }, []);

  // Preload critical components for better performance
  const preloadCriticalComponents = async () => {
    if (componentsPreloaded) return;

    try {
      console.log('🚀 Starting component preloading...');

      // Preload critical screens that users access frequently
      await ScreenPreloadStrategies.preloadCriticalScreens();

      // Preload components during idle time
      ScreenPreloadStrategies.preloadDuringIdle();

      setComponentsPreloaded(true);
      console.log('✅ Component preloading completed');
    } catch (error) {
      console.error('❌ Component preloading failed:', error);
    }
  };

  const handleSignOut = () => {
    onSignOut();
  };

  // Render different screens based on currentScreen
  const renderCurrentScreen = () => {
    // Check modal/overlay screens first (they should take priority)
    if (showLocationSettings) {
      console.log('🎯 Rendering LocationSettingsScreen');
      return (
        <LazyLocationSettingsScreen
          onBack={() => {
            console.log('🔙 LocationSettings onBack called');
            setShowLocationSettings(false);
          }}
        />
      );
    }

    if (currentScreen === 'settings') {
      return (
        <LazySettingsScreen
          onBack={handleBackToMain}
          defaultDistance={defaultNotificationDistance}
          onDistanceChange={setDefaultNotificationDistance}
          onOpenLocationSettings={() => {
            console.log('🚀 Opening Location Settings - setShowLocationSettings(true)');
            setShowLocationSettings(true);
          }}
        />
      );
    }

    if (currentScreen === 'history') {
      return (
        <LazyTaskHistoryScreen
          onBack={handleBackToMain}
          tasks={tasks}
          onCompleteTask={handleCompleteTask}
          onDeleteTask={handleDeleteTask}
          onEditTask={handleEditTask}
          onShowOnMap={openInGoogleMaps}
          userLocation={latitude && longitude ? { latitude, longitude } : undefined}
          calculateDistance={calculateDistance}
        />
      );
    }

    if (currentScreen === 'account') {
      return (
        <LazyAccountScreen
          onBack={handleBackToMain}
          onOpenProfile={() => setShowUserProfile(true)}
          user={user}
        />
      );
    }

    if (currentScreen === 'version') {
      return (
        <LazyVersionScreen
          onBack={handleBackToMain}
        />
      );
    }

    if (currentScreen === 'about') {
      return (
        <LazyAboutScreen
          onBack={handleBackToMain}
        />
      );
    }



    if (showUserProfile) {
      return (
        <LazyUserProfileScreen
          onBack={() => setShowUserProfile(false)}
          userEmail={user?.email || ''}
        />
      );
    }

    if (showWelcome) {
      return (
        <LazyWelcomeScreen
          onContinue={() => {
            setShowWelcome(false);
            setShowUserProfile(true);
          }}
          userName={userProfile?.firstName || ''}
        />
      );
    }

    // Default main screen
    return null;
  };

  // Check if we should render a different screen
  const screenToRender = renderCurrentScreen();
  if (screenToRender) {
    return screenToRender;
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerTop}>
          <TouchableOpacity
            onPress={() => setShowHamburgerMenu(true)}
            style={styles.hamburgerButton}
          >
            <Text style={styles.hamburgerText}>☰</Text>
          </TouchableOpacity>

          <View style={styles.logoContainer}>
            <Text style={styles.logo}>✨</Text>
            <Text style={styles.title}>Evelin</Text>
          </View>

          <View style={styles.headerSpacer} />
        </View>

        <Text style={styles.subtitle}>Your Location-Based To-Do List Companion</Text>
      </View>

      {/* Main content with FlashList */}
      <MainScreenFlashList
        locationData={{
          permission: locationPermission,
          onRequestPermission: requestLocationPermission,
          latitude,
          longitude,
          accuracy,
          error: locationError,
          isTrackingEnabled,
          onToggleTracking: toggleLocationTracking,
        }}
        voiceData={{
          isListening,
          isProcessing,
          transcript,
          isSupported,
          onStartListening: startListening,
          onStopListening: stopListening,
          onTranscriptSubmit: handleVoiceInput,
          error: voiceError,
        }}
        manualData={{
          showManualInput,
          onToggleManualInput: () => setShowManualInput(!showManualInput),
          onSubmit: handleManualInputSubmit,
          defaultDistance: defaultNotificationDistance,
        }}
        distanceData={{
          defaultDistance: defaultNotificationDistance,
          onDistanceChange: setDefaultNotificationDistance,
          isVisible: showSettings,
          onToggle: () => setShowSettings(!showSettings),
        }}
        taskData={{
          tasks,
          onCompleteTask: handleCompleteTask,
          onDeleteTask: handleDeleteTask,
          onEditTask: handleEditTask,
          onShowOnMap: openInGoogleMaps,
          onViewAllTasks: handleViewAllTasks,
          userLocation,
          calculateDistance,
        }}
      />

      {/* Hamburger Menu */}
      <LazyHamburgerMenu
        visible={showHamburgerMenu}
        onClose={() => setShowHamburgerMenu(false)}
        onNavigate={handleMenuNavigation}
        onSignOut={handleSignOut}
        user={user}
        userName={userProfile?.firstName}
      />



      {/* Task Edit Modal */}
      <TaskEditModal
        task={editingTask}
        isVisible={!!editingTask}
        onClose={() => setEditingTask(null)}
        onSave={handleSaveEditedTask}
      />

    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
    paddingTop: Constants.statusBarHeight, // Add padding to push content below status bar
  },
  header: {
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  hamburgerButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    backgroundColor: '#f3f4f6',
    borderRadius: 8,
    width: 44,
    height: 44,
    justifyContent: 'center',
    alignItems: 'center',
  },
  hamburgerText: {
    color: '#374151',
    fontSize: 18,
    fontWeight: '500',
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  logo: {
    fontSize: 24,
    marginRight: 8,
    color: '#3b82f6',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#3b82f6',
  },
  headerSpacer: {
    width: 44,
    height: 44,
  },
  subtitle: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
  },
  content: {
    flex: 1,
  },


});
