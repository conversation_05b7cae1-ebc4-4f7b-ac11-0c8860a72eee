import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Switch,
  Alert,
} from 'react-native';
import { StatusBar as ExpoStatusBar } from 'expo-status-bar';
import Constants from 'expo-constants';
import { appSettingsService, AppSettings } from '../services/appSettingsService';

interface SettingsScreenProps {
  onBack: () => void;
  defaultDistance: number;
  onDistanceChange: (distance: number) => void;
  onOpenLocationSettings?: () => void;
}

export const SettingsScreen: React.FC<SettingsScreenProps> = ({
  onBack,
  defaultDistance,
  onDistanceChange,
  onOpenLocationSettings,
}) => {
  const [settings, setSettings] = useState<AppSettings>({
    darkMode: false,
    soundEffects: true,
    hapticFeedback: true,
    notifications: true,
    backgroundLocation: false,
    defaultNotificationDistance: 200,
    quietHours: { enabled: true, start: '22:00', end: '07:00' },
  });
  const [loading, setLoading] = useState(true);
  const [locationPermissionStatus, setLocationPermissionStatus] = useState<'granted' | 'denied' | 'unknown'>('unknown');

  const distanceOptions = [50, 100, 200, 500, 1000];

  useEffect(() => {
    loadSettings();
    checkLocationPermissionStatus();
  }, []);

  const checkLocationPermissionStatus = async () => {
    try {
      const { mobileLocationService } = await import('../services/locationService');
      const hasPermission = await mobileLocationService.hasLocationPermission();
      const hasBackgroundPermission = await mobileLocationService.hasBackgroundLocationPermission();

      if (hasBackgroundPermission) {
        setLocationPermissionStatus('granted');
      } else if (hasPermission) {
        setLocationPermissionStatus('granted'); // Foreground permission is sufficient
      } else {
        setLocationPermissionStatus('denied');
      }
    } catch (error) {
      console.error('Error checking location permission status:', error);
      setLocationPermissionStatus('unknown');
    }
  };

  const loadSettings = async () => {
    try {
      const currentSettings = await appSettingsService.loadSettings();
      setSettings(currentSettings);
    } catch (error) {
      console.error('Error loading settings:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateSetting = async (key: keyof AppSettings, value: any) => {
    try {
      const newSettings = { ...settings, [key]: value };
      setSettings(newSettings);
      await appSettingsService.saveSettings({ [key]: value });
      await appSettingsService.triggerHaptic('light');
    } catch (error) {
      console.error('Error updating setting:', error);
    }
  };

  const handleBackgroundLocationToggle = async (value: boolean) => {
    await appSettingsService.triggerHaptic('medium');

    if (value) {
      // User wants to enable background location
      Alert.alert(
        'Enable Background Tracking',
        'This allows location-based reminders when you arrive at or leave specific places.\n\n• Get notified when near your tasks\n• Works even when app is closed\n• You can disable anytime',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Enable',
            onPress: async () => {
              try {
                console.log('🔄 Requesting location permissions...');

                // First, request location permission
                const hasPermission = await requestLocationPermission();

                if (hasPermission) {
                  // Permission granted, update the setting
                  await updateSetting('backgroundLocation', true);
                  await checkLocationPermissionStatus(); // Refresh permission status
                  await appSettingsService.triggerHaptic('success');

                  Alert.alert(
                    'Background Tracking Enabled! ✅',
                    'Location-based reminders are now active. The app will notify you when you\'re near your tasks.',
                    [{ text: 'Got it!' }]
                  );
                } else {
                  // Permission denied, show settings option
                  Alert.alert(
                    'Permission Required',
                    'To enable background tracking, please:\n\n1. Tap "Open Settings" below\n2. Find "Location" or "Permissions"\n3. Enable "Location" for this app\n4. Select "Allow all the time" if available',
                    [
                      { text: 'Cancel', style: 'cancel' },
                      {
                        text: 'Open Settings',
                        onPress: async () => {
                          await appSettingsService.openLocationSettings();
                        }
                      }
                    ]
                  );
                }
              } catch (error) {
                console.error('Error enabling background location:', error);
                await appSettingsService.triggerHaptic('error');
                Alert.alert(
                  'Error',
                  'Failed to enable background tracking. Please check your device settings and try again.',
                  [{ text: 'OK' }]
                );
              }
            }
          }
        ]
      );
    } else {
      // User wants to disable background location
      Alert.alert(
        'Disable Background Tracking',
        'This will turn off location-based reminders. You won\'t get notified when near your tasks.',
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Disable',
            style: 'destructive',
            onPress: async () => {
              await updateSetting('backgroundLocation', false);
              await appSettingsService.triggerHaptic('light');
            }
          }
        ]
      );
    }
  };

  const requestLocationPermission = async (): Promise<boolean> => {
    try {
      // Import location service
      const { mobileLocationService } = await import('../services/locationService');

      // Request foreground location permission first
      const foregroundPermission = await mobileLocationService.requestLocationPermission();

      if (foregroundPermission === 'granted') {
        // Now request background location permission
        const backgroundPermission = await mobileLocationService.requestBackgroundLocationPermission();

        if (backgroundPermission === 'granted') {
          console.log('✅ Background location permission granted');
          return true;
        } else if (backgroundPermission === 'denied') {
          console.log('❌ Background location permission denied');
          // Still return true if we have foreground permission
          // User can enable background later from system settings
          return true;
        } else {
          console.log('⏳ Background location permission undetermined');
          return true;
        }
      } else {
        console.log('❌ Foreground location permission not granted:', foregroundPermission);
        return false;
      }
    } catch (error) {
      console.error('Error requesting location permission:', error);
      return false;
    }
  };

  const handlePermissionRequest = async (type: string) => {
    await appSettingsService.triggerHaptic('medium');

    Alert.alert(
      `${type} Permission`,
      `Open device settings to manage ${type.toLowerCase()} permissions for this app.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Open Settings',
          onPress: async () => {
            if (type === 'Location') {
              await appSettingsService.openLocationSettings();
            } else if (type === 'Notification') {
              await appSettingsService.openNotificationSettings();
            } else {
              await appSettingsService.openAppSettings();
            }
          }
        },
      ]
    );
  };

  const handleDarkModeToggle = async () => {
    const newValue = await appSettingsService.toggleDarkMode();
    setSettings(prev => ({ ...prev, darkMode: newValue }));
  };

  const handleSoundEffectsToggle = async () => {
    const newValue = await appSettingsService.toggleSoundEffects();
    setSettings(prev => ({ ...prev, soundEffects: newValue }));
  };

  const handleHapticFeedbackToggle = async () => {
    const newValue = await appSettingsService.toggleHapticFeedback();
    setSettings(prev => ({ ...prev, hapticFeedback: newValue }));
  };

  return (
    <View style={styles.container}>
        
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity
            onPress={() => {
              console.log('🔙 Back button pressed');
              onBack();
            }}
            style={styles.backButton}
          >
            <Text style={styles.backButtonText}>← Back</Text>
          </TouchableOpacity>
          <Text style={styles.title}>Settings</Text>
          <View style={styles.headerSpacer} />
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          
          {/* Notification Distance */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Notification Distance</Text>
            <Text style={styles.sectionDescription}>
              How close you need to be to get location reminders
            </Text>
            
            <View style={styles.distanceOptions}>
              {distanceOptions.map((distance) => (
                <TouchableOpacity
                  key={distance}
                  style={[
                    styles.distanceOption,
                    defaultDistance === distance && styles.distanceOptionActive,
                  ]}
                  onPress={() => onDistanceChange(distance)}
                >
                  <Text
                    style={[
                      styles.distanceOptionText,
                      defaultDistance === distance && styles.distanceOptionTextActive,
                    ]}
                  >
                    {distance}m
                  </Text>
                  <Text
                    style={[
                      styles.distanceOptionDescription,
                      defaultDistance === distance && styles.distanceOptionDescriptionActive,
                    ]}
                  >
                    {distance <= 100 ? 'Very Close' : 
                     distance <= 200 ? 'Close' :
                     distance <= 500 ? 'Nearby' : 'Far'}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {/* Permissions */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Permissions</Text>
            <Text style={styles.sectionDescription}>
              Manage app permissions for optimal functionality
            </Text>
            
            <View style={styles.permissionItem}>
              <View style={styles.permissionInfo}>
                <View style={styles.permissionTitleRow}>
                  <Text style={styles.permissionTitle}>📍 Background Location</Text>
                  {locationPermissionStatus === 'granted' && (
                    <Text style={styles.permissionStatusGranted}>✅</Text>
                  )}
                  {locationPermissionStatus === 'denied' && (
                    <Text style={styles.permissionStatusDenied}>❌</Text>
                  )}
                </View>
                <Text style={styles.permissionDescription}>
                  Get notified when you arrive near your tasks
                  {locationPermissionStatus === 'denied' && ' • Permission required'}
                </Text>
              </View>
              <View style={styles.permissionActions}>
                <Switch
                  value={settings.backgroundLocation}
                  onValueChange={handleBackgroundLocationToggle}
                  trackColor={{ false: '#f3f4f6', true: '#3b82f6' }}
                  thumbColor={settings.backgroundLocation ? '#fff' : '#9ca3af'}
                />
                <TouchableOpacity
                  style={styles.manageButton}
                  onPress={async () => {
                    await handlePermissionRequest('Location');
                    // Refresh permission status after user returns from settings
                    setTimeout(() => {
                      checkLocationPermissionStatus();
                    }, 1000);
                  }}
                >
                  <Text style={styles.manageButtonText}>Settings</Text>
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.permissionItem}>
              <View style={styles.permissionInfo}>
                <Text style={styles.permissionTitle}>🔔 Notifications</Text>
                <Text style={styles.permissionDescription}>
                  Push notifications for task reminders
                </Text>
              </View>
              <View style={styles.permissionActions}>
                <Switch
                  value={settings.notifications}
                  onValueChange={(value) => updateSetting('notifications', value)}
                  trackColor={{ false: '#f3f4f6', true: '#3b82f6' }}
                  thumbColor={settings.notifications ? '#fff' : '#9ca3af'}
                />
                <TouchableOpacity
                  style={styles.manageButton}
                  onPress={() => handlePermissionRequest('Notification')}
                >
                  <Text style={styles.manageButtonText}>Manage</Text>
                </TouchableOpacity>
              </View>
            </View>

            <View style={styles.permissionItem}>
              <View style={styles.permissionInfo}>
                <Text style={styles.permissionTitle}>🎤 Microphone</Text>
                <Text style={styles.permissionDescription}>
                  Voice input for creating tasks
                </Text>
              </View>
              <View style={styles.permissionActions}>
                <Switch
                  value={true}
                  onValueChange={() => {}}
                  trackColor={{ false: '#f3f4f6', true: '#3b82f6' }}
                  thumbColor={'#fff'}
                />
                <TouchableOpacity
                  style={styles.manageButton}
                  onPress={() => handlePermissionRequest('Microphone')}
                >
                  <Text style={styles.manageButtonText}>Manage</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>

          {/* App Preferences */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>App Preferences</Text>

            {onOpenLocationSettings && (
              <TouchableOpacity
                style={styles.preferenceItem}
                onPress={() => {
                  console.log('🔧 Background Location settings pressed');
                  onOpenLocationSettings();
                }}
                activeOpacity={0.7}
              >
                <Text style={styles.preferenceTitle}>📍 Background Location</Text>
                <Text style={styles.preferenceArrow}>›</Text>
              </TouchableOpacity>
            )}

            <View style={styles.preferenceItem}>
              <Text style={styles.preferenceTitle}>🌙 Dark Mode</Text>
              <Switch
                value={settings.darkMode}
                onValueChange={handleDarkModeToggle}
                trackColor={{ false: '#f3f4f6', true: '#3b82f6' }}
                thumbColor={settings.darkMode ? '#fff' : '#9ca3af'}
              />
            </View>

            <View style={styles.preferenceItem}>
              <Text style={styles.preferenceTitle}>🔊 Sound Effects</Text>
              <Switch
                value={settings.soundEffects}
                onValueChange={handleSoundEffectsToggle}
                trackColor={{ false: '#f3f4f6', true: '#3b82f6' }}
                thumbColor={settings.soundEffects ? '#fff' : '#9ca3af'}
              />
            </View>

            <View style={styles.preferenceItem}>
              <Text style={styles.preferenceTitle}>📱 Haptic Feedback</Text>
              <Switch
                value={settings.hapticFeedback}
                onValueChange={handleHapticFeedbackToggle}
                trackColor={{ false: '#f3f4f6', true: '#3b82f6' }}
                thumbColor={settings.hapticFeedback ? '#fff' : '#9ca3af'}
              />
            </View>
          </View>

        </ScrollView>

    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
    paddingTop: Constants.statusBarHeight,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e5e7eb',
  },
  backButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  backButtonText: {
    fontSize: 16,
    color: '#3b82f6',
    fontWeight: '500',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#1f2937',
  },
  headerSpacer: {
    width: 60,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    marginVertical: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#6b7280',
    marginBottom: 16,
  },
  distanceOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  distanceOption: {
    backgroundColor: '#f9fafb',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    alignItems: 'center',
    minWidth: 80,
  },
  distanceOptionActive: {
    backgroundColor: '#dbeafe',
    borderColor: '#3b82f6',
  },
  distanceOptionText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 2,
  },
  distanceOptionTextActive: {
    color: '#1e40af',
  },
  distanceOptionDescription: {
    fontSize: 10,
    color: '#9ca3af',
  },
  distanceOptionDescriptionActive: {
    color: '#3730a3',
  },
  permissionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
  },
  permissionInfo: {
    flex: 1,
  },
  permissionTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1f2937',
    marginBottom: 4,
  },
  permissionDescription: {
    fontSize: 12,
    color: '#6b7280',
  },
  permissionActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  manageButton: {
    backgroundColor: '#f3f4f6',
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  manageButtonText: {
    fontSize: 12,
    color: '#374151',
    fontWeight: '500',
  },
  preferenceItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 4, // Add horizontal padding for better touch area
    borderBottomWidth: 1,
    borderBottomColor: '#f3f4f6',
    minHeight: 48, // Ensure minimum touch target size
  },
  touchablePreference: {
    // Ensure the touchable area is properly defined
    zIndex: 1,
    elevation: 1, // For Android
  },
  preferenceTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1f2937',
  },
  preferenceArrow: {
    fontSize: 20,
    color: '#9ca3af',
  },
  permissionTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  permissionStatusGranted: {
    fontSize: 12,
    color: '#10b981',
  },
  permissionStatusDenied: {
    fontSize: 12,
    color: '#ef4444',
  },
});
