import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Switch,
} from 'react-native';
import { mobileGoogleMapsService } from '../services/googleMapsService';

interface LocationPermissionProps {
  permission: 'granted' | 'denied' | 'prompt';
  onRequestPermission: () => void;
  latitude: number | null;
  longitude: number | null;
  accuracy: number | null;
  error?: string | null;
  isTrackingEnabled: boolean;
  onToggleTracking: (enabled: boolean) => void;
}

export const LocationPermission: React.FC<LocationPermissionProps> = ({
  permission,
  onRequestPermission,
  latitude,
  longitude,
  accuracy,
  error,
  isTrackingEnabled,
  onToggleTracking,
}) => {
  const [locationName, setLocationName] = useState<string | null>(null);
  const [isLoadingLocation, setIsLoadingLocation] = useState(false);

  // Reverse geocode coordinates to get location name
  useEffect(() => {
    if (latitude && longitude) {
      setIsLoadingLocation(true);
      mobileGoogleMapsService.reverseGeocode(latitude, longitude)
        .then((name) => {
          setLocationName(name);
          setIsLoadingLocation(false);
        })
        .catch((error) => {
          console.error('Error reverse geocoding:', error);
          setLocationName(null);
          setIsLoadingLocation(false);
        });
    } else {
      setLocationName(null);
      setIsLoadingLocation(false);
    }
  }, [latitude, longitude]);
  const getStatusIcon = () => {
    if (!isTrackingEnabled) return '⏸️';

    switch (permission) {
      case 'granted':
        return latitude && longitude ? '✅' : '🔄';
      case 'denied':
        return '❌';
      default:
        return '❓';
    }
  };

  const getStatusText = () => {
    if (!isTrackingEnabled) return 'Location Tracking Disabled';

    switch (permission) {
      case 'granted':
        return latitude && longitude ? 'Location Active' : 'Getting Location...';
      case 'denied':
        return 'Location Denied';
      default:
        return 'Location Permission Needed';
    }
  };

  const getStatusColor = () => {
    if (!isTrackingEnabled) return '#6b7280';

    switch (permission) {
      case 'granted':
        return latitude && longitude ? '#10b981' : '#f59e0b';
      case 'denied':
        return '#ef4444';
      default:
        return '#6b7280';
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.card}>
        <View style={styles.header}>
          <Text style={styles.icon}>{getStatusIcon()}</Text>
          <View style={styles.headerText}>
            <Text style={[styles.status, { color: getStatusColor() }]}>
              {getStatusText()}
            </Text>
            <Text style={styles.subtitle}>
              Required for location-based reminders
            </Text>
          </View>
        </View>

        {/* Location Tracking Toggle */}
        <View style={styles.toggleContainer}>
          <View style={styles.toggleInfo}>
            <Text style={styles.toggleLabel}>Location Tracking</Text>
            <Text style={styles.toggleSubtitle}>
              {isTrackingEnabled ? 'Actively monitoring your location' : 'Location tracking is paused'}
            </Text>
          </View>
          <Switch
            value={isTrackingEnabled}
            onValueChange={onToggleTracking}
            trackColor={{ false: '#d1d5db', true: '#3b82f6' }}
            thumbColor={isTrackingEnabled ? '#ffffff' : '#f3f4f6'}
            ios_backgroundColor="#d1d5db"
          />
        </View>

        {error && (
          <View style={styles.errorContainer}>
            <Text style={styles.errorText}>{error}</Text>
          </View>
        )}

        {isTrackingEnabled && latitude && longitude && (
          <View style={styles.locationInfo}>
            <Text style={styles.locationLabel}>Current Location:</Text>
            {isLoadingLocation ? (
              <Text style={styles.locationText}>
                🔄 Getting location name...
              </Text>
            ) : locationName ? (
              <Text style={styles.locationText}>
                📍 {locationName}
              </Text>
            ) : (
              <Text style={styles.locationText}>
                📍 {latitude.toFixed(4)}, {longitude.toFixed(4)}
              </Text>
            )}
            {accuracy && (
              <Text style={styles.accuracyText}>
                🎯 Accuracy: ~{Math.round(accuracy)}m
              </Text>
            )}
          </View>
        )}

        {permission !== 'granted' && (
          <TouchableOpacity
            style={styles.permissionButton}
            onPress={onRequestPermission}
          >
            <Text style={styles.permissionButtonText}>
              {permission === 'denied' ? '⚙️ Open Settings' : '📍 Enable Location'}
            </Text>
          </TouchableOpacity>
        )}

        {permission === 'denied' && (
          <View style={styles.instructionsContainer}>
            <Text style={styles.instructionsTitle}>To enable location:</Text>
            <Text style={styles.instructionsText}>
              1. Go to Settings → Privacy → Location Services{'\n'}
              2. Find "Evelin" in the app list{'\n'}
              3. Select "While Using App" or "Always"
            </Text>
          </View>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 20,
    paddingVertical: 8,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  icon: {
    fontSize: 24,
    marginRight: 12,
  },
  headerText: {
    flex: 1,
  },
  status: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  subtitle: {
    fontSize: 12,
    color: '#6b7280',
  },
  toggleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#f8fafc',
    borderRadius: 8,
    marginBottom: 12,
  },
  toggleInfo: {
    flex: 1,
    marginRight: 12,
  },
  toggleLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 2,
  },
  toggleSubtitle: {
    fontSize: 12,
    color: '#6b7280',
  },
  errorContainer: {
    backgroundColor: '#fef2f2',
    borderRadius: 6,
    padding: 8,
    marginBottom: 12,
  },
  errorText: {
    color: '#dc2626',
    fontSize: 12,
    textAlign: 'center',
  },
  locationInfo: {
    backgroundColor: '#f0f9ff',
    borderRadius: 6,
    padding: 12,
    marginBottom: 12,
  },
  locationLabel: {
    fontSize: 12,
    fontWeight: '600',
    color: '#1e40af',
    marginBottom: 4,
  },
  locationText: {
    fontSize: 14,
    color: '#1f2937',
    fontFamily: 'monospace',
    marginBottom: 2,
  },
  accuracyText: {
    fontSize: 12,
    color: '#6b7280',
  },
  permissionButton: {
    backgroundColor: '#3b82f6',
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: 'center',
    marginBottom: 12,
  },
  permissionButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
  },
  instructionsContainer: {
    backgroundColor: '#f9fafb',
    borderRadius: 6,
    padding: 12,
  },
  instructionsTitle: {
    fontSize: 12,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 6,
  },
  instructionsText: {
    fontSize: 11,
    color: '#6b7280',
    lineHeight: 16,
  },
});
