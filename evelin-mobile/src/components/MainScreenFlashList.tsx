import React, { memo, useCallback, useMemo } from 'react';
import { View, StyleSheet, Text, TouchableOpacity } from 'react-native';
import { Task } from '../types/task';
import { FuturisticVoiceButton } from './FuturisticVoiceButton';
import { ManualTaskInput } from './ManualTaskInput';
import { DistanceSettings } from './DistanceSettings';
import { TaskList } from './TaskList';
import { LocationPermission } from './LocationPermission';
import { BaseFlashList } from './FlashListComponents';

// Define the different types of items that can appear in the main screen
type MainScreenItem =
  | { type: 'location'; data: LocationPermissionData }
  | { type: 'voice'; data: VoiceButtonData }
  | { type: 'manual'; data: ManualInputData }
  | { type: 'distance'; data: DistanceSettingsData }
  | { type: 'tasks'; data: TaskListData };

interface LocationPermissionData {
  permission: string | null;
  onRequestPermission: () => void;
  latitude: number | null;
  longitude: number | null;
  accuracy: number | null;
  error: string | null;
  isTrackingEnabled: boolean;
  onToggleTracking: (enabled: boolean) => void;
}

interface ManualInputData {
  showManualInput: boolean;
  onToggleManualInput: () => void;
  onSubmit: (text: string, location: string, distance: number) => void;
  defaultDistance: number;
}

interface VoiceButtonData {
  isListening: boolean;
  isProcessing: boolean;
  transcript: string;
  isSupported: boolean;
  onStartListening: () => void;
  onStopListening: () => void;
  onTranscriptSubmit: (transcript: string) => void;
  error: string | null;
}

interface DistanceSettingsData {
  defaultDistance: number;
  onDistanceChange: (distance: number) => void;
  isVisible: boolean;
  onToggle: () => void;
}

interface TaskListData {
  tasks: Task[];
  onCompleteTask: (taskId: string) => void;
  onDeleteTask: (taskId: string) => void;
  onEditTask: (taskId: string, updates: Partial<Task>) => void;
  onShowOnMap?: (task: Task) => void;
  onViewAllTasks?: () => void;
  userLocation?: { latitude: number; longitude: number };
  calculateDistance?: (lat1: number, lon1: number, lat2: number, lon2: number) => number;
}

interface MainScreenFlashListProps {
  locationData: LocationPermissionData;
  voiceData: VoiceButtonData;
  manualData: ManualInputData;
  distanceData: DistanceSettingsData;
  taskData: TaskListData;
}

export const MainScreenFlashList: React.FC<MainScreenFlashListProps> = memo(({
  locationData,
  voiceData,
  manualData,
  distanceData,
  taskData,
}) => {
  // Create the data array for FlashList in the correct order
  const mainScreenData = useMemo((): MainScreenItem[] => [
    { type: 'location', data: locationData },
    { type: 'voice', data: voiceData },
    { type: 'manual', data: manualData },
    { type: 'distance', data: distanceData },
    { type: 'tasks', data: taskData },
  ], [locationData, voiceData, manualData, distanceData, taskData]);

  // Render function for each item type
  const renderMainScreenItem = useCallback(({ item }: any) => {
    switch (item.type) {
      case 'location':
        return (
          <LocationPermission
            permission={item.data.permission}
            onRequestPermission={item.data.onRequestPermission}
            latitude={item.data.latitude}
            longitude={item.data.longitude}
            accuracy={item.data.accuracy}
            error={item.data.error}
            isTrackingEnabled={item.data.isTrackingEnabled}
            onToggleTracking={item.data.onToggleTracking}
          />
        );

      case 'voice':
        return (
          <View style={styles.itemContainer}>
            <FuturisticVoiceButton
              isListening={item.data.isListening}
              isProcessing={item.data.isProcessing}
              transcript={item.data.transcript}
              isSupported={item.data.isSupported}
              onStartListening={item.data.onStartListening}
              onStopListening={item.data.onStopListening}
              onTranscriptSubmit={item.data.onTranscriptSubmit}
              error={item.data.error}
            />
          </View>
        );

      case 'manual':
        return (
          <View style={styles.itemContainer}>
            {/* Manual Input Toggle */}
            <View style={styles.inputToggle}>
              <TouchableOpacity
                style={styles.toggleButton}
                onPress={item.data.onToggleManualInput}
              >
                <Text style={styles.toggleButtonText}>
                  {item.data.showManualInput ? '🎤 Switch to Voice' : '⌨️ Manual Input'}
                </Text>
              </TouchableOpacity>
            </View>

            {/* Manual Task Input */}
            {item.data.showManualInput && (
              <ManualTaskInput
                onSubmit={item.data.onSubmit}
                defaultDistance={item.data.defaultDistance}
              />
            )}
          </View>
        );

      case 'distance':
        return (
          <View style={styles.itemContainer}>
            <DistanceSettings
              defaultDistance={item.data.defaultDistance}
              onDistanceChange={item.data.onDistanceChange}
              isVisible={item.data.isVisible}
              onToggle={item.data.onToggle}
            />
          </View>
        );

      case 'tasks':
        return (
          <View style={styles.taskContainer}>
            <TaskList
              tasks={item.data.tasks || []} // Ensure tasks is always an array
              onCompleteTask={item.data.onCompleteTask}
              onDeleteTask={item.data.onDeleteTask}
              onEditTask={item.data.onEditTask}
              onShowOnMap={item.data.onShowOnMap}
              onViewAllTasks={item.data.onViewAllTasks}
              userLocation={item.data.userLocation}
              calculateDistance={item.data.calculateDistance}
              showOnlyActive={true}
              maxTasks={2}
              useScrollView={false} // Important: don't use ScrollView inside FlashList
            />
          </View>
        );

      default:
        return null;
    }
  }, []);

  // Key extractor for FlashList
  const keyExtractor = useCallback((item: MainScreenItem, index: number) => {
    return `${item.type}-${index}`;
  }, []);

  // Get estimated item size based on type
  const getItemType = useCallback((item: MainScreenItem) => {
    return item.type;
  }, []);

  return (
    <BaseFlashList
      data={mainScreenData}
      renderItem={renderMainScreenItem}
      keyExtractor={keyExtractor}
      estimatedItemSize={200} // Average estimated size
      showsVerticalScrollIndicator={false}
      config="MEDIUM_LIST"
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
      getItemType={getItemType}
    />
  );
});

MainScreenFlashList.displayName = 'MainScreenFlashList';

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: 20,
  },
  itemContainer: {
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  taskContainer: {
    flex: 1,
    minHeight: 200, // Ensure tasks have enough space
  },
  inputToggle: {
    alignItems: 'center',
    marginVertical: 10,
  },
  toggleButton: {
    backgroundColor: '#3b82f6',
    borderRadius: 25,
    paddingHorizontal: 20,
    paddingVertical: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  toggleButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },

});
