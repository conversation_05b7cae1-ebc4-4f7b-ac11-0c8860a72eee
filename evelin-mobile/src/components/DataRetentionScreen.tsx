/**
 * Data Retention Settings Screen
 * Granular control over data retention periods and automatic purging
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Switch,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Picker } from '@react-native-picker/picker';
import { privacyService } from '../services/privacyService';
import { 
  LocationPrivacySettings, 
  DataRetentionPeriod,
  DataPurgeLog 
} from '../types/privacy';

interface DataRetentionScreenProps {
  onBack: () => void;
}

const RETENTION_PERIODS: { value: DataRetentionPeriod; label: string }[] = [
  { value: '1_hour', label: '1 Hour' },
  { value: '24_hours', label: '24 Hours' },
  { value: '7_days', label: '7 Days' },
  { value: '30_days', label: '30 Days' },
  { value: '90_days', label: '90 Days' },
  { value: '1_year', label: '1 Year' },
  { value: 'forever', label: 'Keep Forever' },
];

export default function DataRetentionScreen({ onBack }: DataRetentionScreenProps) {
  const [settings, setSettings] = useState<LocationPrivacySettings | null>(null);
  const [purgeLogs, setPurgeLogs] = useState<DataPurgeLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [privacySettings, logs] = await Promise.all([
        privacyService.getPrivacySettings(),
        privacyService.getPurgeLogs(),
      ]);
      setSettings(privacySettings);
      setPurgeLogs(logs);
    } catch (error) {
      console.error('Error loading data retention settings:', error);
      Alert.alert('Error', 'Failed to load settings');
    } finally {
      setLoading(false);
    }
  };

  const updateRetentionPeriod = async (
    dataType: keyof LocationPrivacySettings['dataRetention'], 
    period: DataRetentionPeriod
  ) => {
    if (!settings) return;

    try {
      setSaving(true);
      const newSettings = {
        ...settings,
        dataRetention: {
          ...settings.dataRetention,
          [dataType]: period,
        },
      };
      await privacyService.updatePrivacySettings(newSettings);
      setSettings(newSettings);
    } catch (error) {
      console.error('Error updating retention period:', error);
      Alert.alert('Error', 'Failed to update retention period');
    } finally {
      setSaving(false);
    }
  };

  const updateAutoPurgeSettings = async (updates: Partial<LocationPrivacySettings['autoPurge']>) => {
    if (!settings) return;

    try {
      setSaving(true);
      const newSettings = {
        ...settings,
        autoPurge: {
          ...settings.autoPurge,
          ...updates,
        },
      };
      await privacyService.updatePrivacySettings(newSettings);
      setSettings(newSettings);
    } catch (error) {
      console.error('Error updating auto-purge settings:', error);
      Alert.alert('Error', 'Failed to update auto-purge settings');
    } finally {
      setSaving(false);
    }
  };

  const triggerManualPurge = () => {
    Alert.alert(
      'Manual Data Purge',
      'This will immediately apply your current retention policies and delete old data. Continue?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Purge Now',
          style: 'destructive',
          onPress: async () => {
            try {
              setSaving(true);
              await privacyService.enforceRetentionPolicies();
              await loadData(); // Refresh logs
              Alert.alert('Success', 'Data purge completed successfully');
            } catch (error) {
              console.error('Error during manual purge:', error);
              Alert.alert('Error', 'Failed to purge data');
            } finally {
              setSaving(false);
            }
          },
        },
      ]
    );
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const formatDataSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (loading || !settings) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#007AFF" />
        <Text style={styles.loadingText}>Loading retention settings...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={onBack} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color="#007AFF" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Data Retention</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Retention Periods */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Data Retention Periods</Text>
          <Text style={styles.sectionDescription}>
            Choose how long different types of data should be kept before automatic deletion.
          </Text>

          <View style={styles.retentionItem}>
            <Text style={styles.retentionLabel}>Location History</Text>
            <Text style={styles.retentionDescription}>Your movement and location tracking data</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={settings.dataRetention.locationHistory}
                onValueChange={(value) => updateRetentionPeriod('locationHistory', value)}
                style={styles.picker}
                enabled={!saving}
              >
                {RETENTION_PERIODS.map((period) => (
                  <Picker.Item key={period.value} label={period.label} value={period.value} />
                ))}
              </Picker>
            </View>
          </View>

          <View style={styles.retentionItem}>
            <Text style={styles.retentionLabel}>Visited Places</Text>
            <Text style={styles.retentionDescription}>Places you've been to and saved</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={settings.dataRetention.visitedPlaces}
                onValueChange={(value) => updateRetentionPeriod('visitedPlaces', value)}
                style={styles.picker}
                enabled={!saving}
              >
                {RETENTION_PERIODS.map((period) => (
                  <Picker.Item key={period.value} label={period.label} value={period.value} />
                ))}
              </Picker>
            </View>
          </View>

          <View style={styles.retentionItem}>
            <Text style={styles.retentionLabel}>Search History</Text>
            <Text style={styles.retentionDescription}>Your location searches and queries</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={settings.dataRetention.searchHistory}
                onValueChange={(value) => updateRetentionPeriod('searchHistory', value)}
                style={styles.picker}
                enabled={!saving}
              >
                {RETENTION_PERIODS.map((period) => (
                  <Picker.Item key={period.value} label={period.label} value={period.value} />
                ))}
              </Picker>
            </View>
          </View>

          <View style={styles.retentionItem}>
            <Text style={styles.retentionLabel}>Task Locations</Text>
            <Text style={styles.retentionDescription}>Locations associated with your tasks</Text>
            <View style={styles.pickerContainer}>
              <Picker
                selectedValue={settings.dataRetention.taskLocations}
                onValueChange={(value) => updateRetentionPeriod('taskLocations', value)}
                style={styles.picker}
                enabled={!saving}
              >
                {RETENTION_PERIODS.map((period) => (
                  <Picker.Item key={period.value} label={period.label} value={period.value} />
                ))}
              </Picker>
            </View>
          </View>
        </View>

        {/* Automatic Purging */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Automatic Purging</Text>
          <Text style={styles.sectionDescription}>
            Configure when and how often old data should be automatically deleted.
          </Text>

          <View style={styles.settingRow}>
            <View style={styles.settingInfo}>
              <Text style={styles.settingLabel}>Enable Auto-Purge</Text>
              <Text style={styles.settingDescription}>Automatically delete old data based on retention periods</Text>
            </View>
            <Switch
              value={settings.autoPurge.enabled}
              onValueChange={(value) => updateAutoPurgeSettings({ enabled: value })}
              disabled={saving}
            />
          </View>

          {settings.autoPurge.enabled && (
            <>
              <View style={styles.settingRow}>
                <View style={styles.settingInfo}>
                  <Text style={styles.settingLabel}>Purge Schedule</Text>
                  <Text style={styles.settingDescription}>How often to run automatic cleanup</Text>
                </View>
                <View style={styles.pickerContainer}>
                  <Picker
                    selectedValue={settings.autoPurge.schedule}
                    onValueChange={(value) => updateAutoPurgeSettings({ schedule: value })}
                    style={styles.smallPicker}
                    enabled={!saving}
                  >
                    <Picker.Item label="Daily" value="daily" />
                    <Picker.Item label="Weekly" value="weekly" />
                    <Picker.Item label="Monthly" value="monthly" />
                  </Picker>
                </View>
              </View>

              <View style={styles.settingRow}>
                <View style={styles.settingInfo}>
                  <Text style={styles.settingLabel}>Notify Before Purge</Text>
                  <Text style={styles.settingDescription}>Show notification before deleting data</Text>
                </View>
                <Switch
                  value={settings.autoPurge.notifyBeforePurge}
                  onValueChange={(value) => updateAutoPurgeSettings({ notifyBeforePurge: value })}
                  disabled={saving}
                />
              </View>

              <View style={styles.settingRow}>
                <View style={styles.settingInfo}>
                  <Text style={styles.settingLabel}>Purge on Low Storage</Text>
                  <Text style={styles.settingDescription}>Delete old data when device storage is low</Text>
                </View>
                <Switch
                  value={settings.autoPurge.purgeOnLowStorage}
                  onValueChange={(value) => updateAutoPurgeSettings({ purgeOnLowStorage: value })}
                  disabled={saving}
                />
              </View>

              {settings.autoPurge.lastPurgeDate && (
                <View style={styles.infoCard}>
                  <Text style={styles.infoLabel}>Last Automatic Purge:</Text>
                  <Text style={styles.infoValue}>
                    {formatDate(settings.autoPurge.lastPurgeDate)}
                  </Text>
                </View>
              )}
            </>
          )}

          <TouchableOpacity 
            style={[styles.actionButton, saving && styles.disabledButton]}
            onPress={triggerManualPurge}
            disabled={saving}
          >
            <Ionicons name="refresh-outline" size={20} color="#007AFF" />
            <Text style={styles.actionButtonText}>
              {saving ? 'Purging...' : 'Run Manual Purge Now'}
            </Text>
          </TouchableOpacity>
        </View>

        {/* Purge History */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Purge History</Text>
          <Text style={styles.sectionDescription}>
            Recent data deletion activities and their details.
          </Text>

          {purgeLogs.length === 0 ? (
            <Text style={styles.emptyText}>No purge history available</Text>
          ) : (
            purgeLogs.slice(0, 10).map((log) => (
              <View key={log.id} style={styles.logItem}>
                <View style={styles.logHeader}>
                  <Text style={styles.logDate}>{formatDate(log.timestamp)}</Text>
                  <Text style={styles.logReason}>{log.reason.replace(/_/g, ' ')}</Text>
                </View>
                <Text style={styles.logDetails}>
                  Deleted {log.itemsDeleted} items • Freed {formatDataSize(log.sizeFreed)}
                </Text>
                {log.dataTypes.length > 0 && (
                  <Text style={styles.logDataTypes}>
                    Types: {log.dataTypes.join(', ').replace(/_/g, ' ')}
                  </Text>
                )}
              </View>
            ))
          )}
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f9fa',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: '#666',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e1e5e9',
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#1a1a1a',
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    marginVertical: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 8,
  },
  sectionDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 20,
    lineHeight: 20,
  },
  retentionItem: {
    marginBottom: 24,
    paddingBottom: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  retentionLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 4,
  },
  retentionDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 12,
  },
  pickerContainer: {
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e1e5e9',
  },
  picker: {
    height: 50,
  },
  smallPicker: {
    height: 40,
    width: 120,
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  settingInfo: {
    flex: 1,
    marginRight: 16,
  },
  settingLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1a1a1a',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    color: '#666',
  },
  infoCard: {
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 16,
    marginTop: 16,
  },
  infoLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  infoValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1a1a1a',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 16,
    marginTop: 16,
    borderWidth: 1,
    borderColor: '#e1e5e9',
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#007AFF',
    marginLeft: 8,
  },
  disabledButton: {
    opacity: 0.6,
  },
  emptyText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
    fontStyle: 'italic',
    paddingVertical: 20,
  },
  logItem: {
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 16,
    marginBottom: 12,
  },
  logHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  logDate: {
    fontSize: 14,
    fontWeight: '600',
    color: '#1a1a1a',
  },
  logReason: {
    fontSize: 12,
    color: '#666',
    textTransform: 'capitalize',
  },
  logDetails: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
  },
  logDataTypes: {
    fontSize: 12,
    color: '#888',
    textTransform: 'capitalize',
  },
});
