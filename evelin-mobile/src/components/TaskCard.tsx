import React, { memo, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Linking,
} from 'react-native';
import { Task } from '../types/task';

interface TaskCardProps {
  task: Task;
  onComplete: (taskId: string) => void;
  onDelete: (taskId: string) => void;
  onEdit: (taskId: string, updates: Partial<Task>) => void;
  onShowOnMap?: (task: Task) => void;
  distance?: number;
}

export const TaskCard: React.FC<TaskCardProps> = memo(({
  task,
  onComplete,
  onDelete,
  onEdit,
  onShowOnMap,
  distance,
}) => {

  // Memoized calculations
  const isNearby = useMemo(() =>
    distance !== undefined && distance < (task.notificationDistance || 200),
    [distance, task.notificationDistance]
  );

  const formattedDistance = useMemo(() => {
    if (distance === undefined) return null;
    return distance < 1000
      ? `${Math.round(distance)}m away`
      : `${(distance / 1000).toFixed(1)}km away`;
  }, [distance]);

  const priorityColor = useMemo(() => {
    // Since Task doesn't have priority, use category for color coding
    switch (task.category) {
      case 'urgent': return '#ef4444';
      case 'work': return '#f59e0b';
      case 'personal': return '#10b981';
      default: return '#6b7280';
    }
  }, [task.category]);

  const statusIcon = useMemo(() => {
    return task.completed ? '✅' : isNearby ? '📍' : '⏰';
  }, [task.completed, isNearby]);

  // Memoized callbacks
  const handleComplete = useCallback(() => {
    onComplete(task.id);
  }, [onComplete, task.id]);

  const handleDelete = useCallback(() => {
    Alert.alert(
      'Delete Task',
      'Are you sure you want to delete this task?',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Delete', style: 'destructive', onPress: () => onDelete(task.id) },
      ]
    );
  }, [onDelete, task.id]);

  const handleEdit = useCallback(() => {
    // This would typically open an edit modal
    console.log('Edit task:', task.id);
  }, [task.id]);

  const openInMaps = useCallback(() => {
    if (task.coordinates) {
      const { lat, lng } = task.coordinates;
      const placeName = task.location || 'location';
      const url = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}&travelmode=walking`;
      Linking.openURL(url);
    }
  }, [task.coordinates, task.location]);

  const handleShowOnMap = useCallback(() => {
    if (onShowOnMap) {
      onShowOnMap(task);
    } else {
      openInMaps();
    }
  }, [onShowOnMap, task, openInMaps]);

  return (
    <View style={[styles.container, task.completed && styles.completedContainer]}>
      <View style={styles.header}>
        <Text style={[styles.text, task.completed && styles.completedText]}>
          {task.text}
        </Text>
        <View style={styles.actions}>
          <TouchableOpacity
            style={[styles.button, styles.completeButton]}
            onPress={() => onComplete(task.id)}
          >
            <Text style={styles.buttonText}>
              {task.completed ? '↶' : '✓'}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.button, styles.editButton]}
            onPress={() => onEdit(task.id, { text: task.text })}
          >
            <Text style={styles.buttonText}>✎</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.button, styles.deleteButton]}
            onPress={handleDelete}
          >
            <Text style={styles.buttonText}>🗑</Text>
          </TouchableOpacity>
        </View>
      </View>

      {task.location && (
        <View style={styles.locationContainer}>
          <Text style={styles.locationText}>📍 {task.location}</Text>
          {task.notificationDistance && (
            <Text style={styles.distanceText}>
              Notify at {task.notificationDistance}m
            </Text>
          )}
          {task.coordinates && (
            <TouchableOpacity style={styles.mapButton} onPress={openInMaps}>
              <Text style={styles.mapButtonText}>🗺 Show on Map</Text>
            </TouchableOpacity>
          )}
        </View>
      )}

      {task.category && (
        <View style={styles.categoryContainer}>
          <Text style={styles.categoryText}>{task.category}</Text>
        </View>
      )}

      {isNearby && (
        <View style={styles.nearbyContainer}>
          <Text style={styles.nearbyText}>
            📍 {task.location} is nearby!
          </Text>
          {distance !== undefined && (
            <Text style={styles.nearbyDistance}>
              {distance < 1000 
                ? `${Math.round(distance)}m away` 
                : `${(distance / 1000).toFixed(1)}km away`}
            </Text>
          )}
          {task.coordinates && (
            <TouchableOpacity style={styles.navigateButton} onPress={openInMaps}>
              <Text style={styles.navigateButtonText}>🧭 Navigate</Text>
            </TouchableOpacity>
          )}
        </View>
      )}

      <Text style={styles.timestamp}>
        {new Date(task.createdAt).toLocaleDateString()} at{' '}
        {new Date(task.createdAt).toLocaleTimeString()}
      </Text>
    </View>
  );
}, (prevProps, nextProps) => {
  // Only re-render if essential props change
  return (
    prevProps.task.id === nextProps.task.id &&
    prevProps.task.completed === nextProps.task.completed &&
    prevProps.task.text === nextProps.task.text &&
    prevProps.task.category === nextProps.task.category &&
    prevProps.distance === nextProps.distance &&
    prevProps.task.notificationDistance === nextProps.task.notificationDistance
  );
});

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginVertical: 8,
    marginHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  completedContainer: {
    backgroundColor: '#f8f9fa',
    opacity: 0.7,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  text: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    flex: 1,
    marginRight: 12,
  },
  completedText: {
    textDecorationLine: 'line-through',
    color: '#666',
  },
  actions: {
    flexDirection: 'row',
    gap: 8,
  },
  button: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  completeButton: {
    backgroundColor: '#10b981',
  },
  editButton: {
    backgroundColor: '#6b7280',
  },
  deleteButton: {
    backgroundColor: '#ef4444',
  },
  buttonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  locationContainer: {
    backgroundColor: '#f3f4f6',
    borderRadius: 8,
    padding: 12,
    marginVertical: 8,
  },
  locationText: {
    fontSize: 14,
    color: '#374151',
    marginBottom: 4,
  },
  distanceText: {
    fontSize: 12,
    color: '#6b7280',
    marginBottom: 8,
  },
  mapButton: {
    backgroundColor: '#3b82f6',
    borderRadius: 6,
    paddingVertical: 6,
    paddingHorizontal: 12,
    alignSelf: 'flex-start',
  },
  mapButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '500',
  },
  categoryContainer: {
    marginVertical: 4,
  },
  categoryText: {
    fontSize: 12,
    color: '#6b7280',
    backgroundColor: '#e5e7eb',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
  },
  nearbyContainer: {
    backgroundColor: '#dbeafe',
    borderRadius: 8,
    padding: 12,
    marginVertical: 8,
    borderWidth: 1,
    borderColor: '#3b82f6',
  },
  nearbyText: {
    fontSize: 14,
    color: '#1e40af',
    fontWeight: '500',
    marginBottom: 4,
  },
  nearbyDistance: {
    fontSize: 12,
    color: '#3730a3',
    marginBottom: 8,
  },
  navigateButton: {
    backgroundColor: '#3b82f6',
    borderRadius: 6,
    paddingVertical: 6,
    paddingHorizontal: 12,
    alignSelf: 'flex-start',
  },
  navigateButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '500',
  },
  timestamp: {
    fontSize: 12,
    color: '#9ca3af',
    marginTop: 8,
  },
});
