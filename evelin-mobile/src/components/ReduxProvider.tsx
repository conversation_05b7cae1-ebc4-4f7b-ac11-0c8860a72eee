import React, { useEffect, useState } from 'react';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { View, Text, StyleSheet, ActivityIndicator } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { store, persistor } from '../store';
import { useAppDispatch, useAppSelector } from '../store/hooks';


// Loading component for Redux persistence
const PersistenceLoadingScreen: React.FC = () => {
  return (
    <View style={styles.loadingContainer}>
      <LinearGradient
        colors={['#667eea', '#764ba2']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.loadingGradient}
      >
        <View style={styles.loadingContent}>
          <View style={styles.logoContainer}>
            <Text style={styles.logo}>✨</Text>
          </View>
          <ActivityIndicator size="large" color="#ffffff" style={styles.spinner} />
          <Text style={styles.loadingText}>Initializing App...</Text>
          <Text style={styles.loadingSubtext}>Loading your data</Text>
        </View>
      </LinearGradient>
    </View>
  );
};

// Redux initialization component
const ReduxInitializer: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const dispatch = useAppDispatch();
  const [initialized, setInitialized] = useState(false);
  const startTime = React.useRef(Date.now());

  useEffect(() => {
    const initializeRedux = async () => {
      try {
        // Record startup time
        const startupTime = Date.now() - startTime.current;
        
        // Initialize other services if needed
        console.log('🏪 Redux store initialized successfully');
        console.log(`⏱️ Startup time: ${startupTime}ms`);
        
        setInitialized(true);
      } catch (error) {
        console.error('❌ Redux initialization failed:', error);
        setInitialized(true); // Still allow app to continue
      }
    };

    initializeRedux();
  }, [dispatch]);

  if (!initialized) {
    return <PersistenceLoadingScreen />;
  }

  return <>{children}</>;
};

// Error boundary for Redux
class ReduxErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('Redux Error Boundary caught an error:', error, errorInfo);
    
    // In a real app, you might want to log this to a crash reporting service
    if (__DEV__) {
      console.error('Error details:', errorInfo);
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <View style={styles.errorContainer}>
          <LinearGradient
            colors={['#ef4444', '#dc2626']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            style={styles.errorGradient}
          >
            <View style={styles.errorContent}>
              <Text style={styles.errorIcon}>⚠️</Text>
              <Text style={styles.errorTitle}>Something went wrong</Text>
              <Text style={styles.errorMessage}>
                The app encountered an error and needs to restart.
              </Text>
              {__DEV__ && this.state.error && (
                <Text style={styles.errorDetails}>
                  {this.state.error.message}
                </Text>
              )}
            </View>
          </LinearGradient>
        </View>
      );
    }

    return this.props.children;
  }
}

// Main Redux Provider component
export const ReduxProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <Provider store={store}>
      <ReduxErrorBoundary>
        <PersistGate 
          loading={<PersistenceLoadingScreen />} 
          persistor={persistor}
        >
          <ReduxInitializer>
            {children}
          </ReduxInitializer>
        </PersistGate>
      </ReduxErrorBoundary>
    </Provider>
  );
};



const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
  },
  loadingGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContent: {
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  logoContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 30,
  },
  logo: {
    fontSize: 36,
  },
  spinner: {
    marginBottom: 20,
  },
  loadingText: {
    fontSize: 20,
    color: '#ffffff',
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
  loadingSubtext: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.8)',
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
  },
  errorGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContent: {
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  errorIcon: {
    fontSize: 48,
    marginBottom: 20,
  },
  errorTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#ffffff',
    marginBottom: 16,
    textAlign: 'center',
  },
  errorMessage: {
    fontSize: 16,
    color: 'rgba(255, 255, 255, 0.9)',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 20,
  },
  errorDetails: {
    fontSize: 12,
    color: 'rgba(255, 255, 255, 0.7)',
    textAlign: 'center',
    fontFamily: 'monospace',
  },

});
