import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { Task } from '../types/task';

interface ManualTaskInputProps {
  onSubmit: (text: string, location: string, distance: number) => void;
  defaultDistance: number;
}

export const ManualTaskInput: React.FC<ManualTaskInputProps> = ({
  onSubmit,
  defaultDistance,
}) => {
  const [text, setText] = useState('');
  const [location, setLocation] = useState('');
  const [category, setCategory] = useState('');
  const [notificationDistance, setNotificationDistance] = useState(defaultDistance.toString());

  const handleSubmit = () => {
    if (!text.trim()) return;

    onSubmit(
      text.trim(),
      location.trim() || '',
      parseInt(notificationDistance) || defaultDistance
    );

    // Reset form
    setText('');
    setLocation('');
    setCategory('');
    setNotificationDistance(defaultDistance.toString());
  };

  return (
    <View style={styles.container}>
      <View style={styles.card}>
        <Text style={styles.title}>⌨️ Manual Task Input</Text>
        
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Task Description *</Text>
          <TextInput
            style={styles.textInput}
            value={text}
            onChangeText={setText}
            placeholder="e.g., Buy milk, Call dentist, Pick up package"
            multiline
            numberOfLines={2}
          />
        </View>

        <View style={styles.inputGroup}>
          <Text style={styles.label}>Location (optional)</Text>
          <TextInput
            style={styles.textInput}
            value={location}
            onChangeText={setLocation}
            placeholder="e.g., Lidl, Bank, Post Office"
          />
        </View>

        <View style={styles.row}>
          <View style={[styles.inputGroup, styles.flex1]}>
            <Text style={styles.label}>Category (optional)</Text>
            <TextInput
              style={styles.textInput}
              value={category}
              onChangeText={setCategory}
              placeholder="e.g., Shopping, Work"
            />
          </View>

          <View style={[styles.inputGroup, styles.flex1, styles.marginLeft]}>
            <Text style={styles.label}>Distance (m)</Text>
            <TextInput
              style={styles.textInput}
              value={notificationDistance}
              onChangeText={setNotificationDistance}
              placeholder="200"
              keyboardType="numeric"
            />
          </View>
        </View>

        <TouchableOpacity
          style={[styles.submitButton, !text.trim() && styles.submitButtonDisabled]}
          onPress={handleSubmit}
          disabled={!text.trim()}
        >
          <Text style={styles.submitButtonText}>➕ Add Task</Text>
        </TouchableOpacity>

        <Text style={styles.hint}>
          💡 Tip: Include location for automatic reminders when you're nearby
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 20,
    paddingVertical: 8,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1f2937',
    marginBottom: 16,
    textAlign: 'center',
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 6,
  },
  textInput: {
    borderWidth: 1,
    borderColor: '#d1d5db',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
    color: '#1f2937',
    backgroundColor: '#fff',
  },
  row: {
    flexDirection: 'row',
  },
  flex1: {
    flex: 1,
  },
  marginLeft: {
    marginLeft: 12,
  },
  submitButton: {
    backgroundColor: '#10b981',
    borderRadius: 8,
    paddingVertical: 14,
    alignItems: 'center',
    marginBottom: 12,
  },
  submitButtonDisabled: {
    backgroundColor: '#9ca3af',
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  hint: {
    fontSize: 12,
    color: '#6b7280',
    textAlign: 'center',
    fontStyle: 'italic',
  },
});
