import React, { memo, useCallback, useMemo, forwardRef } from 'react';
import { FlashList, ListRenderItem } from '@shopify/flash-list';
import { View, Text, StyleSheet, RefreshControl } from 'react-native';
import { Task } from '../types/task';

// FlashList configuration constants optimized for different use cases
export const FLASH_LIST_CONFIGS = {
  SMALL_LIST: {
    estimatedItemSize: 80,
    initialNumToRender: 10,
    maxToRenderPerBatch: 5,
    windowSize: 10,
    removeClippedSubviews: false,
  },
  MEDIUM_LIST: {
    estimatedItemSize: 100,
    initialNumToRender: 15,
    maxToRenderPerBatch: 10,
    windowSize: 15,
    removeClippedSubviews: true,
  },
  LARGE_LIST: {
    estimatedItemSize: 120,
    initialNumToRender: 20,
    maxToRenderPerBatch: 15,
    windowSize: 20,
    removeClippedSubviews: true,
  },
  TASK_LIST: {
    estimatedItemSize: 140, // TaskCard height
    initialNumToRender: 10,
    maxToRenderPerBatch: 8,
    windowSize: 12,
    removeClippedSubviews: true,
  },
};

// Base FlashList wrapper with common optimizations
interface BaseFlashListProps<T> {
  data: T[];
  renderItem: any;
  keyExtractor?: (item: T, index: number) => string;
  estimatedItemSize?: number;
  ListHeaderComponent?: React.ComponentType<any> | React.ReactElement | null;
  ListFooterComponent?: React.ComponentType<any> | React.ReactElement | null;
  ListEmptyComponent?: React.ComponentType<any> | React.ReactElement | null;
  ItemSeparatorComponent?: React.ComponentType<any> | null;
  refreshControl?: any;
  onEndReached?: () => void;
  onEndReachedThreshold?: number;
  showsVerticalScrollIndicator?: boolean;
  contentContainerStyle?: any;
  style?: any;
  config?: keyof typeof FLASH_LIST_CONFIGS;
  extraData?: any;
  getItemType?: (item: any) => string;
}

export const BaseFlashList = memo(forwardRef<any, BaseFlashListProps<any>>(({
  data = [], // Provide default empty array
  renderItem,
  keyExtractor,
  estimatedItemSize,
  ListHeaderComponent,
  ListFooterComponent,
  ListEmptyComponent,
  ItemSeparatorComponent,
  refreshControl,
  onEndReached,
  onEndReachedThreshold = 0.5,
  showsVerticalScrollIndicator = false,
  contentContainerStyle,
  style,
  config = 'MEDIUM_LIST',
  extraData,
  getItemType,
}, ref) => {
  const flashListConfig = FLASH_LIST_CONFIGS[config];

  const finalEstimatedItemSize = estimatedItemSize || flashListConfig.estimatedItemSize;

  // Create config without estimatedItemSize to avoid duplication
  const { estimatedItemSize: configEstimatedItemSize, ...restConfig } = flashListConfig;
  
  // Memoized key extractor
  const memoizedKeyExtractor = useCallback((item: any, index: number) => {
    if (keyExtractor) {
      return keyExtractor(item, index);
    }
    return item.id?.toString() || index.toString();
  }, [keyExtractor]);

  // Performance optimized render item
  const optimizedRenderItem = useCallback((info: any) => {
    return renderItem(info);
  }, [renderItem]);

  // Filter contentContainerStyle to only include supported properties
  const filteredContentContainerStyle = useMemo(() => {
    if (!contentContainerStyle) return undefined;

    const supportedKeys = [
      'paddingTop', 'paddingBottom', 'paddingLeft', 'paddingRight',
      'paddingHorizontal', 'paddingVertical', 'padding',
      'backgroundColor'
    ];

    const filtered: any = {};
    Object.keys(contentContainerStyle).forEach(key => {
      if (supportedKeys.includes(key)) {
        filtered[key] = contentContainerStyle[key];
      }
    });

    return Object.keys(filtered).length > 0 ? filtered : undefined;
  }, [contentContainerStyle]);

  // FlashList doesn't support style prop, wrap in View if style is provided
  const flashListComponent = (
    <FlashList
      ref={ref}
      data={data}
      renderItem={optimizedRenderItem}
      keyExtractor={memoizedKeyExtractor}
      estimatedItemSize={finalEstimatedItemSize}
      ListHeaderComponent={ListHeaderComponent}
      ListFooterComponent={ListFooterComponent}
      ListEmptyComponent={ListEmptyComponent}
      ItemSeparatorComponent={ItemSeparatorComponent}
      onEndReached={onEndReached}
      onEndReachedThreshold={onEndReachedThreshold}
      showsVerticalScrollIndicator={showsVerticalScrollIndicator}
      contentContainerStyle={filteredContentContainerStyle}
      extraData={extraData}
      {...restConfig}
    />
  );

  // Wrap in View if style is provided
  if (style) {
    return <View style={style}>{flashListComponent}</View>;
  }

  return flashListComponent;
}));

BaseFlashList.displayName = 'BaseFlashList';

// Specialized Task FlashList component
interface TaskFlashListProps {
  tasks: Task[];
  renderTask: any;
  keyExtractor?: (item: Task, index: number) => string;
  ListHeaderComponent?: React.ComponentType<any> | React.ReactElement | null;
  ListFooterComponent?: React.ComponentType<any> | React.ReactElement | null;
  ListEmptyComponent?: React.ComponentType<any> | React.ReactElement | null;
  ItemSeparatorComponent?: React.ComponentType<any> | null;
  refreshControl?: any;
  onEndReached?: () => void;
  onEndReachedThreshold?: number;
  showsVerticalScrollIndicator?: boolean;
  contentContainerStyle?: any;
  style?: any;
  extraData?: any;
  config?: keyof typeof FLASH_LIST_CONFIGS;
}

export const TaskFlashList = memo(forwardRef<any, TaskFlashListProps>(({
  tasks = [], // Provide default empty array
  renderTask,
  keyExtractor,
  ListHeaderComponent,
  ListFooterComponent,
  ListEmptyComponent,
  ItemSeparatorComponent,
  refreshControl,
  onEndReached,
  onEndReachedThreshold = 0.5,
  showsVerticalScrollIndicator = false,
  contentContainerStyle,
  style,
  extraData,
  config = 'TASK_LIST',
}, ref) => {
  // Default key extractor for tasks
  const defaultKeyExtractor = useCallback((item: Task, index: number) => {
    return item.id || index.toString();
  }, []);

  return (
    <BaseFlashList
      ref={ref}
      data={tasks}
      renderItem={renderTask}
      keyExtractor={keyExtractor || defaultKeyExtractor}
      config={config}
      ListHeaderComponent={ListHeaderComponent}
      ListFooterComponent={ListFooterComponent}
      ListEmptyComponent={ListEmptyComponent}
      ItemSeparatorComponent={ItemSeparatorComponent}
      refreshControl={refreshControl}
      onEndReached={onEndReached}
      onEndReachedThreshold={onEndReachedThreshold}
      showsVerticalScrollIndicator={showsVerticalScrollIndicator}
      contentContainerStyle={contentContainerStyle}
      style={style}
      extraData={extraData}
    />
  );
}));

TaskFlashList.displayName = 'TaskFlashList';

// Empty list component optimized for FlashList
export const FlashListEmptyComponent = memo(({ 
  message = 'No items found',
  style 
}: { 
  message?: string;
  style?: any;
}) => (
  <View style={[styles.emptyContainer, style]}>
    <Text style={styles.emptyText}>{message}</Text>
  </View>
));

FlashListEmptyComponent.displayName = 'FlashListEmptyComponent';

// Loading footer component for FlashList
export const FlashListFooterComponent = memo(({ 
  loading = false, 
  hasMore = false,
  style 
}: { 
  loading?: boolean; 
  hasMore?: boolean;
  style?: any;
}) => {
  if (!loading && !hasMore) return null;
  
  return (
    <View style={[styles.footerContainer, style]}>
      {loading && <Text style={styles.footerText}>Loading...</Text>}
      {!loading && hasMore && (
        <Text style={[styles.footerText, styles.footerSubText]}>
          Pull to load more
        </Text>
      )}
    </View>
  );
});

FlashListFooterComponent.displayName = 'FlashListFooterComponent';

// Item separator optimized for FlashList
export const FlashListItemSeparator = memo(({ 
  height = 1, 
  color = '#e5e7eb',
  marginHorizontal = 16,
  style 
}: {
  height?: number;
  color?: string;
  marginHorizontal?: number;
  style?: any;
}) => (
  <View 
    style={[
      {
        height,
        backgroundColor: color,
        marginLeft: marginHorizontal,
        marginRight: marginHorizontal,
      },
      style
    ]} 
  />
));

FlashListItemSeparator.displayName = 'FlashListItemSeparator';

const styles = StyleSheet.create({
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 40,
    paddingHorizontal: 20,
  },
  emptyText: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
  },
  footerContainer: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 14,
    color: '#374151',
  },
  footerSubText: {
    color: '#6b7280',
  },
});
