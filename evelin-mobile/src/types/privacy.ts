/**
 * Privacy Control Types
 * Comprehensive privacy settings for location data and user control
 */

export type DataRetentionPeriod = 
  | '1_hour'
  | '24_hours' 
  | '7_days'
  | '30_days'
  | '90_days'
  | '1_year'
  | 'forever';

export type LocationDataType = 
  | 'current_location'
  | 'location_history'
  | 'visited_places'
  | 'search_history'
  | 'geofences'
  | 'route_data'
  | 'task_locations';

export type DataPurgeReason = 
  | 'user_request'
  | 'retention_policy'
  | 'privacy_setting_change'
  | 'account_deletion'
  | 'storage_limit'
  | 'security_requirement';

export interface LocationPrivacySettings {
  // Core location data controls
  locationDataCollection: {
    enabled: boolean;
    highAccuracyMode: boolean;
    backgroundTracking: boolean;
    onlyWhenAppActive: boolean;
  };

  // Data retention policies
  dataRetention: {
    locationHistory: DataRetentionPeriod;
    visitedPlaces: DataRetentionPeriod;
    searchHistory: DataRetentionPeriod;
    taskLocations: DataRetentionPeriod;
    routeData: DataRetentionPeriod;
  };

  // Data sharing and usage
  dataUsage: {
    allowAnalytics: boolean;
    allowPersonalization: boolean;
    allowLocationSharing: boolean;
    allowThirdPartyIntegration: boolean;
    anonymizeData: boolean;
  };

  // Storage limits
  storageLimits: {
    maxLocationHistoryEntries: number;
    maxVisitedPlaces: number;
    maxSearchHistoryEntries: number;
    maxGeofences: number;
    enableAutomaticCleanup: boolean;
  };

  // Advanced privacy controls
  advancedControls: {
    encryptLocationData: boolean;
    requireBiometricForLocationAccess: boolean;
    disableLocationInScreenshots: boolean;
    clearDataOnAppUninstall: boolean;
    enablePrivacyMode: boolean; // Minimal data collection
  };

  // Automatic purging settings
  autoPurge: {
    enabled: boolean;
    schedule: 'daily' | 'weekly' | 'monthly';
    lastPurgeDate: string | null;
    purgeOnLowStorage: boolean;
    notifyBeforePurge: boolean;
  };
}

export interface DataPurgeLog {
  id: string;
  timestamp: string;
  dataTypes: LocationDataType[];
  reason: DataPurgeReason;
  itemsDeleted: number;
  sizeFreed: number; // in bytes
  userInitiated: boolean;
  details?: string;
}

export interface LocationDataSummary {
  totalEntries: number;
  oldestEntry: string | null;
  newestEntry: string | null;
  storageSize: number; // in bytes
  dataTypes: {
    [K in LocationDataType]: {
      count: number;
      size: number;
      oldestEntry: string | null;
    };
  };
}

export interface PrivacyAuditLog {
  id: string;
  timestamp: string;
  action: 'data_access' | 'data_export' | 'data_deletion' | 'settings_change' | 'permission_change';
  details: string;
  userAgent?: string;
  ipAddress?: string;
  success: boolean;
}

export interface DataExportRequest {
  id: string;
  requestDate: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  dataTypes: LocationDataType[];
  format: 'json' | 'csv' | 'xml';
  downloadUrl?: string;
  expiresAt?: string;
  fileSize?: number;
}

// Default privacy settings (privacy-first approach)
export const DEFAULT_PRIVACY_SETTINGS: LocationPrivacySettings = {
  locationDataCollection: {
    enabled: false, // Opt-in by default
    highAccuracyMode: false,
    backgroundTracking: false,
    onlyWhenAppActive: true,
  },
  dataRetention: {
    locationHistory: '7_days',
    visitedPlaces: '30_days',
    searchHistory: '24_hours',
    taskLocations: '30_days',
    routeData: '7_days',
  },
  dataUsage: {
    allowAnalytics: false,
    allowPersonalization: true,
    allowLocationSharing: false,
    allowThirdPartyIntegration: false,
    anonymizeData: true,
  },
  storageLimits: {
    maxLocationHistoryEntries: 100,
    maxVisitedPlaces: 50,
    maxSearchHistoryEntries: 20,
    maxGeofences: 10,
    enableAutomaticCleanup: true,
  },
  advancedControls: {
    encryptLocationData: true,
    requireBiometricForLocationAccess: false,
    disableLocationInScreenshots: false,
    clearDataOnAppUninstall: true,
    enablePrivacyMode: false,
  },
  autoPurge: {
    enabled: true,
    schedule: 'weekly',
    lastPurgeDate: null,
    purgeOnLowStorage: true,
    notifyBeforePurge: true,
  },
};

// Utility types for privacy operations
export interface PrivacyOperation {
  type: 'purge' | 'export' | 'anonymize' | 'encrypt';
  dataTypes: LocationDataType[];
  options?: Record<string, any>;
}

export interface PrivacyCompliance {
  gdprCompliant: boolean;
  ccpaCompliant: boolean;
  coppaCompliant: boolean;
  lastComplianceCheck: string;
  requiredActions: string[];
}
