import { Platform } from 'react-native';
import { SecureStorageService, SecureStorageKeys } from '../services/secureStorageService';
import { EncryptionService } from '../services/encryptionService';

/**
 * Security utilities for the application
 */
export class SecurityUtils {
  /**
   * Initialize all security services
   */
  static async initializeSecurity(): Promise<void> {
    try {
      console.log('🔐 Initializing security services...');
      
      // Initialize encryption service
      await EncryptionService.initialize();
      
      // Check SecureStore availability
      const isSecureStoreAvailable = await SecureStorageService.isAvailable();
      console.log(`🔐 SecureStore available: ${isSecureStoreAvailable}`);
      
      // Initialize API keys if not already stored
      await this.initializeAPIKeys();
      
      console.log('✅ Security services initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize security services:', error);
      throw error;
    }
  }

  /**
   * Initialize API keys in secure storage
   */
  private static async initializeAPIKeys(): Promise<void> {
    try {
      // Check if API keys are already stored
      const existingConfig = await SecureStorageService.getJSON('api_config');
      
      if (!existingConfig) {
        // Store API configuration from environment variables
        const apiConfig = {
          supabaseUrl: process.env.EXPO_PUBLIC_SUPABASE_URL,
          supabaseAnonKey: process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY,
          googleMapsApiKey: process.env.EXPO_PUBLIC_GOOGLE_MAPS_API_KEY,
          timestamp: Date.now(),
        };
        
        await SecureStorageService.setJSON('api_config', apiConfig);
        console.log('🔐 API configuration stored securely');
      }
    } catch (error) {
      console.error('❌ Failed to initialize API keys:', error);
    }
  }

  /**
   * Validate data integrity
   */
  static validateDataIntegrity(data: any, expectedChecksum?: string): boolean {
    try {
      if (!expectedChecksum) return true;
      
      const dataString = JSON.stringify(data);
      const actualChecksum = this.generateChecksum(dataString);
      
      return actualChecksum === expectedChecksum;
    } catch (error) {
      console.error('❌ Data integrity validation failed:', error);
      return false;
    }
  }

  /**
   * Generate checksum for data integrity
   */
  static generateChecksum(data: string): string {
    // Simple checksum implementation
    let hash = 0;
    for (let i = 0; i < data.length; i++) {
      const char = data.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(16);
  }

  /**
   * Sanitize sensitive data for logging
   */
  static sanitizeForLogging(data: any): any {
    if (typeof data !== 'object' || data === null) {
      return data;
    }

    const sensitiveKeys = [
      'password', 'token', 'key', 'secret', 'auth', 'credential',
      'jwt', 'refresh', 'access', 'api', 'private', 'secure'
    ];

    const sanitized = { ...data };

    Object.keys(sanitized).forEach(key => {
      const lowerKey = key.toLowerCase();
      const isSensitive = sensitiveKeys.some(sensitiveKey => 
        lowerKey.includes(sensitiveKey)
      );

      if (isSensitive && typeof sanitized[key] === 'string') {
        sanitized[key] = '***REDACTED***';
      } else if (typeof sanitized[key] === 'object') {
        sanitized[key] = this.sanitizeForLogging(sanitized[key]);
      }
    });

    return sanitized;
  }

  /**
   * Check if app is running in secure environment
   */
  static isSecureEnvironment(): boolean {
    // Check for development/debugging indicators
    const isDevelopment = __DEV__;
    const isDebuggingEnabled = typeof atob !== 'undefined';
    
    // In production, we want to ensure the app is not being debugged
    if (!isDevelopment && isDebuggingEnabled) {
      console.warn('⚠️ App may be running in debug mode in production');
      return false;
    }

    return true;
  }

  /**
   * Generate secure random string
   */
  static generateSecureRandomString(length: number = 32): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    return result;
  }

  /**
   * Validate API key format
   */
  static validateAPIKey(key: string, type: 'supabase' | 'google' | 'jwt'): boolean {
    if (!key || typeof key !== 'string') {
      return false;
    }

    switch (type) {
      case 'supabase':
        // Supabase keys are typically JWT tokens
        return key.split('.').length === 3;
      
      case 'google':
        // Google API keys are typically 39 characters
        return key.length >= 35 && key.length <= 45;
      
      case 'jwt':
        // JWT tokens have 3 parts separated by dots
        return key.split('.').length === 3;
      
      default:
        return key.length > 10; // Basic length check
    }
  }

  /**
   * Mask sensitive data for display
   */
  static maskSensitiveData(data: string, visibleChars: number = 4): string {
    if (!data || data.length <= visibleChars * 2) {
      return '***';
    }

    const start = data.substring(0, visibleChars);
    const end = data.substring(data.length - visibleChars);
    const middle = '*'.repeat(Math.min(data.length - visibleChars * 2, 10));

    return `${start}${middle}${end}`;
  }

  /**
   * Check for security threats
   */
  static async performSecurityCheck(): Promise<{
    isSecure: boolean;
    threats: string[];
    warnings: string[];
  }> {
    const threats: string[] = [];
    const warnings: string[] = [];

    try {
      // Check if running in secure environment
      if (!this.isSecureEnvironment()) {
        threats.push('App may be running in debug mode');
      }

      // Check if encryption is available
      if (!EncryptionService.isAvailable()) {
        threats.push('Encryption service not available');
      }

      // Check if SecureStore is available
      const isSecureStoreAvailable = await SecureStorageService.isAvailable();
      if (!isSecureStoreAvailable) {
        warnings.push('SecureStore not available on this platform');
      }

      // Check for development mode
      if (__DEV__) {
        warnings.push('Running in development mode');
      }

      // Check platform security
      if (Platform.OS === 'web') {
        warnings.push('Web platform has limited security features');
      }

      return {
        isSecure: threats.length === 0,
        threats,
        warnings,
      };
    } catch (error) {
      console.error('❌ Security check failed:', error);
      return {
        isSecure: false,
        threats: ['Security check failed'],
        warnings: [],
      };
    }
  }

  /**
   * Clear all sensitive data (for logout/reset)
   */
  static async clearAllSensitiveData(): Promise<void> {
    try {
      console.log('🧹 Clearing all sensitive data...');
      
      // Clear encryption key
      await EncryptionService.clearKey();
      
      // Clear authentication data
      const authKeys = [
        SecureStorageKeys.JWT_TOKEN,
        SecureStorageKeys.REFRESH_TOKEN,
        SecureStorageKeys.USER_SESSION,
      ];
      
      for (const key of authKeys) {
        try {
          await SecureStorageService.removeItem(key);
        } catch (error) {
          console.warn(`⚠️ Failed to clear ${key}:`, error);
        }
      }
      
      console.log('✅ Sensitive data cleared');
    } catch (error) {
      console.error('❌ Failed to clear sensitive data:', error);
      throw error;
    }
  }
}

/**
 * Security configuration
 */
export const SecurityConfig = {
  // Encryption settings
  encryption: {
    enabled: process.env.EXPO_PUBLIC_ENABLE_ENCRYPTION === 'true',
    algorithm: 'AES-256-CBC',
    keyRotationInterval: 30 * 24 * 60 * 60 * 1000, // 30 days
  },
  
  // Authentication settings
  auth: {
    requireBiometric: process.env.EXPO_PUBLIC_REQUIRE_BIOMETRIC_AUTH === 'true',
    tokenRefreshInterval: 15 * 60 * 1000, // 15 minutes
    maxRetryAttempts: 3,
  },
  
  // Security checks
  checks: {
    performOnStartup: true,
    performPeriodically: true,
    checkInterval: 60 * 60 * 1000, // 1 hour
  },
  
  // Logging
  logging: {
    sanitizeSensitiveData: true,
    logSecurityEvents: true,
    logLevel: process.env.EXPO_PUBLIC_LOG_LEVEL || 'info',
  },
} as const;

/**
 * Security event logger
 */
export class SecurityLogger {
  static logSecurityEvent(event: string, details?: any): void {
    if (!SecurityConfig.logging.logSecurityEvents) {
      return;
    }

    const sanitizedDetails = SecurityConfig.logging.sanitizeSensitiveData
      ? SecurityUtils.sanitizeForLogging(details)
      : details;

    console.log(`🔐 Security Event: ${event}`, sanitizedDetails);
  }

  static logSecurityWarning(warning: string, details?: any): void {
    const sanitizedDetails = SecurityConfig.logging.sanitizeSensitiveData
      ? SecurityUtils.sanitizeForLogging(details)
      : details;

    console.warn(`⚠️ Security Warning: ${warning}`, sanitizedDetails);
  }

  static logSecurityError(error: string, details?: any): void {
    const sanitizedDetails = SecurityConfig.logging.sanitizeSensitiveData
      ? SecurityUtils.sanitizeForLogging(details)
      : details;

    console.error(`❌ Security Error: ${error}`, sanitizedDetails);
  }
}
