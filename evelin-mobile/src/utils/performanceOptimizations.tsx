import React, { memo, useCallback, useMemo, useRef, useEffect } from 'react';
import { Task } from '../types/task';

/**
 * Performance optimization utilities for React Native components
 */

// Deep comparison utility for complex objects
export const deepEqual = (obj1: any, obj2: any): boolean => {
  if (obj1 === obj2) return true;
  
  if (obj1 == null || obj2 == null) return false;
  
  if (typeof obj1 !== 'object' || typeof obj2 !== 'object') return false;
  
  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);
  
  if (keys1.length !== keys2.length) return false;
  
  for (const key of keys1) {
    if (!keys2.includes(key)) return false;
    if (!deepEqual(obj1[key], obj2[key])) return false;
  }
  
  return true;
};

// Shallow comparison for arrays
export const shallowEqualArray = (arr1: any[], arr2: any[]): boolean => {
  if (arr1.length !== arr2.length) return false;
  return arr1.every((item, index) => item === arr2[index]);
};

// Custom memo with deep comparison option
export const memoWithDeepCompare = <P extends object>(
  Component: React.ComponentType<P>,
  compareProps?: (prevProps: P, nextProps: P) => boolean
) => {
  return memo(Component, compareProps || ((prevProps, nextProps) => deepEqual(prevProps, nextProps)));
};

// Performance monitoring hook
export const useRenderCount = (componentName: string) => {
  const renderCount = useRef(0);
  
  useEffect(() => {
    renderCount.current += 1;
    if (__DEV__) {
      console.log(`🔄 ${componentName} rendered ${renderCount.current} times`);
    }
  });
  
  return renderCount.current;
};

// Debounced callback hook
export const useDebouncedCallback = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number,
  deps: React.DependencyList
): T => {
  const timeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);
  
  return useCallback(
    ((...args: Parameters<T>) => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      timeoutRef.current = setTimeout(() => {
        callback(...args);
      }, delay);
    }) as T,
    [callback, delay, ...deps]
  );
};

// Throttled callback hook
export const useThrottledCallback = <T extends (...args: any[]) => any>(
  callback: T,
  delay: number,
  deps: React.DependencyList
): T => {
  const lastCallRef = useRef<number>(0);
  
  return useCallback(
    ((...args: Parameters<T>) => {
      const now = Date.now();
      if (now - lastCallRef.current >= delay) {
        lastCallRef.current = now;
        callback(...args);
      }
    }) as T,
    [callback, delay, ...deps]
  );
};

// Memoized task filtering
export const useFilteredTasks = (
  tasks: Task[],
  filters: {
    status?: 'active' | 'completed' | 'all';
    search?: string;
    location?: { latitude: number; longitude: number };
    maxDistance?: number;
  }
) => {
  return useMemo(() => {
    let filtered = [...tasks];
    
    // Filter by status
    if (filters.status && filters.status !== 'all') {
      filtered = filtered.filter(task => 
        filters.status === 'active' ? !task.completed : task.completed
      );
    }
    
    // Filter by search term
    if (filters.search && filters.search.trim()) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(task =>
        task.text.toLowerCase().includes(searchLower) ||
        task.location?.toLowerCase().includes(searchLower)
      );
    }
    
    // Filter by location proximity
    if (filters.location && filters.maxDistance) {
      filtered = filtered.filter(task => {
        if (!task.coordinates) return true;

        const distance = calculateDistance(
          filters.location!.latitude,
          filters.location!.longitude,
          task.coordinates.lat,
          task.coordinates.lng
        );

        return distance <= filters.maxDistance!;
      });
    }
    
    return filtered;
  }, [tasks, filters.status, filters.search, filters.location, filters.maxDistance]);
};

// Memoized task sorting
export const useSortedTasks = (
  tasks: Task[],
  sortBy: 'date' | 'priority' | 'distance' | 'alphabetical',
  userLocation?: { latitude: number; longitude: number }
) => {
  return useMemo(() => {
    const sorted = [...tasks];
    
    switch (sortBy) {
      case 'date':
        return sorted.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
      
      case 'priority':
        // Since Task interface doesn't have priority, sort by category instead
        return sorted.sort((a, b) =>
          (a.category || '').localeCompare(b.category || '')
        );
      
      case 'distance':
        if (!userLocation) return sorted;
        return sorted.sort((a, b) => {
          const distanceA = a.coordinates
            ? calculateDistance(
                userLocation.latitude,
                userLocation.longitude,
                a.coordinates.lat,
                a.coordinates.lng
              )
            : Infinity;

          const distanceB = b.coordinates
            ? calculateDistance(
                userLocation.latitude,
                userLocation.longitude,
                b.coordinates.lat,
                b.coordinates.lng
              )
            : Infinity;

          return distanceA - distanceB;
        });

      case 'alphabetical':
        return sorted.sort((a, b) => a.text.localeCompare(b.text));
      
      default:
        return sorted;
    }
  }, [tasks, sortBy, userLocation]);
};

// Calculate distance between two coordinates
const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
  const R = 6371; // Earth's radius in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c * 1000; // Convert to meters
};

// Performance metrics hook


// Virtualization helpers
export const getItemLayout = (data: any[] | null | undefined, index: number, itemHeight: number) => ({
  length: itemHeight,
  offset: itemHeight * index,
  index,
});

export const keyExtractor = (item: any, index: number): string => {
  return item.id?.toString() || index.toString();
};

// Optimized list item separator
export const ListItemSeparator = memo(() => (
  <div style={{ height: 1, backgroundColor: '#e5e7eb', marginLeft: 16, marginRight: 16 }} />
));

// Empty list component
export const EmptyListComponent = memo(({ message = 'No items found' }: { message?: string }) => (
  <div style={{
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 40,
    paddingBottom: 40
  }}>
    <span style={{ fontSize: 16, color: '#6b7280', textAlign: 'center' }}>
      {message}
    </span>
  </div>
));

// List footer component with loading state
export const ListFooterComponent = memo(({ 
  loading = false, 
  hasMore = false 
}: { 
  loading?: boolean; 
  hasMore?: boolean; 
}) => {
  if (!loading && !hasMore) return null;
  
  return (
    <div style={{
      paddingTop: 20,
      paddingBottom: 20,
      alignItems: 'center'
    }}>
      {loading && <span>Loading...</span>}
      {!loading && hasMore && <span style={{ color: '#6b7280' }}>Pull to load more</span>}
    </div>
  );
});

// Performance optimization constants optimized for FlashList
export const PERFORMANCE_CONSTANTS = {
  DEBOUNCE_DELAY: 300,
  THROTTLE_DELAY: 100,
  VIRTUALIZATION_THRESHOLD: 15, // FlashList is more efficient, lower threshold
  ITEM_HEIGHT: 80, // Kept for backward compatibility
  ESTIMATED_ITEM_SIZE: 120, // FlashList uses estimated size
  TASK_CARD_HEIGHT: 140, // Specific height for TaskCard components
  INITIAL_NUM_TO_RENDER: 10,
  MAX_TO_RENDER_PER_BATCH: 8, // Optimized for FlashList
  WINDOW_SIZE: 12, // Slightly larger for FlashList
  UPDATE_CELLS_BATCH_PERIOD: 50,
  SEPARATOR_HEIGHT: 1,
  MAX_CACHED_ITEMS: 150, // FlashList can handle more cached items
} as const;
