import { Task } from '../types/task';
import { aiLocationService } from '../services/aiLocationService';

// Location keywords and their categories
const locationKeywords = {
  'lidl': { category: 'grocery', searchTerms: ['lidl'] },
  'tesco': { category: 'grocery', searchTerms: ['tesco'] },
  'asda': { category: 'grocery', searchTerms: ['asda'] },
  'sainsburys': { category: 'grocery', searchTerms: ['sainsburys', 'sainsbury'] },
  'morrisons': { category: 'grocery', searchTerms: ['morrisons'] },
  'aldi': { category: 'grocery', searchTerms: ['aldi'] },
  'waitrose': { category: 'grocery', searchTerms: ['waitrose'] },
  
  'pharmacy': { category: 'pharmacy', searchTerms: ['pharmacy', 'chemist', 'boots', 'superdrug'] },
  'boots': { category: 'pharmacy', searchTerms: ['boots'] },
  'superdrug': { category: 'pharmacy', searchTerms: ['superdrug'] },
  
  'petrol': { category: 'fuel', searchTerms: ['petrol station', 'gas station', 'shell', 'bp', 'esso', 'texaco'] },
  'shell': { category: 'fuel', searchTerms: ['shell'] },
  'bp': { category: 'fuel', searchTerms: ['bp'] },
  
  'bank': { category: 'finance', searchTerms: ['bank', 'atm', 'cash machine', 'barclays', 'lloyds', 'hsbc', 'natwest'] },
  'barclays': { category: 'finance', searchTerms: ['barclays'] },
  'lloyds': { category: 'finance', searchTerms: ['lloyds'] },
  'hsbc': { category: 'finance', searchTerms: ['hsbc'] },
  
  'post office': { category: 'postal', searchTerms: ['post office', 'royal mail'] },
  
  'gym': { category: 'fitness', searchTerms: ['gym', 'fitness', 'puregym', 'david lloyd'] },
  
  'restaurant': { category: 'food', searchTerms: ['restaurant', 'cafe', 'mcdonald', 'kfc', 'subway', 'starbucks', 'costa'] },
  'mcdonalds': { category: 'food', searchTerms: ['mcdonald', 'mcdonalds'] },
  'starbucks': { category: 'food', searchTerms: ['starbucks'] },
  'costa': { category: 'food', searchTerms: ['costa'] },
};

export const parseTaskFromSpeech = async (transcript: string, defaultRadius: number = 500): Promise<Partial<Task>> => {
  console.log('🎯 NEW INTELLIGENT PARSING - Processing:', transcript, 'with radius:', defaultRadius);

  // Extract specific place name if mentioned (e.g., "Buy toothpaste from Lidl")
  const lowerTranscript = transcript.toLowerCase();
  let specificPlace = '';
  let searchRadius = defaultRadius;

  // Check for specific store mentions
  const storePatterns = [
    /from\s+(\w+)/i,
    /at\s+(\w+)/i,
    /\b(lidl|tesco|asda|sainsburys|morrisons|aldi|waitrose|biedronka|żabka|mrówka|psb)\b/i
  ];

  for (const pattern of storePatterns) {
    const match = transcript.match(pattern);
    if (match) {
      specificPlace = match[1] || match[0];
      break;
    }
  }

  if (specificPlace) {
    console.log(`🎯 Looking for specific place: ${specificPlace} within ${searchRadius}m`);

    try {
      // Use the specific place finder for more accurate results
      const specificResult = await intelligentLocationService.findSpecificPlace(specificPlace, searchRadius);

      if (specificResult) {
        console.log('✅ Found specific place:', specificResult);

        return {
          id: Math.random().toString(36).substring(2) + Date.now().toString(36),
          text: transcript,
          location: specificResult.name,
          category: specificResult.category,
          completed: false,
          createdAt: new Date(),
          coordinates: specificResult.coordinates,
          notificationDistance: searchRadius,
        };
      }
    } catch (error) {
      console.warn(`Could not find specific place "${specificPlace}", will create task without exact coordinates:`, error);
      // Continue to create task without coordinates
    }
  }

  try {
    // Fallback to AI-powered intelligent search
    const locationSuggestions = await aiLocationService.getLocationSuggestions(transcript, searchRadius);

    if (locationSuggestions.length > 0) {
      const bestMatch = locationSuggestions[0];
      console.log('✅ Best location match:', bestMatch);

      return {
        id: Math.random().toString(36).substring(2) + Date.now().toString(36),
        text: transcript,
        location: bestMatch.name,
        category: bestMatch.category,
        completed: false,
        createdAt: new Date(),
        coordinates: bestMatch.coordinates,
        notificationDistance: searchRadius,
      };
    }
  } catch (error) {
    console.warn('Could not find location suggestions, will create basic task:', error);
    // Continue to create basic task
  }

  // Fallback to basic parsing if no location matches found
  console.log('⚠️ No location matches found, falling back to basic parsing');
  return parseTaskBasic(transcript);
};

// Fallback basic parsing function
const parseTaskBasic = (transcript: string): Partial<Task> => {
  const lowerTranscript = transcript.toLowerCase();

  // Find location matches
  let detectedLocation = '';
  let detectedCategory = '';

  for (const [location, data] of Object.entries(locationKeywords)) {
    for (const term of data.searchTerms) {
      if (lowerTranscript.includes(term)) {
        detectedLocation = location;
        detectedCategory = data.category;
        break;
      }
    }
    if (detectedLocation) break;
  }

  // If no specific location found, try to detect general categories
  if (!detectedLocation) {
    if (lowerTranscript.includes('medicine') || lowerTranscript.includes('pills') || lowerTranscript.includes('prescription')) {
      detectedCategory = 'pharmacy';
      detectedLocation = 'pharmacy';
    } else if (lowerTranscript.includes('food') || lowerTranscript.includes('grocery') || lowerTranscript.includes('shopping')) {
      detectedCategory = 'grocery';
      detectedLocation = 'grocery store';
    } else if (lowerTranscript.includes('fuel') || lowerTranscript.includes('petrol') || lowerTranscript.includes('gas')) {
      detectedCategory = 'fuel';
      detectedLocation = 'petrol station';
    } else if (lowerTranscript.includes('money') || lowerTranscript.includes('cash') || lowerTranscript.includes('withdraw')) {
      detectedCategory = 'finance';
      detectedLocation = 'bank';
    }
  }

  return {
    id: Math.random().toString(36).substring(2) + Date.now().toString(36),
    text: transcript,
    location: detectedLocation || undefined,
    category: detectedCategory || undefined,
    completed: false,
    createdAt: new Date(),
  };
};

export const getLocationSearchTerm = (task: Task): string => {
  if (!task.location) return '';
  
  const locationData = locationKeywords[task.location as keyof typeof locationKeywords];
  if (locationData) {
    return locationData.searchTerms[0];
  }
  
  return task.location;
};

export const getCategoryDisplayName = (category: string): string => {
  const categoryNames: Record<string, string> = {
    grocery: 'Grocery Store',
    pharmacy: 'Pharmacy',
    fuel: 'Petrol Station',
    finance: 'Bank/ATM',
    postal: 'Post Office',
    fitness: 'Gym',
    food: 'Restaurant/Cafe',
  };
  
  return categoryNames[category] || category;
};