/**
 * Memory Management Utility
 * Helps prevent memory leaks and optimize performance in React Native
 */

import { AppState, AppStateStatus } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';

interface MemoryStats {
  timestamp: number;
  jsHeapSizeUsed?: number;
  jsHeapSizeTotal?: number;
  jsHeapSizeLimit?: number;
}

interface CleanupTask {
  id: string;
  name: string;
  cleanup: () => Promise<void> | void;
  priority: 'low' | 'medium' | 'high' | 'critical';
}

class MemoryManager {
  private cleanupTasks: Map<string, CleanupTask> = new Map();
  private memoryStats: MemoryStats[] = [];
  private appStateSubscription: any = null;
  private memoryMonitorInterval: NodeJS.Timeout | null = null;
  private isInitialized = false;
  private lastCleanupTime = 0;
  private readonly MAX_MEMORY_STATS = 50; // Keep last 50 memory readings
  private readonly CLEANUP_INTERVAL = 300000; // 5 minutes
  private readonly MEMORY_MONITOR_INTERVAL = 60000; // 1 minute

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Set up app state monitoring
      this.appStateSubscription = AppState.addEventListener('change', (nextAppState: AppStateStatus) => {
        this.handleAppStateChange(nextAppState);
      });

      // Start memory monitoring
      this.startMemoryMonitoring();

      this.isInitialized = true;
      console.log('✅ Memory manager initialized');
    } catch (error) {
      console.error('❌ Failed to initialize memory manager:', error);
    }
  }

  registerCleanupTask(task: CleanupTask): void {
    this.cleanupTasks.set(task.id, task);
    console.log(`📝 Registered cleanup task: ${task.name} (${task.priority})`);
  }

  unregisterCleanupTask(taskId: string): void {
    const task = this.cleanupTasks.get(taskId);
    if (task) {
      this.cleanupTasks.delete(taskId);
      console.log(`🗑️ Unregistered cleanup task: ${task.name}`);
    }
  }

  private startMemoryMonitoring(): void {
    this.memoryMonitorInterval = setInterval(() => {
      this.collectMemoryStats();
    }, this.MEMORY_MONITOR_INTERVAL);
  }

  private collectMemoryStats(): void {
    try {
      const stats: MemoryStats = {
        timestamp: Date.now(),
      };

      // Collect memory stats if available (mainly for development)
      if (__DEV__ && (global as any).performance && (global as any).performance.memory) {
        const memory = (global as any).performance.memory;
        stats.jsHeapSizeUsed = memory.usedJSHeapSize;
        stats.jsHeapSizeTotal = memory.totalJSHeapSize;
        stats.jsHeapSizeLimit = memory.jsHeapSizeLimit;
      }

      this.memoryStats.push(stats);

      // Keep only recent stats
      if (this.memoryStats.length > this.MAX_MEMORY_STATS) {
        this.memoryStats = this.memoryStats.slice(-this.MAX_MEMORY_STATS);
      }

      // Check if cleanup is needed
      this.checkMemoryPressure();
    } catch (error) {
      console.error('Error collecting memory stats:', error);
    }
  }

  private checkMemoryPressure(): void {
    if (this.memoryStats.length < 5) return; // Need some history

    const recent = this.memoryStats.slice(-5);
    const hasMemoryData = recent.some(stat => stat.jsHeapSizeUsed !== undefined);

    if (hasMemoryData) {
      const avgUsed = recent.reduce((sum, stat) => sum + (stat.jsHeapSizeUsed || 0), 0) / recent.length;
      const avgTotal = recent.reduce((sum, stat) => sum + (stat.jsHeapSizeTotal || 0), 0) / recent.length;
      
      const memoryUsageRatio = avgUsed / avgTotal;
      
      if (memoryUsageRatio > 0.8) { // Using more than 80% of heap
        console.warn('⚠️ High memory usage detected, triggering cleanup');
        this.performCleanup('high');
      } else if (memoryUsageRatio > 0.6) { // Using more than 60% of heap
        this.performCleanup('medium');
      }
    }

    // Time-based cleanup
    const timeSinceLastCleanup = Date.now() - this.lastCleanupTime;
    if (timeSinceLastCleanup > this.CLEANUP_INTERVAL) {
      this.performCleanup('low');
    }
  }

  private handleAppStateChange(nextAppState: AppStateStatus): void {
    if (nextAppState === 'background') {
      console.log('📱 App backgrounded - performing memory cleanup');
      this.performCleanup('medium');
    } else if (nextAppState === 'active') {
      console.log('📱 App foregrounded - collecting memory stats');
      this.collectMemoryStats();
    }
  }

  async performCleanup(priority: 'low' | 'medium' | 'high' | 'critical' = 'medium'): Promise<void> {
    console.log(`🧹 Performing ${priority} priority memory cleanup`);
    
    try {
      const priorityOrder = ['critical', 'high', 'medium', 'low'];
      const maxPriorityIndex = priorityOrder.indexOf(priority);
      
      const tasksToRun = Array.from(this.cleanupTasks.values())
        .filter(task => priorityOrder.indexOf(task.priority) <= maxPriorityIndex)
        .sort((a, b) => priorityOrder.indexOf(a.priority) - priorityOrder.indexOf(b.priority));

      let completedTasks = 0;
      for (const task of tasksToRun) {
        try {
          await task.cleanup();
          completedTasks++;
          console.log(`✅ Completed cleanup task: ${task.name}`);
        } catch (error) {
          console.error(`❌ Failed cleanup task: ${task.name}`, error);
        }
      }

      // Force garbage collection if available (development only)
      if (__DEV__ && global.gc) {
        global.gc();
        console.log('🗑️ Forced garbage collection');
      }

      this.lastCleanupTime = Date.now();
      console.log(`✅ Memory cleanup completed (${completedTasks}/${tasksToRun.length} tasks)`);
    } catch (error) {
      console.error('❌ Error during memory cleanup:', error);
    }
  }

  getMemoryStats(): {
    current: MemoryStats | null;
    history: MemoryStats[];
    averageUsage: number;
    peakUsage: number;
    cleanupTasksCount: number;
    lastCleanupTime: number;
  } {
    const current = this.memoryStats.length > 0 ? this.memoryStats[this.memoryStats.length - 1] : null;
    const usageValues = this.memoryStats
      .map(stat => stat.jsHeapSizeUsed)
      .filter(usage => usage !== undefined) as number[];
    
    const averageUsage = usageValues.length > 0 
      ? usageValues.reduce((sum, usage) => sum + usage, 0) / usageValues.length 
      : 0;
    
    const peakUsage = usageValues.length > 0 ? Math.max(...usageValues) : 0;

    return {
      current,
      history: [...this.memoryStats],
      averageUsage,
      peakUsage,
      cleanupTasksCount: this.cleanupTasks.size,
      lastCleanupTime: this.lastCleanupTime,
    };
  }

  async cleanup(): Promise<void> {
    console.log('🧹 Cleaning up memory manager');
    
    try {
      // Perform final cleanup
      await this.performCleanup('critical');
      
      // Clear intervals
      if (this.memoryMonitorInterval) {
        clearInterval(this.memoryMonitorInterval);
        this.memoryMonitorInterval = null;
      }
      
      // Remove app state listener
      if (this.appStateSubscription) {
        this.appStateSubscription.remove();
        this.appStateSubscription = null;
      }
      
      // Clear data
      this.cleanupTasks.clear();
      this.memoryStats = [];
      this.isInitialized = false;
      
      console.log('✅ Memory manager cleanup completed');
    } catch (error) {
      console.error('❌ Error during memory manager cleanup:', error);
    }
  }

  // Utility method to create cleanup tasks for common scenarios
  static createLocationCleanupTask(): CleanupTask {
    return {
      id: 'location-cleanup',
      name: 'Location Services Cleanup',
      priority: 'medium',
      cleanup: async () => {
        // Clear any cached location data
        try {
          await AsyncStorage.removeItem('cached_location_data');
          console.log('🗑️ Cleared cached location data');
        } catch (error) {
          console.warn('Warning: Could not clear cached location data:', error);
        }
      },
    };
  }

  static createOfflineDataCleanupTask(): CleanupTask {
    return {
      id: 'offline-data-cleanup',
      name: 'Offline Data Cleanup',
      priority: 'low',
      cleanup: async () => {
        // Clean up old offline data
        try {
          const keys = await AsyncStorage.getAllKeys();
          const oldDataKeys = keys.filter(key => 
            key.startsWith('offline_') && 
            key.includes('_temp_') // Temporary offline data
          );
          
          if (oldDataKeys.length > 0) {
            await AsyncStorage.multiRemove(oldDataKeys);
            console.log(`🗑️ Cleared ${oldDataKeys.length} temporary offline data entries`);
          }
        } catch (error) {
          console.warn('Warning: Could not clear offline data:', error);
        }
      },
    };
  }

  static createImageCacheCleanupTask(): CleanupTask {
    return {
      id: 'image-cache-cleanup',
      name: 'Image Cache Cleanup',
      priority: 'low',
      cleanup: async () => {
        // This would integrate with image caching libraries
        console.log('🗑️ Image cache cleanup (placeholder)');
      },
    };
  }
}

export const memoryManager = new MemoryManager();
export { MemoryManager, CleanupTask, MemoryStats };
