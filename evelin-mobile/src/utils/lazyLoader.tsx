import React, { Suspense, ComponentType } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import Constants from 'expo-constants';

const { width, height } = Dimensions.get('window');

// Loading component with beautiful animation
const LoadingScreen: React.FC<{ message?: string }> = ({ message = 'Loading...' }) => {
  return (
    <View style={styles.loadingContainer}>
      <LinearGradient
        colors={['#667eea', '#764ba2']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.loadingGradient}
      >
        <View style={styles.loadingContent}>
          <View style={styles.logoContainer}>
            <Text style={styles.logo}>✨</Text>
          </View>
          <ActivityIndicator size="large" color="#ffffff" style={styles.spinner} />
          <Text style={styles.loadingText}>{message}</Text>
          <View style={styles.loadingBar}>
            <View style={styles.loadingBarFill} />
          </View>
        </View>
      </LinearGradient>
    </View>
  );
};

// Error boundary for lazy loaded components
class LazyErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: ComponentType },
  { hasError: boolean; error?: Error }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    console.error('Lazy loading error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      const FallbackComponent = this.props.fallback;
      if (FallbackComponent) {
        return <FallbackComponent />;
      }
      
      return (
        <View style={styles.errorContainer}>
          <Text style={styles.errorTitle}>Oops! Something went wrong</Text>
          <Text style={styles.errorMessage}>
            Failed to load this screen. Please try again.
          </Text>
        </View>
      );
    }

    return this.props.children;
  }
}

// Higher-order component for lazy loading with custom loading states
export function withLazyLoading<P extends object>(
  Component: ComponentType<P>,
  loadingMessage?: string,
  fallbackComponent?: ComponentType
) {
  const LazyComponent = React.lazy(() => Promise.resolve({ default: Component }));
  
  return React.forwardRef<any, P>((props, ref) => (
    <LazyErrorBoundary fallback={fallbackComponent}>
      <Suspense fallback={<LoadingScreen message={loadingMessage} />}>
        <LazyComponent {...props} ref={ref} />
      </Suspense>
    </LazyErrorBoundary>
  ));
}

// Utility for creating lazy-loaded screens with preloading
export function createLazyScreen<P extends object = any>(
  importFunction: () => Promise<{ default: ComponentType<P> }>,
  loadingMessage?: string,
  preload: boolean = false
) {
  const LazyComponent = React.lazy(importFunction);

  // Preload the component if requested
  if (preload) {
    importFunction().catch(console.error);
  }

  return React.forwardRef<any, P>((props, ref) => (
    <LazyErrorBoundary>
      <Suspense fallback={<LoadingScreen message={loadingMessage} />}>
        <LazyComponent {...props} ref={ref} />
      </Suspense>
    </LazyErrorBoundary>
  ));
}

// Preloader utility for warming up components
export class ComponentPreloader {
  private static preloadedComponents = new Set<string>();
  
  static async preload(
    componentName: string,
    importFunction: () => Promise<any>
  ): Promise<void> {
    if (this.preloadedComponents.has(componentName)) {
      return;
    }
    
    try {
      await importFunction();
      this.preloadedComponents.add(componentName);
      console.log(`✅ Preloaded component: ${componentName}`);
    } catch (error) {
      console.error(`❌ Failed to preload component: ${componentName}`, error);
    }
  }
  
  static isPreloaded(componentName: string): boolean {
    return this.preloadedComponents.has(componentName);
  }
  
  static async preloadCriticalComponents(): Promise<void> {
    const criticalComponents = [
      {
        name: 'SettingsScreen',
        loader: () => import('../components/SettingsScreen'),
      },
      {
        name: 'TaskHistoryScreen',
        loader: () => import('../components/TaskHistoryScreen'),
      },
    ];
    
    await Promise.allSettled(
      criticalComponents.map(({ name, loader }) => this.preload(name, loader))
    );
  }
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    paddingTop: Constants.statusBarHeight,
  },
  loadingGradient: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingContent: {
    alignItems: 'center',
    paddingHorizontal: 40,
  },
  logoContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 30,
  },
  logo: {
    fontSize: 36,
  },
  spinner: {
    marginBottom: 20,
  },
  loadingText: {
    fontSize: 18,
    color: '#ffffff',
    fontWeight: '500',
    marginBottom: 30,
    textAlign: 'center',
  },
  loadingBar: {
    width: width * 0.6,
    height: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 2,
    overflow: 'hidden',
  },
  loadingBarFill: {
    height: '100%',
    width: '60%',
    backgroundColor: '#ffffff',
    borderRadius: 2,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 40,
    backgroundColor: '#f8fafc',
    paddingTop: Constants.statusBarHeight,
  },
  errorTitle: {
    fontSize: 24,
    fontWeight: '600',
    color: '#1f2937',
    marginBottom: 16,
    textAlign: 'center',
  },
  errorMessage: {
    fontSize: 16,
    color: '#6b7280',
    textAlign: 'center',
    lineHeight: 24,
  },
});
