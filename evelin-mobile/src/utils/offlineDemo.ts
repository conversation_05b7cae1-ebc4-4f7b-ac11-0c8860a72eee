/**
 * Demo script to test offline functionality
 * This can be called from the app to demonstrate offline features
 */

import { store } from '../store';
import { addTaskAsync, updateTaskAsync, deleteTaskAsync } from '../store/slices/tasksSlice';
import { offlineManager } from '../services/offlineManager';
import { networkService } from '../services/networkService';

export const runOfflineDemo = async () => {
  console.log('🚀 Starting Offline Functionality Demo...');
  
  try {
    // 1. Check initial network state
    console.log('📡 Current network state:', networkService.getNetworkState());
    console.log('🌐 Is online:', offlineManager.isOnline());
    
    // 2. Create a test task
    console.log('\n📝 Creating a test task...');
    const newTask = {
      text: 'Offline Demo Task',
      location: 'Demo Location',
      category: 'personal' as const,
      completed: false,
      notificationTriggered: false,
      notificationDistance: 100,
      coordinates: {
        lat: 40.7128,
        lng: -74.0060,
      },
    };
    
    const createResult = await store.dispatch(addTaskAsync(newTask));
    console.log('✅ Task created:', createResult.payload);

    // 3. Update the task
    console.log('\n✏️ Updating the task...');
    const taskId = (createResult.payload as any).id;
    const updateResult = await store.dispatch(updateTaskAsync({
      id: taskId,
      updates: {
        text: 'Updated Offline Demo Task',
        location: 'Updated Demo Location',
      },
    }));
    console.log('✅ Task updated:', updateResult.payload);
    
    // 4. Check queue stats
    const queueStats = offlineManager.getQueueStats();
    console.log('\n📊 Queue Statistics:');
    console.log('- Total operations:', queueStats.totalOperations);
    console.log('- Pending operations:', queueStats.pendingOperations);
    console.log('- Completed operations:', queueStats.completedOperations);
    console.log('- Failed operations:', queueStats.failedOperations);
    
    // 5. Check storage info
    const storageInfo = await offlineManager.getStorageInfo();
    console.log('\n💾 Storage Information:');
    console.log('- Tasks stored:', storageInfo.tasksCount);
    console.log('- Storage size:', storageInfo.storageSize, 'bytes');
    console.log('- Conflicts:', storageInfo.conflicts);
    console.log('- Last sync:', storageInfo.lastSync);
    
    // 6. Test sync (if online)
    if (offlineManager.isOnline()) {
      console.log('\n🔄 Testing sync...');
      const syncSuccess = await offlineManager.syncNow();
      console.log('✅ Sync result:', syncSuccess ? 'Success' : 'Failed');
      
      // Check queue stats after sync
      const postSyncStats = offlineManager.getQueueStats();
      console.log('\n📊 Post-Sync Queue Statistics:');
      console.log('- Pending operations:', postSyncStats.pendingOperations);
      console.log('- Completed operations:', postSyncStats.completedOperations);
    } else {
      console.log('\n📵 Currently offline - sync will happen when connection is restored');
    }
    
    // 7. Clean up - delete the test task
    console.log('\n🗑️ Cleaning up test task...');
    const deleteResult = await store.dispatch(deleteTaskAsync(taskId));
    console.log('✅ Task deleted:', deleteResult.payload);
    
    console.log('\n🎉 Offline functionality demo completed successfully!');
    
    return {
      success: true,
      message: 'Offline demo completed successfully',
      stats: {
        queueStats: offlineManager.getQueueStats(),
        storageInfo: await offlineManager.getStorageInfo(),
        networkState: networkService.getNetworkState(),
      },
    };
    
  } catch (error) {
    console.error('❌ Offline demo failed:', error);
    return {
      success: false,
      message: 'Offline demo failed',
      error: error instanceof Error ? error.message : String(error),
    };
  }
};

export const testOfflineScenario = async () => {
  console.log('🧪 Testing Offline Scenario...');
  
  try {
    // Simulate offline mode by mocking network service
    const originalIsOnline = networkService.isOnline;
    const originalIsOffline = networkService.isOffline;
    
    // Mock offline state
    (networkService as any).isOnline = () => false;
    (networkService as any).isOffline = () => true;
    
    console.log('📵 Simulating offline mode...');
    
    // Create tasks while "offline"
    const offlineTasks = [
      {
        text: 'Offline Task 1',
        location: 'Offline Location 1',
        category: 'work' as const,
        completed: false,
        notificationTriggered: false,
        notificationDistance: 100,
        coordinates: {
          lat: 40.7128,
          lng: -74.0060,
        },
      },
      {
        text: 'Offline Task 2',
        location: 'Offline Location 2',
        category: 'personal' as const,
        completed: false,
        notificationTriggered: false,
        notificationDistance: 200,
        coordinates: {
          lat: 40.7589,
          lng: -73.9851,
        },
      },
    ];
    
    const createdTasks = [];
    for (const task of offlineTasks) {
      const result = await store.dispatch(addTaskAsync(task));
      createdTasks.push(result.payload);
      console.log('📝 Created offline task:', (result.payload as any).text);
    }
    
    // Check queue stats
    let queueStats = offlineManager.getQueueStats();
    console.log('\n📊 Queue stats after offline operations:');
    console.log('- Pending operations:', queueStats.pendingOperations);
    console.log('- Total operations:', queueStats.totalOperations);
    
    // Restore online state
    (networkService as any).isOnline = originalIsOnline;
    (networkService as any).isOffline = originalIsOffline;
    
    console.log('\n🌐 Simulating connection restored...');
    
    // Trigger sync
    if (offlineManager.isOnline()) {
      const syncSuccess = await offlineManager.syncNow();
      console.log('🔄 Sync result:', syncSuccess ? 'Success' : 'Failed');
      
      queueStats = offlineManager.getQueueStats();
      console.log('\n📊 Queue stats after sync:');
      console.log('- Pending operations:', queueStats.pendingOperations);
      console.log('- Completed operations:', queueStats.completedOperations);
    }
    
    // Clean up
    console.log('\n🧹 Cleaning up test tasks...');
    for (const task of createdTasks) {
      await store.dispatch(deleteTaskAsync((task as any).id));
    }
    
    console.log('✅ Offline scenario test completed!');
    
    return {
      success: true,
      message: 'Offline scenario test completed successfully',
      tasksCreated: createdTasks.length,
      finalQueueStats: offlineManager.getQueueStats(),
    };
    
  } catch (error) {
    console.error('❌ Offline scenario test failed:', error);
    return {
      success: false,
      message: 'Offline scenario test failed',
      error: error instanceof Error ? error.message : String(error),
    };
  }
};

// Helper function to display offline status
export const getOfflineStatus = () => {
  const networkState = networkService.getNetworkState();
  const queueStats = offlineManager.getQueueStats();
  
  return {
    isOnline: offlineManager.isOnline(),
    isOffline: offlineManager.isOffline(),
    connectionType: networkState.type,
    connectionQuality: networkState.connectionQuality,
    pendingOperations: queueStats.pendingOperations,
    failedOperations: queueStats.failedOperations,
    completedOperations: queueStats.completedOperations,
  };
};

// Function to be called from React DevTools or console
(global as any).offlineDemo = {
  runDemo: runOfflineDemo,
  testOfflineScenario,
  getStatus: getOfflineStatus,
  manager: offlineManager,
  networkService,
};
