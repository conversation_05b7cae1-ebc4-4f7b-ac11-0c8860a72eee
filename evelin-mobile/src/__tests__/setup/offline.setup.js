// Basic Jest setup for offline tests

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(() => Promise.resolve(null)),
  setItem: jest.fn(() => Promise.resolve()),
  removeItem: jest.fn(() => Promise.resolve()),
  multiGet: jest.fn(() => Promise.resolve([])),
  multiSet: jest.fn(() => Promise.resolve()),
  multiRemove: jest.fn(() => Promise.resolve()),
  getAllKeys: jest.fn(() => Promise.resolve([])),
  clear: jest.fn(() => Promise.resolve()),
}));

// Mock NetInfo
jest.mock('@react-native-community/netinfo', () => ({
  fetch: jest.fn(() => Promise.resolve({
    type: 'wifi',
    isConnected: true,
    isInternetReachable: true,
  })),
  addEventListener: jest.fn(() => jest.fn()),
}));

// Mock React Native modules that might be used
jest.mock('react-native', () => ({
  Platform: {
    OS: 'ios',
    select: jest.fn((obj) => obj.ios || obj.default),
  },
  Dimensions: {
    get: jest.fn(() => ({ width: 375, height: 812 })),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
  },
  Alert: {
    alert: jest.fn(),
  },
  AppState: {
    currentState: 'active',
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
  },
}));

// Mock Supabase
jest.mock('../../services/supabase', () => ({
  supabase: {
    from: jest.fn(() => ({
      select: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      delete: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      single: jest.fn(() => Promise.resolve({ data: null, error: null })),
      then: jest.fn((callback) => callback({ data: [], error: null })),
    })),
    auth: {
      getUser: jest.fn(() => Promise.resolve({ 
        data: { user: { id: 'test-user-id' } }, 
        error: null 
      })),
    },
  },
}));

// Mock task service
jest.mock('../../services/taskService', () => ({
  mobileTaskService: {
    getTasks: jest.fn(() => Promise.resolve([])),
    createTask: jest.fn((task) => Promise.resolve({ ...task, id: 'new-task-id' })),
    updateTask: jest.fn((id, updates) => Promise.resolve({ id, ...updates })),
    deleteTask: jest.fn(() => Promise.resolve()),
  },
}));

// Mock Redux store
const mockStore = {
  getState: jest.fn(() => ({
    tasks: { tasks: [] },
    network: { isOnline: true },
    ui: { loading: false },
  })),
  dispatch: jest.fn(),
  subscribe: jest.fn(),
};

jest.mock('../../store', () => ({
  store: mockStore,
}));

// Mock network service
jest.mock('../../services/networkService', () => ({
  networkService: {
    isOnline: jest.fn(() => true),
    isOffline: jest.fn(() => false),
    getConnectionQuality: jest.fn(() => ({ latency: 50, quality: 'good' })),
    on: jest.fn(),
    off: jest.fn(),
    emit: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
  },
}));

// Global test utilities
global.mockAsyncStorage = () => {
  const storage = new Map();
  
  return {
    getItem: jest.fn((key) => Promise.resolve(storage.get(key) || null)),
    setItem: jest.fn((key, value) => {
      storage.set(key, value);
      return Promise.resolve();
    }),
    removeItem: jest.fn((key) => {
      storage.delete(key);
      return Promise.resolve();
    }),
    multiGet: jest.fn((keys) => 
      Promise.resolve(keys.map(key => [key, storage.get(key) || null]))
    ),
    multiSet: jest.fn((keyValuePairs) => {
      keyValuePairs.forEach(([key, value]) => storage.set(key, value));
      return Promise.resolve();
    }),
    multiRemove: jest.fn((keys) => {
      keys.forEach(key => storage.delete(key));
      return Promise.resolve();
    }),
    getAllKeys: jest.fn(() => Promise.resolve([...storage.keys()])),
    clear: jest.fn(() => {
      storage.clear();
      return Promise.resolve();
    }),
    _storage: storage, // For test inspection
  };
};

// Test data factories
global.createMockTask = (overrides = {}) => ({
  id: 'test-task-id',
  text: 'Test task',
  location: 'Test location',
  category: 'general',
  completed: false,
  createdAt: new Date(),
  notificationDistance: 1000,
  notificationTriggered: false,
  ...overrides,
});

global.createMockQueueOperation = (overrides = {}) => ({
  id: 'test-operation-id',
  type: 'CREATE_TASK',
  payload: { text: 'Test task' },
  timestamp: new Date().toISOString(),
  retryCount: 0,
  status: 'pending',
  priority: 'medium',
  maxRetries: 3,
  version: 1,
  checksum: 'test-checksum',
  ...overrides,
});

global.createMockConflict = (overrides = {}) => ({
  id: 'test-conflict-id',
  type: 'task',
  entityId: 'test-entity-id',
  localData: { id: 'test-entity-id', text: 'Local version' },
  serverData: { id: 'test-entity-id', text: 'Server version' },
  conflictFields: ['text'],
  timestamp: new Date().toISOString(),
  status: 'pending',
  metadata: {
    autoResolvable: true,
    confidence: 0.8,
  },
  ...overrides,
});

// Setup and teardown helpers
global.setupOfflineTest = async () => {
  // Reset all mocks
  jest.clearAllMocks();
  
  // Reset AsyncStorage
  const AsyncStorage = require('@react-native-async-storage/async-storage');
  AsyncStorage.clear();
  
  // Reset network state
  const { networkService } = require('../../services/networkService');
  networkService.isOnline.mockReturnValue(true);
  networkService.isOffline.mockReturnValue(false);
  
  // Reset store state
  mockStore.getState.mockReturnValue({
    tasks: { tasks: [] },
    network: { isOnline: true },
    ui: { loading: false },
  });
};

global.teardownOfflineTest = async () => {
  // Clean up any timers or intervals
  jest.clearAllTimers();
  
  // Reset all mocks
  jest.clearAllMocks();
};

// Console suppression for cleaner test output
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

beforeEach(() => {
  // Suppress expected console messages during tests
  console.error = jest.fn((message) => {
    if (typeof message === 'string' && (
      message.includes('Warning:') ||
      message.includes('Failed to') ||
      message.includes('Error:')
    )) {
      return; // Suppress expected errors/warnings
    }
    originalConsoleError(message);
  });
  
  console.warn = jest.fn((message) => {
    if (typeof message === 'string' && (
      message.includes('Warning:') ||
      message.includes('Deprecated')
    )) {
      return; // Suppress expected warnings
    }
    originalConsoleWarn(message);
  });
});

afterEach(() => {
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
});

// Increase timeout for offline operations
jest.setTimeout(30000);
