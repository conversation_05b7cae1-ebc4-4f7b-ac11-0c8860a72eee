import AsyncStorage from '@react-native-async-storage/async-storage';
import { offlineQueueService } from '../../services/offlineQueueService';
import { offlineStorageService } from '../../services/offlineStorageService';
import { conflictResolutionService, ConflictResolutionStrategy } from '../../services/conflictResolutionService';
import { dataVersioningService } from '../../services/dataVersioningService';
import { offlineManager } from '../../services/offlineManager';
import { Task } from '../../types/task';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  multiGet: jest.fn(),
  multiSet: jest.fn(),
  multiRemove: jest.fn(),
  getAllKeys: jest.fn(),
  clear: jest.fn(),
}));

// Mock network service
jest.mock('../../services/networkService', () => ({
  networkService: {
    isOnline: jest.fn(() => true),
    isOffline: jest.fn(() => false),
    on: jest.fn(),
    emit: jest.fn(),
    getConnectionQuality: jest.fn(() => ({ latency: 50, quality: 'good' })),
  },
}));

// Mock Redux store
jest.mock('../../store', () => ({
  store: {
    getState: jest.fn(() => ({
      tasks: { tasks: [] },
      network: { isOnline: true },
    })),
    dispatch: jest.fn(),
  },
}));

describe('Offline-First Architecture', () => {
  beforeEach(async () => {
    jest.clearAllMocks();
    (AsyncStorage.getItem as jest.Mock).mockResolvedValue(null);
    (AsyncStorage.setItem as jest.Mock).mockResolvedValue(undefined);
    (AsyncStorage.getAllKeys as jest.Mock).mockResolvedValue([]);
  });

  describe('Queue Persistence Service', () => {
    it('should initialize and load existing queue', async () => {
      const mockQueue = [
        {
          id: 'test-1',
          type: 'CREATE_TASK',
          payload: { text: 'Test task' },
          timestamp: new Date().toISOString(),
          retryCount: 0,
          status: 'pending',
          priority: 'medium',
          maxRetries: 3,
          version: 1,
          checksum: 'abc123',
        },
      ];

      (AsyncStorage.getItem as jest.Mock).mockResolvedValueOnce(JSON.stringify(mockQueue));

      await offlineQueueService.initialize();
      const stats = await offlineQueueService.getStats();

      expect(stats.pendingOperations).toBe(1);
      expect(stats.totalOperations).toBe(1);
    });

    it('should enqueue operations with proper metadata', async () => {
      await offlineQueueService.initialize();

      const operationId = await offlineQueueService.enqueue({
        type: 'CREATE_TASK',
        payload: { text: 'New task', location: 'Test location' },
        priority: 'high',
        maxRetries: 3,
        metadata: {
          conflictResolution: 'timestamp-based' as ConflictResolutionStrategy,
        },
      });

      expect(operationId).toBeDefined();
      expect(typeof operationId).toBe('string');

      const stats = await offlineQueueService.getStats();
      expect(stats.pendingOperations).toBe(1);
    });

    it('should handle atomic operations', async () => {
      await offlineQueueService.initialize();

      const operations = [
        {
          type: 'CREATE_TASK' as const,
          payload: { text: 'Task 1' },
          priority: 'medium' as const,
          maxRetries: 3,
        },
        {
          type: 'CREATE_TASK' as const,
          payload: { text: 'Task 2' },
          priority: 'medium' as const,
          maxRetries: 3,
        },
      ];

      const operationIds = await offlineQueueService.enqueueAtomicGroup(operations);

      expect(operationIds).toHaveLength(2);
      expect(operationIds.every(id => typeof id === 'string')).toBe(true);

      const stats = await offlineQueueService.getStats();
      expect(stats.pendingOperations).toBe(2);
      expect(stats.atomicGroups).toBe(1);
    });

    it('should validate queue integrity', async () => {
      const corruptedQueue = [
        {
          id: 'test-1',
          type: 'CREATE_TASK',
          payload: { text: 'Test task' },
          timestamp: new Date().toISOString(),
          retryCount: 0,
          status: 'pending',
          priority: 'medium',
          maxRetries: 3,
          version: 1,
          checksum: 'wrong-checksum', // Intentionally wrong
        },
      ];

      (AsyncStorage.getItem as jest.Mock).mockResolvedValueOnce(JSON.stringify(corruptedQueue));

      await offlineQueueService.initialize();
      const stats = await offlineQueueService.getStats();

      expect(stats.queueHealth).toBe('degraded');
    });

    it('should handle rollback operations', async () => {
      await offlineQueueService.initialize();

      const operationId = await offlineQueueService.enqueue({
        type: 'UPDATE_TASK',
        payload: { id: 'task-1', text: 'Updated task' },
        priority: 'medium',
        maxRetries: 3,
        rollbackData: {
          taskId: 'task-1',
          previousData: { text: 'Original task' },
        },
      });

      const rollbackResult = await offlineQueueService.rollbackOperation(operationId);
      expect(rollbackResult).toBe(true);
    });
  });

  describe('Conflict Resolution Service', () => {
    beforeEach(async () => {
      await conflictResolutionService.initialize();
    });

    it('should detect conflicts between local and server data', async () => {
      const localTasks: Task[] = [
        {
          id: 'task-1',
          text: 'Local version',
          location: 'Local location',
          completed: false,
          createdAt: new Date('2023-01-01'),
          notificationDistance: 1000,
          notificationTriggered: false,
          category: 'work',
        },
      ];

      const serverTasks: Task[] = [
        {
          id: 'task-1',
          text: 'Server version',
          location: 'Server location',
          completed: true,
          createdAt: new Date('2023-01-01'),
          notificationDistance: 2000,
          notificationTriggered: false,
          category: 'personal',
        },
      ];

      const conflicts = await conflictResolutionService.detectConflicts(
        localTasks,
        serverTasks,
        'task'
      );

      expect(conflicts).toHaveLength(1);
      expect(conflicts[0].conflictFields).toContain('text');
      expect(conflicts[0].conflictFields).toContain('location');
      expect(conflicts[0].conflictFields).toContain('completed');
      expect(conflicts[0].conflictFields).toContain('category');
    });

    it('should resolve conflicts using timestamp strategy', async () => {
      const conflict = {
        id: 'conflict-1',
        type: 'task' as const,
        entityId: 'task-1',
        localData: {
          id: 'task-1',
          text: 'Local version',
          updatedAt: '2023-01-02T10:00:00Z',
        },
        serverData: {
          id: 'task-1',
          text: 'Server version',
          updatedAt: '2023-01-01T10:00:00Z',
        },
        conflictFields: ['text'],
        timestamp: new Date().toISOString(),
        status: 'pending' as const,
        metadata: {
          localTimestamp: '2023-01-02T10:00:00Z',
          serverTimestamp: '2023-01-01T10:00:00Z',
          autoResolvable: true,
          confidence: 0.9,
        },
      };

      const result = await conflictResolutionService.resolveConflict(conflict, 'timestamp-based');

      expect(result.success).toBe(true);
      expect(result.strategy).toBe('timestamp-based');
      expect(result.resolvedData.text).toBe('Local version'); // Local is newer
    });

    it('should resolve conflicts using merge strategy', async () => {
      const conflict = {
        id: 'conflict-1',
        type: 'task' as const,
        entityId: 'task-1',
        localData: {
          id: 'task-1',
          text: 'Very long local description',
          completed: true,
          notificationDistance: 3000,
        },
        serverData: {
          id: 'task-1',
          text: 'Short server text',
          completed: false,
          notificationDistance: 1000,
        },
        conflictFields: ['text', 'completed', 'notificationDistance'],
        timestamp: new Date().toISOString(),
        status: 'pending' as const,
        metadata: {
          autoResolvable: true,
          confidence: 0.8,
        },
      };

      const result = await conflictResolutionService.resolveConflict(conflict, 'field-level-merge');

      expect(result.success).toBe(true);
      expect(result.strategy).toBe('field-level-merge');
      expect(result.resolvedData.text).toBe('Very long local description'); // Longest wins
      expect(result.resolvedData.completed).toBe(true); // Truthy wins
      expect(result.resolvedData.notificationDistance).toBe(3000); // Max wins
    });

    it('should handle user choice conflicts', async () => {
      const conflict = {
        id: 'conflict-1',
        type: 'task' as const,
        entityId: 'task-1',
        localData: { id: 'task-1', text: 'Local version' },
        serverData: { id: 'task-1', text: 'Server version' },
        conflictFields: ['text'],
        timestamp: new Date().toISOString(),
        status: 'pending' as const,
      };

      const result = await conflictResolutionService.resolveConflict(conflict, 'user-choice');

      expect(result.success).toBe(true);
      expect(result.strategy).toBe('user-choice');
      expect(result.requiresUserInput).toBe(true);

      // Simulate user choice
      const userChoice = { id: 'task-1', text: 'User chosen version' };
      const userResult = await conflictResolutionService.resolveUserConflict(conflict.id, userChoice);

      expect(userResult.success).toBe(true);
      expect(userResult.resolvedData.text).toBe('User chosen version');
    });
  });

  describe('Data Versioning Service', () => {
    beforeEach(async () => {
      await dataVersioningService.initialize();
    });

    it('should detect when migrations are needed', async () => {
      // Mock current version as 1
      (AsyncStorage.getItem as jest.Mock).mockImplementation((key) => {
        if (key === 'data_version') return Promise.resolve('1');
        return Promise.resolve(null);
      });

      await dataVersioningService.initialize();
      const needsMigration = await dataVersioningService.checkForMigrations();

      expect(needsMigration).toBe(true);
    });

    it('should apply migrations successfully', async () => {
      // Mock data with old schema
      const oldData = {
        tasks: [
          {
            id: 'task-1',
            text: 'Test task',
            completed: false,
            // Missing category field (added in v2)
          },
        ],
      };

      (AsyncStorage.getItem as jest.Mock).mockImplementation((key) => {
        if (key === 'data_version') return Promise.resolve('1');
        if (key === 'offline_tasks') return Promise.resolve(JSON.stringify(oldData.tasks));
        return Promise.resolve(null);
      });

      await dataVersioningService.initialize();
      const result = await dataVersioningService.migrate(2);

      expect(result.success).toBe(true);
      expect(result.version).toBe(2);
      expect(result.migrationsApplied).toContain('add_task_category_v2');
    });

    it('should rollback migrations when needed', async () => {
      // Mock current version as 3
      (AsyncStorage.getItem as jest.Mock).mockImplementation((key) => {
        if (key === 'data_version') return Promise.resolve('3');
        return Promise.resolve(null);
      });

      await dataVersioningService.initialize();
      const result = await dataVersioningService.rollbackToVersion(2);

      expect(result.success).toBe(true);
      expect(result.version).toBe(2);
    });

    it('should validate data integrity', async () => {
      const validData = {
        tasks: [
          {
            id: 'task-1',
            text: 'Valid task',
            completed: false,
          },
        ],
      };

      (AsyncStorage.getItem as jest.Mock).mockImplementation((key) => {
        if (key === 'offline_tasks') return Promise.resolve(JSON.stringify(validData.tasks));
        return Promise.resolve(null);
      });

      const integrityCheck = await dataVersioningService.validateDataIntegrity();

      expect(integrityCheck.valid).toBe(true);
      expect(integrityCheck.errors).toHaveLength(0);
    });

    it('should detect data corruption', async () => {
      const corruptData = {
        tasks: [
          {
            // Missing required id field
            text: 'Corrupt task',
            completed: false,
          },
          {
            id: 'task-2',
            // Missing required text field
            completed: true,
          },
        ],
      };

      (AsyncStorage.getItem as jest.Mock).mockImplementation((key) => {
        if (key === 'offline_tasks') return Promise.resolve(JSON.stringify(corruptData.tasks));
        return Promise.resolve(null);
      });

      const integrityCheck = await dataVersioningService.validateDataIntegrity();

      expect(integrityCheck.valid).toBe(false);
      expect(integrityCheck.errors.length).toBeGreaterThan(0);
    });
  });

  describe('Offline Storage Service', () => {
    beforeEach(async () => {
      await offlineStorageService.initialize();
    });

    it('should sync with advanced conflict resolution', async () => {
      const localTasks: Task[] = [
        {
          id: 'task-1',
          text: 'Local task',
          location: 'Local location',
          completed: false,
          createdAt: new Date('2023-01-02'),
          notificationDistance: 1000,
          notificationTriggered: false,
          category: 'work',
        },
      ];

      const serverTasks: Task[] = [
        {
          id: 'task-1',
          text: 'Server task',
          location: 'Server location',
          completed: true,
          createdAt: new Date('2023-01-01'),
          notificationDistance: 2000,
          notificationTriggered: false,
          category: 'personal',
        },
      ];

      // Mock storage and service calls
      (AsyncStorage.getItem as jest.Mock).mockImplementation((key) => {
        if (key === 'offline_tasks') return Promise.resolve(JSON.stringify(localTasks));
        return Promise.resolve(null);
      });

      // Mock task service
      jest.doMock('../../services/taskService', () => ({
        mobileTaskService: {
          getTasks: jest.fn().mockResolvedValue(serverTasks),
        },
      }));

      const result = await offlineStorageService.syncWithServer('timestamp-based');

      expect(result.success).toBe(true);
      expect(result.autoResolvedConflicts).toBeGreaterThan(0);
    });
  });

  describe('Integration Tests', () => {
    it('should handle complete offline-to-online workflow', async () => {
      // Initialize all services
      await offlineManager.initialize();

      // Simulate offline state
      const { networkService } = require('../../services/networkService');
      networkService.isOnline.mockReturnValue(false);
      networkService.isOffline.mockReturnValue(true);

      // Create task while offline
      const taskData = {
        text: 'Offline task',
        location: 'Test location',
        category: 'work',
        completed: false,
        notificationDistance: 1000,
        notificationTriggered: false,
      };

      const operationId = await offlineQueueService.enqueue({
        type: 'CREATE_TASK',
        payload: taskData,
        priority: 'medium',
        maxRetries: 3,
      });

      expect(operationId).toBeDefined();

      // Verify task is queued
      const stats = await offlineQueueService.getStats();
      expect(stats.pendingOperations).toBe(1);

      // Simulate going online
      networkService.isOnline.mockReturnValue(true);
      networkService.isOffline.mockReturnValue(false);

      // Process queue (would normally be triggered by network change)
      await offlineQueueService.processQueue();

      // Verify queue is processed
      const finalStats = await offlineQueueService.getStats();
      expect(finalStats.pendingOperations).toBe(0);
    });

    it('should handle data migration during app update', async () => {
      // Mock old version data
      (AsyncStorage.getItem as jest.Mock).mockImplementation((key) => {
        if (key === 'data_version') return Promise.resolve('1');
        if (key === 'offline_tasks') {
          return Promise.resolve(JSON.stringify([
            {
              id: 'task-1',
              text: 'Old format task',
              completed: false,
              // Missing newer fields
            },
          ]));
        }
        return Promise.resolve(null);
      });

      // Initialize offline manager (should trigger migration)
      await offlineManager.initialize();

      // Verify migration was applied
      expect(AsyncStorage.setItem).toHaveBeenCalledWith('data_version', expect.any(String));
    });

    it('should handle conflict resolution during sync', async () => {
      // Setup conflicting data
      const localTasks = [
        {
          id: 'task-1',
          text: 'Local version',
          completed: true,
          updatedAt: '2023-01-02T10:00:00Z',
        },
      ];

      const serverTasks = [
        {
          id: 'task-1',
          text: 'Server version',
          completed: false,
          updatedAt: '2023-01-01T10:00:00Z',
        },
      ];

      (AsyncStorage.getItem as jest.Mock).mockImplementation((key) => {
        if (key === 'offline_tasks') return Promise.resolve(JSON.stringify(localTasks));
        return Promise.resolve(null);
      });

      // Mock task service
      jest.doMock('../../services/taskService', () => ({
        mobileTaskService: {
          getTasks: jest.fn().mockResolvedValue(serverTasks),
        },
      }));

      await offlineManager.initialize();
      const result = await offlineStorageService.syncWithServer('timestamp-based');

      expect(result.success).toBe(true);
      expect(result.autoResolvedConflicts).toBe(1);
      expect(result.userRequiredConflicts).toBe(0);
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle storage quota exceeded', async () => {
      const error = new Error('QuotaExceededError');
      (AsyncStorage.setItem as jest.Mock).mockRejectedValue(error);

      await offlineQueueService.initialize();

      const operationId = await offlineQueueService.enqueue({
        type: 'CREATE_TASK',
        payload: { text: 'Large task data'.repeat(1000) },
        priority: 'medium',
        maxRetries: 3,
      });

      // Should still return an ID but handle the error gracefully
      expect(operationId).toBeDefined();
    });

    it('should handle corrupted AsyncStorage data', async () => {
      (AsyncStorage.getItem as jest.Mock).mockResolvedValue('invalid-json');

      await expect(offlineQueueService.initialize()).resolves.not.toThrow();

      const stats = await offlineQueueService.getStats();
      expect(stats.totalOperations).toBe(0); // Should start fresh
    });

    it('should handle network interruption during sync', async () => {
      const { networkService } = require('../../services/networkService');

      await offlineManager.initialize();

      // Start sync
      const syncPromise = offlineStorageService.syncWithServer();

      // Simulate network interruption
      networkService.isOnline.mockReturnValue(false);

      const result = await syncPromise;

      // Should handle gracefully
      expect(result.success).toBeDefined();
    });

    it('should handle concurrent operations', async () => {
      await offlineQueueService.initialize();

      // Create multiple concurrent operations
      const promises = Array.from({ length: 10 }, (_, i) =>
        offlineQueueService.enqueue({
          type: 'CREATE_TASK',
          payload: { text: `Concurrent task ${i}` },
          priority: 'medium',
          maxRetries: 3,
        })
      );

      const operationIds = await Promise.all(promises);

      expect(operationIds).toHaveLength(10);
      expect(new Set(operationIds).size).toBe(10); // All unique IDs

      const stats = await offlineQueueService.getStats();
      expect(stats.pendingOperations).toBe(10);
    });

    it('should handle migration failure and rollback', async () => {
      // Mock migration that will fail
      const originalMigrate = dataVersioningService.migrate;
      jest.spyOn(dataVersioningService, 'migrate').mockImplementation(async () => ({
        success: false,
        version: 1,
        migrationsApplied: [],
        errors: ['Migration failed'],
        warnings: [],
        rollbackAvailable: true,
        duration: 100,
      }));

      await expect(offlineManager.initialize()).rejects.toThrow('Data migration failed');

      // Restore original method
      dataVersioningService.migrate = originalMigrate;
    });

    it('should handle large queue processing', async () => {
      await offlineQueueService.initialize();

      // Add many operations
      const operations = Array.from({ length: 100 }, (_, i) => ({
        type: 'CREATE_TASK' as const,
        payload: { text: `Task ${i}` },
        priority: 'low' as const,
        maxRetries: 3,
      }));

      for (const operation of operations) {
        await offlineQueueService.enqueue(operation);
      }

      const stats = await offlineQueueService.getStats();
      expect(stats.pendingOperations).toBe(100);
      expect(stats.queueHealth).toBe('healthy');
    });
  });

  describe('Performance Tests', () => {
    it('should handle queue operations efficiently', async () => {
      await offlineQueueService.initialize();

      const startTime = Date.now();

      // Add 50 operations
      for (let i = 0; i < 50; i++) {
        await offlineQueueService.enqueue({
          type: 'CREATE_TASK',
          payload: { text: `Performance test task ${i}` },
          priority: 'medium',
          maxRetries: 3,
        });
      }

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should complete within reasonable time (adjust threshold as needed)
      expect(duration).toBeLessThan(5000); // 5 seconds
    });

    it('should handle conflict resolution efficiently', async () => {
      await conflictResolutionService.initialize();

      const localTasks = Array.from({ length: 20 }, (_, i) => ({
        id: `task-${i}`,
        text: `Local task ${i}`,
        completed: i % 2 === 0,
        createdAt: new Date(),
        location: `Local location ${i}`,
        notificationDistance: 1000,
        notificationTriggered: false,
        category: 'test',
      }));

      const serverTasks = Array.from({ length: 20 }, (_, i) => ({
        id: `task-${i}`,
        text: `Server task ${i}`,
        completed: i % 3 === 0,
        createdAt: new Date(),
        location: `Server location ${i}`,
        notificationDistance: 2000,
        notificationTriggered: false,
        category: 'work',
      }));

      const startTime = Date.now();

      const conflicts = await conflictResolutionService.detectConflicts(
        localTasks,
        serverTasks,
        'task'
      );

      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(conflicts.length).toBeGreaterThan(0);
      expect(duration).toBeLessThan(2000); // 2 seconds
    });

    it('should handle data migration efficiently', async () => {
      // Mock large dataset
      const largeTasks = Array.from({ length: 100 }, (_, i) => ({
        id: `task-${i}`,
        text: `Task ${i}`,
        completed: false,
      }));

      (AsyncStorage.getItem as jest.Mock).mockImplementation((key) => {
        if (key === 'data_version') return Promise.resolve('1');
        if (key === 'offline_tasks') return Promise.resolve(JSON.stringify(largeTasks));
        return Promise.resolve(null);
      });

      await dataVersioningService.initialize();

      const startTime = Date.now();
      const result = await dataVersioningService.migrate(2);
      const endTime = Date.now();

      expect(result.success).toBe(true);
      expect(result.duration).toBeLessThan(3000); // 3 seconds
      expect(endTime - startTime).toBeLessThan(5000); // 5 seconds total
    });
  });
});
