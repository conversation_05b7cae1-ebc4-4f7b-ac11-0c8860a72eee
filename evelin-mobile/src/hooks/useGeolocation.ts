import { useState, useEffect, useRef, useCallback } from 'react';
import * as Location from 'expo-location';
import { mobileLocationService } from '../services/locationService';

export const useGeolocation = () => {
  const [latitude, setLatitude] = useState<number | null>(null);
  const [longitude, setLongitude] = useState<number | null>(null);
  const [accuracy, setAccuracy] = useState<number | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [permission, setPermission] = useState<'granted' | 'denied' | 'prompt'>('prompt');
  const [locationSubscription, setLocationSubscription] = useState<Location.LocationSubscription | null>(null);
  const [isTrackingEnabled, setIsTrackingEnabled] = useState<boolean>(true);

  // Use ref to track if component is mounted to prevent memory leaks
  const isMountedRef = useRef(true);
  const abortControllerRef = useRef<AbortController | null>(null);

  useEffect(() => {
    isMountedRef.current = true;

    // Create abort controller for cleanup
    abortControllerRef.current = new AbortController();

    checkPermissionAndStartTracking();

    return () => {
      // Mark component as unmounted
      isMountedRef.current = false;

      // Abort any ongoing operations
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // Cleanup location subscription
      if (locationSubscription) {
        locationSubscription.remove();
        setLocationSubscription(null);
      }
    };
  }, [isTrackingEnabled]);

  const checkPermissionAndStartTracking = useCallback(async () => {
    try {
      // Check if component is still mounted and operation not aborted
      if (!isMountedRef.current || abortControllerRef.current?.signal.aborted) {
        return;
      }

      const { status } = await Location.getForegroundPermissionsAsync();

      // Check again after async operation
      if (!isMountedRef.current || abortControllerRef.current?.signal.aborted) {
        return;
      }

      if (status === 'granted') {
        setPermission('granted');
        if (isTrackingEnabled) {
          startLocationTracking();
        }
      } else {
        setPermission(status === 'denied' ? 'denied' : 'prompt');
      }
    } catch (error) {
      // Only update state if component is still mounted
      if (isMountedRef.current && !abortControllerRef.current?.signal.aborted) {
        console.error('Error checking location permission:', error);
        setError('Failed to check location permission');
      }
    }
  }, []);

  const requestPermission = useCallback(async () => {
    try {
      // Check if component is still mounted
      if (!isMountedRef.current || abortControllerRef.current?.signal.aborted) {
        return;
      }

      const hasPermission = await mobileLocationService.requestLocationPermission();

      // Check again after async operation
      if (!isMountedRef.current || abortControllerRef.current?.signal.aborted) {
        return;
      }

      if (hasPermission) {
        setPermission('granted');
        setError(null);
        if (isTrackingEnabled) {
          startLocationTracking();
        }
      } else {
        setPermission('denied');
        setError('Location permission denied');
      }
    } catch (error) {
      // Only update state if component is still mounted
      if (isMountedRef.current && !abortControllerRef.current?.signal.aborted) {
        console.error('Error requesting location permission:', error);
        setError('Failed to request location permission');
      }
    }
  }, []);

  const startLocationTracking = useCallback(async () => {
    try {
      // Check if component is still mounted
      if (!isMountedRef.current || abortControllerRef.current?.signal.aborted) {
        return;
      }

      // Clean up existing subscription before creating new one
      if (locationSubscription) {
        locationSubscription.remove();
        setLocationSubscription(null);
      }

      // Get current location first
      const currentLocation = await mobileLocationService.getCurrentLocation();

      // Check if component is still mounted after async operation
      if (!isMountedRef.current || abortControllerRef.current?.signal.aborted) {
        return;
      }

      if (currentLocation) {
        setLatitude(currentLocation.latitude);
        setLongitude(currentLocation.longitude);
        setAccuracy(10); // Approximate accuracy
        setError(null);
      }

      // Start watching location changes with memory leak protection
      const subscription = await mobileLocationService.watchLocation((location) => {
        // Only update state if component is still mounted
        if (isMountedRef.current && !abortControllerRef.current?.signal.aborted) {
          setLatitude(location.latitude);
          setLongitude(location.longitude);
          setAccuracy(10); // Approximate accuracy
          setError(null);
        }
      });

      // Check if component is still mounted before setting subscription
      if (isMountedRef.current && !abortControllerRef.current?.signal.aborted && subscription) {
        setLocationSubscription(subscription);
      } else if (subscription) {
        // Clean up subscription if component unmounted during creation
        subscription.remove();
      }
    } catch (error) {
      // Only update state if component is still mounted
      if (isMountedRef.current && !abortControllerRef.current?.signal.aborted) {
        console.error('Error starting location tracking:', error);
        setError('Failed to get location');
      }
    }
  }, [locationSubscription]);

  const stopLocationTracking = useCallback(() => {
    console.log('🛑 Stopping location tracking');

    // Clean up location subscription
    if (locationSubscription) {
      locationSubscription.remove();
      setLocationSubscription(null);
    }

    // Clear location data
    setLatitude(null);
    setLongitude(null);
    setAccuracy(null);
    setError(null);
  }, [locationSubscription]);

  const toggleLocationTracking = useCallback(async (enabled: boolean) => {
    console.log(`🔄 ${enabled ? 'Enabling' : 'Disabling'} location tracking`);
    setIsTrackingEnabled(enabled);

    if (enabled && permission === 'granted') {
      // Start tracking if permission is already granted
      startLocationTracking();
    } else if (!enabled) {
      // Stop tracking
      stopLocationTracking();
    }
  }, [permission, startLocationTracking, stopLocationTracking]);

  return {
    latitude,
    longitude,
    accuracy,
    error,
    permission,
    isTrackingEnabled,
    requestPermission,
    toggleLocationTracking,
  };
};
