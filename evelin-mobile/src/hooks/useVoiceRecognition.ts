import { useState, useEffect } from 'react';
import { mobileVoiceService } from '../services/voiceService';

export const useVoiceRecognition = () => {
  const [isListening, setIsListening] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [isSupported, setIsSupported] = useState(true);

  useEffect(() => {
    // Check if voice recognition is supported
    checkSupport();
  }, []);

  const checkSupport = async () => {
    try {
      const supported = await mobileVoiceService.isSpeechAvailable();
      setIsSupported(supported);
    } catch (error) {
      console.error('Error checking voice support:', error);
      setIsSupported(false);
    }
  };

  const startListening = async () => {
    try {
      setError(null);
      setIsProcessing(true);
      
      const started = await mobileVoiceService.startRecording();
      if (started) {
        setIsListening(true);
        setTranscript('');
      } else {
        setError('Could not start voice recording');
      }
    } catch (error) {
      console.error('Error starting voice recognition:', error);
      setError('Failed to start voice recognition');
    } finally {
      setIsProcessing(false);
    }
  };

  const stopListening = async () => {
    try {
      setIsProcessing(true);
      
      const result = await mobileVoiceService.stopRecording();
      setIsListening(false);
      
      if (result) {
        // For now, we'll use a placeholder transcript
        // In a real implementation, you would process the audio with speech-to-text
        setTranscript('Voice input recorded - speech-to-text integration needed');
      }
    } catch (error) {
      console.error('Error stopping voice recognition:', error);
      setError('Failed to stop voice recognition');
    } finally {
      setIsProcessing(false);
    }
  };

  const clearTranscript = () => {
    setTranscript('');
    setError(null);
  };

  return {
    isListening,
    isProcessing,
    transcript,
    isSupported,
    error,
    startListening,
    stopListening,
    clearTranscript,
  };
};
