import { useState, useEffect } from 'react';
import { getSupabaseClient } from '../services/supabase';
import { Task } from '../types/task';
import { User } from '@supabase/supabase-js';

export const useTasks = (user: User | null) => {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);

  // Load tasks from database
  useEffect(() => {
    if (!user) {
      setTasks([]);
      setLoading(false);
      return;
    }

    const loadTasks = async () => {
      try {
        const supabase = await getSupabaseClient();
        const { data, error } = await supabase
          .from('tasks')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false });

        if (error) throw error;

        const formattedTasks: Task[] = (data || []).map(task => ({
          id: task.id,
          text: task.text,
          location: task.location || undefined,
          category: task.category || undefined,
          completed: task.completed,
          createdAt: new Date(task.created_at),
          notificationTriggered: task.notification_triggered || false,
          notificationDistance: task.notification_distance || undefined,
          coordinates: task.coordinates || undefined,
        }));

        setTasks(formattedTasks);
      } catch (error) {
        console.error('Error loading tasks:', error);
      } finally {
        setLoading(false);
      }
    };

    loadTasks();
  }, [user]);

  const addTask = async (taskData: Partial<Task>) => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    try {
      const insertData = {
        user_id: user.id,
        text: taskData.text || '',
        location: taskData.location || null,
        category: taskData.category || null,
        completed: false,
        notification_triggered: false,
        notification_distance: taskData.notificationDistance || 200,
        coordinates: taskData.coordinates || null,
      };

      const { data, error } = await supabase
        .from('tasks')
        .insert(insertData)
        .select()
        .single();

      if (error) throw error;

      const newTask: Task = {
        id: data.id,
        text: data.text,
        location: data.location || undefined,
        category: data.category || undefined,
        completed: data.completed,
        createdAt: new Date(data.created_at),
        notificationTriggered: data.notification_triggered || false,
        notificationDistance: data.notification_distance || undefined,
        coordinates: data.coordinates || undefined,
      };

      setTasks(prevTasks => [newTask, ...prevTasks]);
      console.log('Task added successfully:', newTask);
    } catch (error) {
      console.error('Error adding task:', error);
      throw error;
    }
  };

  const updateTask = async (taskId: string, updates: Partial<Task>) => {
    if (!user) return;

    try {
      const updateData: any = {};
      if (updates.text !== undefined) updateData.text = updates.text;
      if (updates.location !== undefined) updateData.location = updates.location || null;
      if (updates.category !== undefined) updateData.category = updates.category || null;
      if (updates.completed !== undefined) updateData.completed = updates.completed;
      if (updates.notificationTriggered !== undefined) updateData.notification_triggered = updates.notificationTriggered;
      if (updates.notificationDistance !== undefined) updateData.notification_distance = updates.notificationDistance || null;
      if (updates.coordinates !== undefined) updateData.coordinates = updates.coordinates || null;


      const { error } = await supabase
        .from('tasks')
        .update(updateData)
        .eq('id', taskId)
        .eq('user_id', user.id);

      if (error) throw error;

      setTasks(prevTasks =>
        prevTasks.map(task =>
          task.id === taskId ? { ...task, ...updates } : task
        )
      );
      console.log('Task updated successfully:', taskId, updates);
    } catch (error) {
      console.error('Error updating task:', error);
      throw error;
    }
  };

  const deleteTask = async (taskId: string) => {
    if (!user) return;

    try {
      const { error } = await supabase
        .from('tasks')
        .delete()
        .eq('id', taskId)
        .eq('user_id', user.id);

      if (error) throw error;

      setTasks(prev => prev.filter(task => task.id !== taskId));
      console.log('Task deleted successfully:', taskId);
    } catch (error) {
      console.error('Error deleting task:', error);
      throw error;
    }
  };

  return {
    tasks,
    loading,
    addTask,
    updateTask,
    deleteTask,
  };
};