import { useDispatch, useSelector, TypedUseSelectorHook } from 'react-redux';
import { useMemo, useCallback } from 'react';
import type { RootState, AppDispatch } from './index';
import { useGetTasksQuery } from './api/tasksApi';
import {
  selectTasksWithLocationData,
  selectTasksByCreationDate,
  selectTasksByCategory,
  selectTasksGroupedByDate,
  selectActiveGeofences,
  selectRecentLocationHistory,
  selectLocationPlacesByType,
  selectMostVisitedPlaces,


  selectActiveToasts,
  selectNavigationBreadcrumb,
  selectNetworkHealth,
  selectDashboardData,
} from './selectors';

// Typed hooks
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

// Custom hooks for common operations
export const useAppState = () => {
  const dispatch = useAppDispatch();
  const state = useAppSelector(state => state);
  
  return { state, dispatch };
};

// Tasks hooks using RTK Query
export const useTasks = () => {
  const { data: tasks = [], isLoading: loading, error } = useGetTasksQuery({});
  // Keep other state from the slice
  const stats = useAppSelector(state => state.tasks.stats);
  const filters = useAppSelector(state => state.tasks.filters);
  const sorting = useAppSelector(state => state.tasks.sorting);
  const selectedTasks = useAppSelector(state => state.tasks.selectedTasks);

  return {
    tasks,
    loading,
    error,
    stats,
    filters,
    sorting,
    selectedTasks,
  };
};

export const useFilteredTasks = () => {
  const { tasks, filters, sorting } = useTasks();
  
  return useMemo(() => {
    let filtered = [...tasks];
    
    // Apply status filter
    if (filters.status !== 'all') {
      filtered = filtered.filter(task => 
        filters.status === 'active' ? !task.completed : task.completed
      );
    }
    
    // Apply priority filter (Task doesn't have priority, so skip this filter)
    if (filters.priority !== 'all') {
      // Since Task interface doesn't have priority, we'll skip this filter
      // In a real app, you'd either add priority to Task or remove this filter
    }

    // Apply search filter
    if (filters.search.trim()) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(task =>
        task.text.toLowerCase().includes(searchLower) ||
        task.category?.toLowerCase().includes(searchLower) ||
        task.location?.toLowerCase().includes(searchLower)
      );
    }
    
    // Apply location filter
    if (filters.location.enabled && filters.location.coordinates) {
      filtered = filtered.filter(task => {
        if (!task.coordinates) return false;
        
        // Calculate distance (simplified)
        const distance = Math.sqrt(
          Math.pow(task.coordinates.lat - filters.location.coordinates!.latitude, 2) +
          Math.pow(task.coordinates.lng - filters.location.coordinates!.longitude, 2)
        ) * 111000; // Rough conversion to meters
        
        return distance <= filters.location.radius;
      });
    }
    
    // Apply date range filter
    if (filters.dateRange.start || filters.dateRange.end) {
      filtered = filtered.filter(task => {
        const taskDate = new Date(task.createdAt);
        const start = filters.dateRange.start ? new Date(filters.dateRange.start) : null;
        const end = filters.dateRange.end ? new Date(filters.dateRange.end) : null;
        
        if (start && taskDate < start) return false;
        if (end && taskDate > end) return false;
        return true;
      });
    }
    
    // Apply sorting
    filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (sorting.field) {
        case 'date':
          comparison = new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
          break;
        case 'priority':
          // Since Task doesn't have priority, sort by category instead
          const categoryA = a.category || 'zzz';
          const categoryB = b.category || 'zzz';
          comparison = categoryA.localeCompare(categoryB);
          break;
        case 'alphabetical':
          comparison = a.text.localeCompare(b.text);
          break;
        case 'distance':
          // Would need user location for this
          comparison = 0;
          break;
      }
      
      return sorting.direction === 'desc' ? comparison : -comparison;
    });
    
    return filtered;
  }, [tasks, filters, sorting]);
};

// User hooks
export const useUser = () => {
  const profile = useAppSelector(state => state.user.profile);
  const auth = useAppSelector(state => state.user.auth);
  const loading = useAppSelector(state => state.user.loading);
  const error = useAppSelector(state => state.user.error);
  const onboarding = useAppSelector(state => state.user.onboarding);
  
  const isAuthenticated = auth.isAuthenticated;
  const isOnboardingComplete = onboarding.completed;
  
  return {
    profile,
    auth,
    loading,
    error,
    onboarding,
    isAuthenticated,
    isOnboardingComplete,
  };
};

// Settings hooks
export const useSettings = () => {
  const settings = useAppSelector(state => state.settings.settings);
  const loading = useAppSelector(state => state.settings.loading);
  const error = useAppSelector(state => state.settings.error);
  const hasUnsavedChanges = useAppSelector(state => state.settings.hasUnsavedChanges);
  
  return {
    settings,
    loading,
    error,
    hasUnsavedChanges,
  };
};

// Location hooks
export const useLocation = () => {
  const currentLocation = useAppSelector(state => state.location.currentLocation);
  const isTracking = useAppSelector(state => state.location.isTracking);
  const places = useAppSelector(state => state.location.places);
  const searchResults = useAppSelector(state => state.location.searchResults);
  const loading = useAppSelector(state => state.location.loading);
  const error = useAppSelector(state => state.location.error);
  const permissions = useAppSelector(state => state.location.permissions);
  
  return {
    currentLocation,
    isTracking,
    places,
    searchResults,
    loading,
    error,
    permissions,
  };
};

// Notifications hooks
export const useNotifications = () => {
  const notifications = useAppSelector(state => state.notifications.notifications);
  const unreadCount = useAppSelector(state => state.notifications.unreadCount);
  const loading = useAppSelector(state => state.notifications.loading);
  const error = useAppSelector(state => state.notifications.error);
  const permissions = useAppSelector(state => state.notifications.permissions);
  const settings = useAppSelector(state => state.notifications.settings);
  
  const unreadNotifications = useMemo(() => 
    notifications.filter(n => !n.readAt), 
    [notifications]
  );
  
  return {
    notifications,
    unreadNotifications,
    unreadCount,
    loading,
    error,
    permissions,
    settings,
  };
};

// UI hooks
export const useUI = () => {
  const currentScreen = useAppSelector(state => state.ui.currentScreen);
  const modal = useAppSelector(state => state.ui.modal);
  const loading = useAppSelector(state => state.ui.loading);
  const toasts = useAppSelector(state => state.ui.toasts);
  const forms = useAppSelector(state => state.ui.forms);
  const features = useAppSelector(state => state.ui.features);
  
  // Overlay states
  const showHamburgerMenu = useAppSelector(state => state.ui.showHamburgerMenu);
  const showVoiceInput = useAppSelector(state => state.ui.showVoiceInput);
  const showLocationSettings = useAppSelector(state => state.ui.showLocationSettings);
  const showUserProfile = useAppSelector(state => state.ui.showUserProfile);
  const showWelcome = useAppSelector(state => state.ui.showWelcome);

  
  return {
    currentScreen,
    modal,
    loading,
    toasts,
    forms,
    features,
    overlays: {
      showHamburgerMenu,
      showVoiceInput,
      showLocationSettings,
      showUserProfile,
      showWelcome,

    },
  };
};



// Action creators with hooks
export const useTaskActions = () => {
  const dispatch = useAppDispatch();

  return useMemo(() => ({
    addTask: useCallback((task: any) => {
      // Import and dispatch async action from feature
      import('./features/tasks').then(({ addTaskAsync }) => {
        dispatch(addTaskAsync(task));
      });
    }, [dispatch]),

    updateTask: useCallback((id: string, updates: any) => {
      import('./features/tasks').then(({ updateTaskAsync }) => {
        dispatch(updateTaskAsync({ id, updates }));
      });
    }, [dispatch]),

    deleteTask: useCallback((id: string) => {
      import('./features/tasks').then(({ deleteTaskAsync }) => {
        dispatch(deleteTaskAsync({ id }));
      });
    }, [dispatch]),
  }), [dispatch]);
};

export const useUIActions = () => {
  const dispatch = useAppDispatch();
  
  return useMemo(() => ({
    setCurrentScreen: useCallback((screen: any) => {
      import('./slices/uiSlice').then(({ setCurrentScreen }) => {
        dispatch(setCurrentScreen(screen));
      });
    }, [dispatch]),
    
    showToast: useCallback((toast: any) => {
      import('./slices/uiSlice').then(({ showToast }) => {
        dispatch(showToast(toast));
      });
    }, [dispatch]),
    
    toggleHamburgerMenu: useCallback((show?: boolean) => {
      import('./slices/uiSlice').then(({ toggleHamburgerMenu }) => {
        dispatch(toggleHamburgerMenu(show));
      });
    }, [dispatch]),
  }), [dispatch]);
};

// ===== PERFORMANCE-OPTIMIZED HOOKS WITH MEMOIZED SELECTORS =====

// Enhanced task hooks with memoization
export const useTasksWithLocation = () => {
  return useAppSelector(selectTasksWithLocationData);
};

export const useTasksByCreationDate = () => {
  return useAppSelector(selectTasksByCreationDate);
};

export const useTasksByCategory = () => {
  return useAppSelector(selectTasksByCategory);
};

export const useTasksGroupedByDate = () => {
  return useAppSelector(selectTasksGroupedByDate);
};

// Enhanced location hooks with memoization
export const useActiveGeofences = () => {
  return useAppSelector(selectActiveGeofences);
};

export const useRecentLocationHistory = () => {
  return useAppSelector(selectRecentLocationHistory);
};

export const useLocationPlacesByType = () => {
  return useAppSelector(selectLocationPlacesByType);
};

export const useMostVisitedPlaces = () => {
  return useAppSelector(selectMostVisitedPlaces);
};



// Enhanced UI hooks with memoization
export const useActiveToasts = () => {
  return useAppSelector(selectActiveToasts);
};

export const useNavigationBreadcrumb = () => {
  return useAppSelector(selectNavigationBreadcrumb);
};

// Enhanced network hooks with memoization
export const useNetworkHealth = () => {
  return useAppSelector(selectNetworkHealth);
};

// Dashboard hook with comprehensive memoized data
export const useDashboardData = () => {
  return useAppSelector(selectDashboardData);
};

// Store size monitoring hook
export const useStoreSize = () => {
  const dispatch = useAppDispatch();

  const triggerCleanup = useCallback(() => {
    // Force a cleanup by dispatching a dummy action that will trigger the middleware
    dispatch({ type: 'store/triggerCleanup' });
  }, [dispatch]);

  const getStoreSizeInfo = useCallback(async () => {
    const { getStoreSizeInfo } = await import('./middleware/cleanupMiddleware');
    return getStoreSizeInfo();
  }, []);

  const logStoreSize = useCallback(async () => {
    const { logStoreSizeInfo } = await import('./middleware/cleanupMiddleware');
    logStoreSizeInfo();
  }, []);

  return {
    triggerCleanup,
    getStoreSizeInfo,
    logStoreSize,
  };
};


