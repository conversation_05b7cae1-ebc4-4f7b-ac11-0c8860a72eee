import { configureStore, combineReducers } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { createLogger } from 'redux-logger';
import { createCleanupMiddleware, logStoreSizeInfo } from './middleware/cleanupMiddleware';
import { createPrivacyMiddleware } from './middleware/privacyMiddleware';
import { EncryptionService, createEncryptTransform } from '../services/encryptionService';

// Import RTK Query API services
import { api, apiReducer, apiMiddleware } from './api/baseApi';

// Import feature-based reducers
import { taskReducer } from './features/tasks';
import { userReducer } from './features/user';
import { settingsReducer } from './features/settings';
import { locationReducer } from './features/location';
import { notificationsReducer } from './features/notifications';
import { uiReducer } from './features/ui';
import { networkReducer } from './features/network';

// Create encryption transform for sensitive data
const encryptTransform = createEncryptTransform(['tasks', 'user', 'settings']);

// Redux persist configuration with encryption
const persistConfig = {
  key: 'root',
  storage: AsyncStorage,
  whitelist: ['tasks', 'user', 'settings'], // Only persist these slices
  blacklist: ['ui', 'location', 'network', 'api'], // Don't persist these (runtime state)
  transforms: [encryptTransform], // Apply encryption to persisted data
};

// Combine all reducers
const rootReducer = combineReducers({
  // RTK Query API reducer
  [api.reducerPath]: apiReducer,
  // Feature reducers
  tasks: taskReducer,
  user: userReducer,
  settings: settingsReducer,
  location: locationReducer,
  notifications: notificationsReducer,
  ui: uiReducer,
  network: networkReducer,
});

// Create persisted reducer
const persistedReducer = persistReducer(persistConfig, rootReducer);

// Configure store with middleware
export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) => {
    const middleware = getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [
          'persist/PERSIST',
          'persist/REHYDRATE',
          // RTK Query actions
          'api/executeQuery/pending',
          'api/executeQuery/fulfilled',
          'api/executeQuery/rejected',
          'api/executeMutation/pending',
          'api/executeMutation/fulfilled',
          'api/executeMutation/rejected',
          // RTK Query cache patch actions (for Date serialization)
          'api/queries/queryResultPatched',
          'api/mutations/mutationResultPatched',
        ],
        ignoredPaths: [
          'register',
          'api',
          // Ignore Date objects in RTK Query cache
          'api.queries',
          'api.mutations',
        ],
      },
    })
    // Add RTK Query middleware
    .concat(apiMiddleware);

    // Add cleanup middleware for store size management
    middleware.concat(createCleanupMiddleware({
      tasks: {
        maxCompletedTasks: 50, // Keep fewer completed tasks on mobile
        maxTaskAge: 14 * 24 * 60 * 60 * 1000, // 14 days instead of 30
        cleanupInterval: 3 * 60 * 1000, // Clean every 3 minutes
      },
      location: {
        maxLocationHistory: 30, // Reduce location history
        maxPlaces: 30, // Reduce places
        maxGeofences: 15, // Reduce geofences
        maxLocationAge: 3 * 24 * 60 * 60 * 1000, // 3 days
      },
    }));

    // Add privacy middleware for data protection
    middleware.concat(createPrivacyMiddleware({
      checkInterval: 5 * 60 * 1000, // Check every 5 minutes
      triggerActions: [
        'location/updateCurrentLocation',
        'location/addPlace',
        'tasks/addTask',
        'tasks/updateTask',
      ],
    }));

    // Add logger in development
    if (__DEV__) {
      middleware.concat(
        createLogger({
          collapsed: true,
          duration: true,
          timestamp: true,
          logErrors: true,
          predicate: (getState, action) => {
            // Don't log network and cleanup actions to reduce noise
            return !action.type.startsWith('network/updateNetworkState') &&
                   !action.type.startsWith('network/addMetrics') &&
                   !action.type.includes('cleanup');
          },
        })
      );
    }

    return middleware;
  },
  devTools: __DEV__,
});

// Create persistor
export const persistor = persistStore(store);

// Export types
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Export root reducer type for testing
export type RootReducer = typeof rootReducer;

// Store utilities
export const getStoreState = () => store.getState();
export const dispatchAction = (action: any) => store.dispatch(action);

// Initialize encryption service
EncryptionService.initialize().catch(error => {
  console.error('❌ Failed to initialize encryption service:', error);
});

// Development utilities
if (__DEV__) {
  // Make store available globally for debugging
  (global as any).__REDUX_STORE__ = store;

  // Log store initialization
  console.log('🏪 Redux Store initialized with slices:', Object.keys(rootReducer));

  // Log store size info periodically in development
  setInterval(() => {
    logStoreSizeInfo();
  }, 60000); // Every minute
}
