import { Middleware } from '@reduxjs/toolkit';
import { RootState } from '../index';

// Configuration for cleanup rules
interface CleanupConfig {
  // Task cleanup rules
  tasks: {
    maxCompletedTasks: number;
    maxTaskAge: number; // in milliseconds
    cleanupInterval: number; // in milliseconds
  };
  
  // Location cleanup rules
  location: {
    maxLocationHistory: number;
    maxPlaces: number;
    maxGeofences: number;
    maxLocationAge: number; // in milliseconds
  };
  

  
  // UI cleanup rules
  ui: {
    maxNavigationHistory: number;
    maxToasts: number;
    toastMaxAge: number; // in milliseconds
  };
  
  // Network cleanup rules
  network: {
    maxMetrics: number;
    maxEndpointTests: number;
    maxMetricsAge: number; // in milliseconds
  };
}

// Default cleanup configuration
const defaultCleanupConfig: CleanupConfig = {
  tasks: {
    maxCompletedTasks: 100, // Keep only last 100 completed tasks
    maxTaskAge: 30 * 24 * 60 * 60 * 1000, // 30 days
    cleanupInterval: 5 * 60 * 1000, // Clean every 5 minutes
  },
  location: {
    maxLocationHistory: 50, // Keep only last 50 location points
    maxPlaces: 50, // Keep only 50 places
    maxGeofences: 20, // Keep only 20 geofences
    maxLocationAge: 7 * 24 * 60 * 60 * 1000, // 7 days
  },

  ui: {
    maxNavigationHistory: 10, // Keep only last 10 navigation entries
    maxToasts: 5, // Keep only 5 toasts
    toastMaxAge: 10 * 1000, // 10 seconds
  },
  network: {
    maxMetrics: 100, // Keep only last 100 network metrics
    maxEndpointTests: 20, // Keep only 20 endpoint tests
    maxMetricsAge: 30 * 60 * 1000, // 30 minutes
  },
};

// Store size monitoring
interface StoreSizeInfo {
  totalSize: number;
  sliceSizes: Record<string, number>;
  lastCleanup: number;
  cleanupCount: number;
}

let storeSizeInfo: StoreSizeInfo = {
  totalSize: 0,
  sliceSizes: {},
  lastCleanup: Date.now(),
  cleanupCount: 0,
};

// Utility function to estimate object size in bytes
function estimateObjectSize(obj: any): number {
  const jsonString = JSON.stringify(obj);
  return new Blob([jsonString]).size;
}

// Utility function to check if cleanup is needed
function shouldCleanup(config: CleanupConfig): boolean {
  const now = Date.now();
  const timeSinceLastCleanup = now - storeSizeInfo.lastCleanup;
  return timeSinceLastCleanup >= config.tasks.cleanupInterval;
}

// Cleanup functions for each slice
const cleanupFunctions = {
  tasks: (state: RootState, config: CleanupConfig) => {
    // RTK Query handles task data, so we can't directly access state.tasks.tasks
    // For now, return empty array - task cleanup would need to be handled via API
    return [];
  },
  
  location: (state: RootState, config: CleanupConfig) => {
    const now = Date.now();
    const { maxLocationHistory, maxPlaces, maxGeofences, maxLocationAge } = config.location;
    
    const cleanedState = { ...state.location };
    
    // Clean location history
    cleanedState.history = cleanedState.history
      .filter(location => (now - location.timestamp) <= maxLocationAge)
      .slice(0, maxLocationHistory);
    
    // Clean places (keep most visited)
    if (cleanedState.places.length > maxPlaces) {
      cleanedState.places = cleanedState.places
        .sort((a, b) => b.visits - a.visits)
        .slice(0, maxPlaces);
    }
    
    // Clean geofences (keep most recent)
    if (cleanedState.geofences.length > maxGeofences) {
      cleanedState.geofences = cleanedState.geofences.slice(0, maxGeofences);
    }
    
    return cleanedState;
  },
  

  
  ui: (state: RootState, config: CleanupConfig) => {
    const now = Date.now();
    const { maxNavigationHistory, maxToasts, toastMaxAge } = config.ui;
    
    const cleanedState = { ...state.ui };
    
    // Clean navigation history
    if (cleanedState.navigationHistory.length > maxNavigationHistory) {
      cleanedState.navigationHistory = cleanedState.navigationHistory.slice(-maxNavigationHistory);
    }
    
    // Clean old toasts (ToastState doesn't have createdAt, so just limit by count)
    cleanedState.toasts = cleanedState.toasts.slice(0, maxToasts);
    
    return cleanedState;
  },
  
  network: (state: RootState, config: CleanupConfig) => {
    const now = Date.now();
    const { maxMetrics, maxEndpointTests, maxMetricsAge } = config.network;
    
    const cleanedState = { ...state.network };
    
    // Clean metrics
    cleanedState.metrics = cleanedState.metrics
      .filter(metric => {
        const metricTime = new Date(metric.timestamp).getTime();
        return (now - metricTime) <= maxMetricsAge;
      })
      .slice(0, maxMetrics);
    
    // Clean endpoint tests
    cleanedState.endpointTests = cleanedState.endpointTests
      .filter(test => {
        const testTime = new Date(test.timestamp).getTime();
        return (now - testTime) <= maxMetricsAge;
      })
      .slice(0, maxEndpointTests);
    
    return cleanedState;
  },
};

// Main cleanup middleware
export const createCleanupMiddleware = (config: Partial<CleanupConfig> = {}): Middleware => {
  const finalConfig = { ...defaultCleanupConfig, ...config };
  
  return (store) => (next) => (action) => {
    // Process the action first
    const result = next(action);
    
    // Check if cleanup is needed
    if (shouldCleanup(finalConfig)) {
      const state = store.getState() as RootState;
      
      // Calculate current store size
      const currentSize = estimateObjectSize(state);
      storeSizeInfo.totalSize = currentSize;
      
      // Calculate individual slice sizes
      Object.keys(state).forEach(sliceKey => {
        storeSizeInfo.sliceSizes[sliceKey] = estimateObjectSize(state[sliceKey as keyof RootState]);
      });
      
      // Perform cleanup if store is getting large (> 5MB)
      if (currentSize > 5 * 1024 * 1024) {
        console.log(`🧹 Store cleanup triggered - Current size: ${(currentSize / 1024 / 1024).toFixed(2)}MB`);
        
        // Dispatch cleanup actions
        try {
          // Clean tasks - RTK Query handles task data, so skip for now
          // const cleanedTasks = cleanupFunctions.tasks(state, finalConfig);
          // Task cleanup would need to be handled via API calls
          
          // Clean location data
          const cleanedLocation = cleanupFunctions.location(state, finalConfig);
          if (JSON.stringify(cleanedLocation) !== JSON.stringify(state.location)) {
            store.dispatch({ type: 'location/cleanupLocationData', payload: cleanedLocation });
          }
          

          
          // Clean UI data
          const cleanedUI = cleanupFunctions.ui(state, finalConfig);
          if (JSON.stringify(cleanedUI) !== JSON.stringify(state.ui)) {
            store.dispatch({ type: 'ui/cleanupUIData', payload: cleanedUI });
          }
          
          // Clean network data
          const cleanedNetwork = cleanupFunctions.network(state, finalConfig);
          if (JSON.stringify(cleanedNetwork) !== JSON.stringify(state.network)) {
            store.dispatch({ type: 'network/cleanupNetworkData', payload: cleanedNetwork });
          }
          
          storeSizeInfo.lastCleanup = Date.now();
          storeSizeInfo.cleanupCount++;
          
          const newSize = estimateObjectSize(store.getState());
          const savedSize = currentSize - newSize;
          console.log(`✅ Store cleanup completed - Saved: ${(savedSize / 1024 / 1024).toFixed(2)}MB`);
          
        } catch (error) {
          console.error('❌ Store cleanup failed:', error);
        }
      }
    }
    
    return result;
  };
};

// Export store size monitoring utilities
export const getStoreSizeInfo = (): StoreSizeInfo => ({ ...storeSizeInfo });

export const logStoreSizeInfo = () => {
  console.log('📊 Store Size Info:', {
    totalSize: `${(storeSizeInfo.totalSize / 1024 / 1024).toFixed(2)}MB`,
    sliceSizes: Object.entries(storeSizeInfo.sliceSizes).reduce((acc, [key, size]) => {
      acc[key] = `${(size / 1024).toFixed(2)}KB`;
      return acc;
    }, {} as Record<string, string>),
    lastCleanup: new Date(storeSizeInfo.lastCleanup).toISOString(),
    cleanupCount: storeSizeInfo.cleanupCount,
  });
};

// Export default config for external use
export { defaultCleanupConfig };
export type { CleanupConfig, StoreSizeInfo };
