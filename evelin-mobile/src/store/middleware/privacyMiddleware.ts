/**
 * Privacy Middleware
 * Automatically enforces privacy policies and data retention rules
 */

import { Middleware } from '@reduxjs/toolkit';
import { RootState } from '../index';
import { privacyService } from '../../services/privacyService';
import { LocationDataType } from '../../types/privacy';

// Privacy enforcement configuration
interface PrivacyConfig {
  // Check interval for privacy enforcement (in milliseconds)
  checkInterval: number;
  
  // Actions that trigger privacy checks
  triggerActions: string[];
  
  // Data collection restrictions
  dataCollectionRules: {
    [key in LocationDataType]?: {
      requiresConsent: boolean;
      maxAge: number; // milliseconds
      maxEntries: number;
    };
  };
}

const defaultPrivacyConfig: PrivacyConfig = {
  checkInterval: 5 * 60 * 1000, // 5 minutes
  triggerActions: [
    'location/updateCurrentLocation',
    'location/addPlace',
    'tasks/addTask',
    'tasks/updateTask',
  ],
  dataCollectionRules: {
    location_history: {
      requiresConsent: true,
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
      maxEntries: 1000,
    },
    visited_places: {
      requiresConsent: true,
      maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
      maxEntries: 100,
    },
    search_history: {
      requiresConsent: false,
      maxAge: 24 * 60 * 60 * 1000, // 24 hours
      maxEntries: 50,
    },
    current_location: {
      requiresConsent: true,
      maxAge: Infinity,
      maxEntries: 1,
    },
    task_locations: {
      requiresConsent: true,
      maxAge: 90 * 24 * 60 * 60 * 1000, // 90 days
      maxEntries: 500,
    },
  },
};

// Store privacy enforcement state
let lastPrivacyCheck = 0;
let privacyTimer: NodeJS.Timeout | null = null;

/**
 * Create privacy enforcement middleware
 */
export const createPrivacyMiddleware = (config: Partial<PrivacyConfig> = {}): Middleware => {
  const finalConfig = { ...defaultPrivacyConfig, ...config };

  return (store) => (next) => (action) => {
    // Execute the action first
    const result = next(action);

    // Check if this action should trigger privacy enforcement
    if (shouldEnforcePrivacy(action, finalConfig)) {
      // Run privacy enforcement asynchronously to avoid blocking the action
      setTimeout(() => {
        enforcePrivacyPolicies(store.getState(), finalConfig);
      }, 0);
    }

    return result;
  };
};

/**
 * Check if an action should trigger privacy enforcement
 */
function shouldEnforcePrivacy(action: any, config: PrivacyConfig): boolean {
  // Check if action type is in trigger list
  if (config.triggerActions.includes(action.type)) {
    return true;
  }

  // Check if enough time has passed since last check
  const now = Date.now();
  if (now - lastPrivacyCheck >= config.checkInterval) {
    lastPrivacyCheck = now;
    return true;
  }

  return false;
}

/**
 * Enforce privacy policies based on current state
 */
async function enforcePrivacyPolicies(state: RootState, config: PrivacyConfig): Promise<void> {
  try {
    // Get current privacy settings
    const privacySettings = privacyService.getPrivacySettings();

    // Skip enforcement if privacy service is not initialized
    if (!privacySettings) {
      return;
    }

    // Check if data collection is allowed
    if (!privacySettings.locationDataCollection.enabled) {
      console.log('🔒 Data collection disabled - skipping privacy enforcement');
      return;
    }

    // Enforce data retention policies
    await enforceDataRetention(state, config, privacySettings);

    // Enforce storage limits
    await enforceStorageLimits(state, config, privacySettings);

    // Check for privacy mode
    if (privacySettings.advancedControls.enablePrivacyMode) {
      await enforcePrivacyMode(state);
    }

    console.log('🔒 Privacy policies enforced successfully');
  } catch (error) {
    console.error('❌ Error enforcing privacy policies:', error);
  }
}

/**
 * Enforce data retention policies
 */
async function enforceDataRetention(
  state: RootState, 
  config: PrivacyConfig, 
  privacySettings: any
): Promise<void> {
  const now = Date.now();

  // Check location history retention
  if (state.location?.history) {
    const maxAge = getRetentionPeriodMs(privacySettings.dataRetention.locationHistory);
    const validHistory = state.location.history.filter(
      (entry: any) => (now - entry.timestamp) <= maxAge
    );

    if (validHistory.length !== state.location.history.length) {
      console.log(`🧹 Cleaned ${state.location.history.length - validHistory.length} old location history entries`);
      // Note: In a real implementation, you'd dispatch an action to update the state
    }
  }

  // Check visited places retention
  if (state.location?.places) {
    const maxAge = getRetentionPeriodMs(privacySettings.dataRetention.visitedPlaces);
    const validPlaces = state.location.places.filter(
      (place: any) => {
        const lastVisit = new Date(place.lastVisit).getTime();
        return (now - lastVisit) <= maxAge;
      }
    );

    if (validPlaces.length !== state.location.places.length) {
      console.log(`🧹 Cleaned ${state.location.places.length - validPlaces.length} old visited places`);
    }
  }
}

/**
 * Enforce storage limits
 */
async function enforceStorageLimits(
  state: RootState, 
  config: PrivacyConfig, 
  privacySettings: any
): Promise<void> {
  const limits = privacySettings.storageLimits;

  // Enforce location history limits
  if (state.location?.history && state.location.history.length > limits.maxLocationHistoryEntries) {
    console.log(`🧹 Location history exceeds limit (${state.location.history.length}/${limits.maxLocationHistoryEntries})`);
    // Keep only the most recent entries
    // Note: In a real implementation, you'd dispatch an action to update the state
  }

  // Enforce visited places limits
  if (state.location?.places && state.location.places.length > limits.maxVisitedPlaces) {
    console.log(`🧹 Visited places exceeds limit (${state.location.places.length}/${limits.maxVisitedPlaces})`);
    // Keep only the most visited places
  }

  // Enforce geofences limits
  if (state.location?.geofences && state.location.geofences.length > limits.maxGeofences) {
    console.log(`🧹 Geofences exceeds limit (${state.location.geofences.length}/${limits.maxGeofences})`);
  }
}

/**
 * Enforce privacy mode (minimal data collection)
 */
async function enforcePrivacyMode(state: RootState): Promise<void> {
  console.log('🔒 Privacy mode enabled - enforcing minimal data collection');
  
  // In privacy mode, we should:
  // 1. Clear current location data
  // 2. Disable location history tracking
  // 3. Clear search history
  // 4. Anonymize existing data
  
  // Note: In a real implementation, you'd dispatch actions to clear sensitive data
}

/**
 * Get retention period in milliseconds
 */
function getRetentionPeriodMs(period: string): number {
  const periods: { [key: string]: number } = {
    '1_hour': 60 * 60 * 1000,
    '24_hours': 24 * 60 * 60 * 1000,
    '7_days': 7 * 24 * 60 * 60 * 1000,
    '30_days': 30 * 24 * 60 * 60 * 1000,
    '90_days': 90 * 24 * 60 * 60 * 1000,
    '1_year': 365 * 24 * 60 * 60 * 1000,
    'forever': Infinity,
  };
  return periods[period] || periods['30_days'];
}

/**
 * Start periodic privacy enforcement
 */
export function startPrivacyEnforcement(config: Partial<PrivacyConfig> = {}): void {
  const finalConfig = { ...defaultPrivacyConfig, ...config };

  if (privacyTimer) {
    clearInterval(privacyTimer);
  }

  privacyTimer = setInterval(async () => {
    try {
      // This would need access to the store - in a real implementation,
      // you'd pass the store reference or use a different approach
      console.log('🔒 Running periodic privacy enforcement...');
      await privacyService.enforceRetentionPolicies();
    } catch (error) {
      console.error('❌ Error in periodic privacy enforcement:', error);
    }
  }, finalConfig.checkInterval);

  console.log(`🔒 Started periodic privacy enforcement (every ${finalConfig.checkInterval / 1000}s)`);
}

/**
 * Stop periodic privacy enforcement
 */
export function stopPrivacyEnforcement(): void {
  if (privacyTimer) {
    clearInterval(privacyTimer);
    privacyTimer = null;
    console.log('🔒 Stopped periodic privacy enforcement');
  }
}

/**
 * Check if data collection is allowed for a specific data type
 */
export function isDataCollectionAllowed(dataType: LocationDataType): boolean {
  try {
    return privacyService.isDataCollectionAllowed(dataType);
  } catch (error) {
    console.error('Error checking data collection permission:', error);
    return false; // Fail-safe: deny by default
  }
}

/**
 * Anonymize sensitive data in an object
 */
export function anonymizeData(data: any): any {
  if (!data || typeof data !== 'object') {
    return data;
  }

  const anonymized = { ...data };

  // Remove or hash sensitive fields
  const sensitiveFields = ['coordinates', 'latitude', 'longitude', 'address', 'name'];
  
  sensitiveFields.forEach(field => {
    if (anonymized[field]) {
      if (field === 'coordinates' || field === 'latitude' || field === 'longitude') {
        // Round coordinates to reduce precision
        if (typeof anonymized[field] === 'number') {
          anonymized[field] = Math.round(anonymized[field] * 100) / 100;
        }
      } else {
        // Hash or remove other sensitive fields
        anonymized[field] = `[ANONYMIZED_${field.toUpperCase()}]`;
      }
    }
  });

  return anonymized;
}
