import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { NetworkState } from './networkTypes';
import {
  addNetworkAsync,
  updateNetworkAsync,
  deleteNetworkAsync,
  fetchNetworkAsync,
} from './networkActions';

// Initial state
const initialState: NetworkState = {
  loading: false,
  error: null,
  // Initialize your state properties here
};

// Create the slice
const networkSlice = createSlice({
  name: 'network',
  initialState,
  reducers: {
    // Synchronous actions
    clearNetworkError: (state) => {
      state.error = null;
    },
    resetNetworkState: (state) => {
      return initialState;
    },
    // Add more synchronous actions as needed
  },
  extraReducers: (builder) => {
    // Add Network
    builder
      .addCase(addNetworkAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(addNetworkAsync.fulfilled, (state, action) => {
        state.loading = false;
        // Handle successful addition
      })
      .addCase(addNetworkAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to add network';
      });

    // Update Network
    builder
      .addCase(updateNetworkAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateNetworkAsync.fulfilled, (state, action) => {
        state.loading = false;
        // Handle successful update
      })
      .addCase(updateNetworkAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to update network';
      });

    // Delete Network
    builder
      .addCase(deleteNetworkAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteNetworkAsync.fulfilled, (state, action) => {
        state.loading = false;
        // Handle successful deletion
      })
      .addCase(deleteNetworkAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to delete network';
      });

    // Fetch Network
    builder
      .addCase(fetchNetworkAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchNetworkAsync.fulfilled, (state, action) => {
        state.loading = false;
        // Handle successful fetch
      })
      .addCase(fetchNetworkAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch network';
      });
  },
});

// Export actions
export const {
  clearNetworkError,
  resetNetworkState,
} = networkSlice.actions;

// Export reducer
export default networkSlice.reducer;

// Export slice for store configuration
export { networkSlice };
