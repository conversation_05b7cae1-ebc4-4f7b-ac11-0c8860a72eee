import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../../index';

// Base selectors
export const selectNetworkState = (state: RootState) => state.network;
export const selectNetworkLoading = (state: RootState) => state.network.loading;
export const selectNetworkError = (state: RootState) => state.network.error;

// Memoized selectors
export const selectHasNetworkError = createSelector(
  [selectNetworkError],
  (error) => !!error
);

export const selectIsNetworkLoading = createSelector(
  [selectNetworkLoading],
  (loading) => loading
);

// Add more selectors as needed based on your state structure
