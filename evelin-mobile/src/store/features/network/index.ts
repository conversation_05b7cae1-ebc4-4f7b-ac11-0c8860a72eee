// Export all network-related functionality from a single entry point

// Types
export type {
  NetworkState,
  AddNetworkPayload,
  UpdateNetworkPayload,
  DeleteNetworkPayload,
  NetworkApiResponse,
  NetworkExportData,
  NetworkImportResult,
} from './networkTypes';

// Actions (async thunks)
export {
  addNetworkAsync,
  updateNetworkAsync,
  deleteNetworkAsync,
  fetchNetworkAsync,
  networkActionTypes,
} from './networkActions';

// Slice and synchronous actions
export {
  default as networkReducer,
  networkSlice,
  clearNetworkError,
  resetNetworkState,
} from './networkSlice';

// Selectors
export {
  selectNetworkState,
  selectNetworkLoading,
  selectNetworkError,
  selectHasNetworkError,
  selectIsNetworkLoading,
} from './networkSelectors';

// Feature name constant
export const NETWORK_FEATURE_KEY = 'network' as const;
