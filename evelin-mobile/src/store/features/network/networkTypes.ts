// Network types and interfaces

export interface NetworkState {
  loading: boolean;
  error: string | null;
  // Add your state properties here
}

// Action payload types
export interface AddNetworkPayload {
  // Define payload structure
}

export interface UpdateNetworkPayload {
  id: string;
  // Define update payload structure
}

export interface DeleteNetworkPayload {
  id: string;
}

// API response types
export interface NetworkApiResponse {
  // Define API response structure
}

// Export/import types
export interface NetworkExportData {
  exportDate: string;
  version: string;
  // Add export data structure
}

export interface NetworkImportResult {
  imported: number;
  skipped: number;
  errors: string[];
}
