// Export all settings-related functionality from a single entry point

// Types
export type {
  SettingsState,
  AddSettingsPayload,
  UpdateSettingsPayload,
  DeleteSettingsPayload,
  SettingsApiResponse,
  SettingsExportData,
  SettingsImportResult,
} from './settingsTypes';

// Actions (async thunks)
export {
  addSettingsAsync,
  updateSettingsAsync,
  deleteSettingsAsync,
  fetchSettingsAsync,
  settingsActionTypes,
} from './settingsActions';

// Slice and synchronous actions
export {
  default as settingsReducer,
  settingsSlice,
  clearSettingsError,
  resetSettingsState,
} from './settingsSlice';

// Selectors
export {
  selectSettingsState,
  selectSettingsLoading,
  selectSettingsError,
  selectHasSettingsError,
  selectIsSettingsLoading,
} from './settingsSelectors';

// Feature name constant
export const SETTINGS_FEATURE_KEY = 'settings' as const;
