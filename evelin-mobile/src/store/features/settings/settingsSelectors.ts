import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../../index';

// Base selectors
export const selectSettingsState = (state: RootState) => state.settings;
export const selectSettingsLoading = (state: RootState) => state.settings.loading;
export const selectSettingsError = (state: RootState) => state.settings.error;

// Memoized selectors
export const selectHasSettingsError = createSelector(
  [selectSettingsError],
  (error) => !!error
);

export const selectIsSettingsLoading = createSelector(
  [selectSettingsLoading],
  (loading) => loading
);

// Add more selectors as needed based on your state structure
