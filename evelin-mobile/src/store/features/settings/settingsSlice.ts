import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { SettingsState } from './settingsTypes';
import {
  addSettingsAsync,
  updateSettingsAsync,
  deleteSettingsAsync,
  fetchSettingsAsync,
} from './settingsActions';

// Initial state
const initialState: SettingsState = {
  loading: false,
  error: null,
  // Initialize your state properties here
};

// Create the slice
const settingsSlice = createSlice({
  name: 'settings',
  initialState,
  reducers: {
    // Synchronous actions
    clearSettingsError: (state) => {
      state.error = null;
    },
    resetSettingsState: (state) => {
      return initialState;
    },
    // Add more synchronous actions as needed
  },
  extraReducers: (builder) => {
    // Add Settings
    builder
      .addCase(addSettingsAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(addSettingsAsync.fulfilled, (state, action) => {
        state.loading = false;
        // Handle successful addition
      })
      .addCase(addSettingsAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to add settings';
      });

    // Update Settings
    builder
      .addCase(updateSettingsAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateSettingsAsync.fulfilled, (state, action) => {
        state.loading = false;
        // Handle successful update
      })
      .addCase(updateSettingsAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to update settings';
      });

    // Delete Settings
    builder
      .addCase(deleteSettingsAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteSettingsAsync.fulfilled, (state, action) => {
        state.loading = false;
        // Handle successful deletion
      })
      .addCase(deleteSettingsAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to delete settings';
      });

    // Fetch Settings
    builder
      .addCase(fetchSettingsAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchSettingsAsync.fulfilled, (state, action) => {
        state.loading = false;
        // Handle successful fetch
      })
      .addCase(fetchSettingsAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch settings';
      });
  },
});

// Export actions
export const {
  clearSettingsError,
  resetSettingsState,
} = settingsSlice.actions;

// Export reducer
export default settingsSlice.reducer;

// Export slice for store configuration
export { settingsSlice };
