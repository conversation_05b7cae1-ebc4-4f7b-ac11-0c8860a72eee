// Settings types and interfaces

export interface SettingsState {
  loading: boolean;
  error: string | null;
  // Add your state properties here
}

// Action payload types
export interface AddSettingsPayload {
  // Define payload structure
}

export interface UpdateSettingsPayload {
  id: string;
  // Define update payload structure
}

export interface DeleteSettingsPayload {
  id: string;
}

// API response types
export interface SettingsApiResponse {
  // Define API response structure
}

// Export/import types
export interface SettingsExportData {
  exportDate: string;
  version: string;
  // Add export data structure
}

export interface SettingsImportResult {
  imported: number;
  skipped: number;
  errors: string[];
}
