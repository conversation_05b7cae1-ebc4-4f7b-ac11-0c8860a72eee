// Export all ui-related functionality from a single entry point

// Types
export type {
  UiState,
  AddUiPayload,
  UpdateUiPayload,
  DeleteUiPayload,
  UiApiResponse,
  UiExportData,
  UiImportResult,
} from './uiTypes';

// Actions (async thunks)
export {
  addUiAsync,
  updateUiAsync,
  deleteUiAsync,
  fetchUiAsync,
  uiActionTypes,
} from './uiActions';

// Slice and synchronous actions
export {
  default as uiReducer,
  uiSlice,
  clearUiError,
  resetUiState,
} from './uiSlice';

// Selectors
export {
  selectUiState,
  selectUiLoading,
  selectIsUiLoading,
} from './uiSelectors';

// Feature name constant
export const UI_FEATURE_KEY = 'ui' as const;
