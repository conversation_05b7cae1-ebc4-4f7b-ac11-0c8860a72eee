import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { UiState } from './uiTypes';
import {
  addUiAsync,
  updateUiAsync,
  deleteUiAsync,
  fetchUiAsync,
} from './uiActions';

// Initial state
const initialState: UiState = {
  loading: false,
  error: null,
  // Initialize your state properties here
};

// Create the slice
const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    // Synchronous actions
    clearUiError: (state) => {
      state.error = null;
    },
    resetUiState: (state) => {
      return initialState;
    },
    // Add more synchronous actions as needed
  },
  extraReducers: (builder) => {
    // Add Ui
    builder
      .addCase(addUiAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(addUiAsync.fulfilled, (state, action) => {
        state.loading = false;
        // Handle successful addition
      })
      .addCase(addUiAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to add ui';
      });

    // Update Ui
    builder
      .addCase(updateUiAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateUiAsync.fulfilled, (state, action) => {
        state.loading = false;
        // Handle successful update
      })
      .addCase(updateUiAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to update ui';
      });

    // Delete Ui
    builder
      .addCase(deleteUiAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteUiAsync.fulfilled, (state, action) => {
        state.loading = false;
        // Handle successful deletion
      })
      .addCase(deleteUiAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to delete ui';
      });

    // Fetch Ui
    builder
      .addCase(fetchUiAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUiAsync.fulfilled, (state, action) => {
        state.loading = false;
        // Handle successful fetch
      })
      .addCase(fetchUiAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch ui';
      });
  },
});

// Export actions
export const {
  clearUiError,
  resetUiState,
} = uiSlice.actions;

// Export reducer
export default uiSlice.reducer;

// Export slice for store configuration
export { uiSlice };
