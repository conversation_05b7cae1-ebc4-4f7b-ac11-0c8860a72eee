// Ui types and interfaces

export interface UiState {
  loading: boolean;
  error: string | null;
  // Add your state properties here
}

// Action payload types
export interface AddUiPayload {
  // Define payload structure
}

export interface UpdateUiPayload {
  id: string;
  // Define update payload structure
}

export interface DeleteUiPayload {
  id: string;
}

// API response types
export interface UiApiResponse {
  // Define API response structure
}

// Export/import types
export interface UiExportData {
  exportDate: string;
  version: string;
  // Add export data structure
}

export interface UiImportResult {
  imported: number;
  skipped: number;
  errors: string[];
}
