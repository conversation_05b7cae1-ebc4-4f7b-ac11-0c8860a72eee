# Feature-Based Redux Architecture

This directory contains the new feature-based Redux architecture for the Evelin Mobile app. This structure provides better organization, maintainability, and scalability compared to the previous slice-based approach.

## 📁 Directory Structure

```
src/store/features/
├── index.ts                 # Central export point for all features
├── README.md               # This file
├── tasks/                  # Task management feature
│   ├── index.ts           # Feature exports
│   ├── taskTypes.ts       # TypeScript interfaces and types
│   ├── taskActions.ts     # Async thunks and action creators
│   ├── taskSlice.ts       # Redux slice with reducers
│   └── taskSelectors.ts   # Memoized selectors
├── user/                   # User authentication and profile
│   ├── index.ts
│   ├── userTypes.ts
│   ├── userActions.ts
│   ├── userSlice.ts
│   └── userSelectors.ts
├── location/               # Location tracking and geofencing
│   ├── index.ts
│   ├── locationTypes.ts
│   ├── locationActions.ts
│   ├── locationSlice.ts
│   └── locationSelectors.ts
├── notifications/          # Push notifications and alerts
│   ├── index.ts
│   ├── notificationsTypes.ts
│   ├── notificationsActions.ts
│   ├── notificationsSlice.ts
│   └── notificationsSelectors.ts
├── settings/               # Application settings
│   ├── index.ts
│   ├── settingsTypes.ts
│   ├── settingsActions.ts
│   ├── settingsSlice.ts
│   └── settingsSelectors.ts
├── ui/                     # User interface state
│   ├── index.ts
│   ├── uiTypes.ts
│   ├── uiActions.ts
│   ├── uiSlice.ts
│   └── uiSelectors.ts
└── network/                # Network connectivity and sync
    ├── index.ts
    ├── networkTypes.ts
    ├── networkActions.ts
    ├── networkSlice.ts
    └── networkSelectors.ts
```

## 🏗️ Architecture Benefits

### 1. **Better Organization**
- Each feature is self-contained with all related code in one directory
- Clear separation of concerns between different app features
- Easier to locate and modify feature-specific code

### 2. **Improved Maintainability**
- Changes to one feature don't affect others
- Easier to add new features without touching existing code
- Better code reusability and modularity

### 3. **Enhanced Scalability**
- Features can be developed independently by different team members
- Easy to add new features using the code generator
- Consistent structure across all features

### 4. **Better Testing**
- Each feature can be tested in isolation
- Easier to mock dependencies between features
- More focused unit tests

## 🚀 Usage Examples

### Importing Feature Functionality

```typescript
// Import everything from a feature
import { 
  taskReducer, 
  addTaskAsync, 
  selectActiveTasks,
  TasksState 
} from '../store/features/tasks';

// Import specific items
import { selectUserProfile } from '../store/features/user';
import { showToast } from '../store/features/ui';

// Import from central features index
import { 
  taskReducer, 
  userReducer, 
  FEATURE_KEYS 
} from '../store/features';
```

### Using in Components

```typescript
import React from 'react';
import { useAppSelector, useAppDispatch } from '../store/hooks';
import { selectActiveTasks, addTaskAsync } from '../store/features/tasks';

export const TaskList: React.FC = () => {
  const dispatch = useAppDispatch();
  const activeTasks = useAppSelector(selectActiveTasks);
  
  const handleAddTask = (taskData: any) => {
    dispatch(addTaskAsync(taskData));
  };
  
  return (
    <div>
      {activeTasks.map(task => (
        <div key={task.id}>{task.text}</div>
      ))}
    </div>
  );
};
```

### Store Configuration

```typescript
import { configureStore } from '@reduxjs/toolkit';
import { 
  taskReducer,
  userReducer,
  locationReducer,
  notificationsReducer,
  settingsReducer,
  uiReducer,
  networkReducer
} from './features';

export const store = configureStore({
  reducer: {
    tasks: taskReducer,
    user: userReducer,
    location: locationReducer,
    notifications: notificationsReducer,
    settings: settingsReducer,
    ui: uiReducer,
    network: networkReducer,
  },
});
```

## 🛠️ Development Tools

### Code Generator

Use the Redux feature generator to create new features:

```bash
node scripts/generateReduxFeature.js <featureName>
```

This creates a complete feature structure with:
- TypeScript types and interfaces
- Async thunks and actions
- Redux slice with reducers
- Memoized selectors
- Feature index file

### Migration Script

Migrate existing code to use the new feature structure:

```bash
node scripts/migrateToFeatures.js
```

This automatically updates import statements throughout the codebase.

## 📋 Feature Guidelines

### 1. **File Naming Convention**
- `<feature>Types.ts` - TypeScript interfaces and types
- `<feature>Actions.ts` - Async thunks and action creators
- `<feature>Slice.ts` - Redux slice with reducers
- `<feature>Selectors.ts` - Memoized selectors
- `index.ts` - Feature exports

### 2. **State Structure**
Each feature should have a consistent state structure:

```typescript
interface FeatureState {
  loading: boolean;
  error: string | null;
  // Feature-specific state properties
}
```

### 3. **Selector Patterns**
- Use `createSelector` for memoized selectors
- Provide both base selectors and computed selectors
- Follow naming convention: `select<FeatureName><Property>`

### 4. **Action Patterns**
- Use async thunks for API calls
- Provide proper error handling
- Follow naming convention: `<action><FeatureName>Async`

### 5. **Export Patterns**
Each feature's `index.ts` should export:
- All types and interfaces
- All actions and async thunks
- Slice reducer and synchronous actions
- All selectors
- Feature key constant

## 🔄 Migration from Slice-Based Architecture

### Completed Migrations
- ✅ **Tasks Feature** - Fully migrated with comprehensive functionality
- ✅ **User Feature** - Migrated with profile and authentication support
- 🔄 **Location Feature** - Generated structure, needs implementation
- 🔄 **Notifications Feature** - Generated structure, needs implementation
- 🔄 **Settings Feature** - Generated structure, needs implementation
- 🔄 **UI Feature** - Generated structure, needs implementation
- 🔄 **Network Feature** - Generated structure, needs implementation

### Next Steps
1. Implement the generated feature structures with actual logic
2. Update all import statements using the migration script
3. Test each feature thoroughly
4. Remove old slice files when migration is complete
5. Update documentation and team guidelines

## 🧪 Testing

Each feature should include comprehensive tests:

```typescript
// Example test structure
describe('Tasks Feature', () => {
  describe('Actions', () => {
    it('should add task successfully', () => {
      // Test async thunks
    });
  });
  
  describe('Selectors', () => {
    it('should select active tasks', () => {
      // Test selectors
    });
  });
  
  describe('Reducers', () => {
    it('should handle task addition', () => {
      // Test reducers
    });
  });
});
```

## 📚 Additional Resources

- [Redux Toolkit Documentation](https://redux-toolkit.js.org/)
- [React Redux Hooks](https://react-redux.js.org/api/hooks)
- [Reselect Selectors](https://github.com/reduxjs/reselect)
- [Redux Best Practices](https://redux.js.org/style-guide/style-guide)
