// User profile interface
export interface UserProfile {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  phone?: string;
  preferences: {
    theme: 'light' | 'dark' | 'system';
    language: string;
    timezone: string;
    notifications: {
      email: boolean;
      push: boolean;
      sms: boolean;
    };
  };
  subscription: {
    plan: 'free' | 'premium' | 'enterprise';
    expiresAt?: string;
    features: string[];
  };
  stats: {
    tasksCreated: number;
    tasksCompleted: number;
    streakDays: number;
    joinedAt: string;
    lastActiveAt: string;
  };
}

// Authentication state
export interface AuthState {
  isAuthenticated: boolean;
  accessToken: string | null;
  refreshToken: string | null;
  expiresAt: string | null;
}

// User state interface
export interface UserState {
  profile: UserProfile | null;
  auth: AuthState;
  loading: boolean;
  error: string | null;
  preferences: {
    theme: 'light' | 'dark' | 'system';
    language: string;
    timezone: string;
    notifications: {
      email: boolean;
      push: boolean;
      sms: boolean;
    };
  };
  subscription: {
    plan: 'free' | 'premium' | 'enterprise';
    expiresAt?: string;
    features: string[];
    isActive: boolean;
  };
  stats: {
    tasksCreated: number;
    tasksCompleted: number;
    streakDays: number;
    joinedAt: string;
    lastActiveAt: string;
    totalLoginTime: number;
    lastLoginAt: string;
  };
  session: {
    isActive: boolean;
    startTime: string | null;
    lastActivity: string | null;
    deviceInfo: {
      platform: string;
      version: string;
      deviceId: string;
    } | null;
  };
}

// Action payload types
export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterCredentials {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
}

export interface UpdateProfilePayload extends Partial<Omit<UserProfile, 'id' | 'stats'>> {}

export interface UpdatePreferencesPayload extends Partial<UserProfile['preferences']> {}

export interface UpdateSubscriptionPayload extends Partial<UserProfile['subscription']> {}

export interface UpdateStatsPayload extends Partial<UserProfile['stats']> {}

export interface RefreshTokenPayload {
  refreshToken: string;
}

export interface ResetPasswordPayload {
  email: string;
}

export interface ChangePasswordPayload {
  currentPassword: string;
  newPassword: string;
}

// Authentication response types
export interface AuthResponse {
  user: UserProfile;
  auth: {
    accessToken: string;
    refreshToken: string;
    expiresAt: string;
  };
}

export interface TokenRefreshResponse {
  accessToken: string;
  expiresAt: string;
}

// User activity tracking
export interface UserActivity {
  type: 'login' | 'logout' | 'task_created' | 'task_completed' | 'profile_updated';
  timestamp: string;
  metadata?: Record<string, any>;
}

// User preferences with validation
export interface UserPreferencesSchema {
  theme: {
    value: 'light' | 'dark' | 'system';
    default: 'system';
    options: ['light', 'dark', 'system'];
  };
  language: {
    value: string;
    default: 'en';
    options: string[];
  };
  timezone: {
    value: string;
    default: 'UTC';
    options: string[];
  };
  notifications: {
    email: {
      value: boolean;
      default: true;
    };
    push: {
      value: boolean;
      default: true;
    };
    sms: {
      value: boolean;
      default: false;
    };
  };
}

// Subscription plans configuration
export interface SubscriptionPlan {
  id: 'free' | 'premium' | 'enterprise';
  name: string;
  price: number;
  currency: string;
  interval: 'month' | 'year';
  features: string[];
  limits: {
    tasks: number;
    locations: number;
    notifications: number;
    storage: number; // in MB
  };
}

// User export/import types
export interface UserExportData {
  profile: UserProfile;
  preferences: UserProfile['preferences'];
  stats: UserProfile['stats'];
  exportDate: string;
  version: string;
}

export interface UserImportResult {
  success: boolean;
  imported: string[];
  skipped: string[];
  errors: string[];
}
