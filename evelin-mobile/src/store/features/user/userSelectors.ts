import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../../index';
import { UserProfile, AuthState, SubscriptionPlan } from './userTypes';

// Base selectors
export const selectUserState = (state: RootState) => state.user;
export const selectUserProfile = (state: RootState) => state.user.profile;
export const selectAuthState = (state: RootState) => state.user.auth;
export const selectUserLoading = (state: RootState) => state.user.loading;
export const selectUserError = (state: RootState) => state.user.error;
export const selectUserOnboarding = (state: RootState) => state.user.onboarding;

// Authentication selectors
export const selectIsAuthenticated = createSelector(
  [selectAuthState],
  (auth) => auth.isAuthenticated
);

export const selectAccessToken = createSelector(
  [selectAuthState],
  (auth) => auth.accessToken
);

export const selectRefreshToken = createSelector(
  [selectAuthState],
  (auth) => auth.refreshToken
);

export const selectTokenExpiresAt = createSelector(
  [selectAuthState],
  (auth) => auth.expiresAt
);

export const selectIsTokenExpired = createSelector(
  [selectTokenExpiresAt],
  (expiresAt) => {
    if (!expiresAt) return true;
    return new Date(expiresAt) <= new Date();
  }
);

// User profile selectors
export const selectUserEmail = createSelector(
  [selectUserProfile],
  (profile) => profile?.email
);

export const selectUserFullName = createSelector(
  [selectUserProfile],
  (profile) => {
    if (!profile) return null;
    return `${profile.firstName} ${profile.lastName}`.trim();
  }
);

export const selectUserInitials = createSelector(
  [selectUserProfile],
  (profile) => {
    if (!profile) return null;
    const firstInitial = profile.firstName.charAt(0).toUpperCase();
    const lastInitial = profile.lastName.charAt(0).toUpperCase();
    return `${firstInitial}${lastInitial}`;
  }
);

export const selectUserAvatar = createSelector(
  [selectUserProfile],
  (profile) => profile?.avatar
);

// Profile-based preferences selectors (from UserProfile)
export const selectTheme = createSelector(
  [selectUserProfile],
  (profile) => profile?.preferences?.theme || 'system'
);

export const selectLanguage = createSelector(
  [selectUserProfile],
  (profile) => profile?.preferences?.language || 'en'
);

export const selectTimezone = createSelector(
  [selectUserProfile],
  (profile) => profile?.preferences?.timezone || 'UTC'
);

export const selectNotificationPreferences = createSelector(
  [selectUserProfile],
  (profile) => profile?.preferences?.notifications || { email: true, push: true, sms: false }
);

export const selectEmailNotificationsEnabled = createSelector(
  [selectNotificationPreferences],
  (notifications) => notifications.email
);

export const selectPushNotificationsEnabled = createSelector(
  [selectNotificationPreferences],
  (notifications) => notifications.push
);

export const selectSmsNotificationsEnabled = createSelector(
  [selectNotificationPreferences],
  (notifications) => notifications.sms
);

// Subscription selectors (from UserProfile)
export const selectSubscriptionPlan = createSelector(
  [selectUserProfile],
  (profile) => profile?.subscription?.plan || 'free'
);

export const selectSubscriptionFeatures = createSelector(
  [selectUserProfile],
  (profile) => profile?.subscription?.features || []
);

export const selectIsSubscriptionActive = createSelector(
  [selectUserProfile],
  (profile) => {
    if (!profile?.subscription?.expiresAt) return profile?.subscription?.plan !== 'free';
    return new Date(profile.subscription.expiresAt) > new Date();
  }
);

export const selectSubscriptionExpiresAt = createSelector(
  [selectUserProfile],
  (profile) => profile?.subscription?.expiresAt
);

export const selectIsPremiumUser = createSelector(
  [selectSubscriptionPlan],
  (plan) => plan === 'premium' || plan === 'enterprise'
);

export const selectIsEnterpriseUser = createSelector(
  [selectSubscriptionPlan],
  (plan) => plan === 'enterprise'
);

export const selectHasFeature = (featureName: string) =>
  createSelector(
    [selectSubscriptionFeatures],
    (features) => features.includes(featureName)
  );

// Stats selectors (from UserProfile)
export const selectTasksCreated = createSelector(
  [selectUserProfile],
  (profile) => profile?.stats?.tasksCreated || 0
);

export const selectTasksCompleted = createSelector(
  [selectUserProfile],
  (profile) => profile?.stats?.tasksCompleted || 0
);

export const selectStreakDays = createSelector(
  [selectUserProfile],
  (profile) => profile?.stats?.streakDays || 0
);

export const selectJoinedAt = createSelector(
  [selectUserProfile],
  (profile) => profile?.stats?.joinedAt
);

export const selectLastActiveAt = createSelector(
  [selectUserProfile],
  (profile) => profile?.stats?.lastActiveAt
);

export const selectCompletionRate = createSelector(
  [selectTasksCreated, selectTasksCompleted],
  (created, completed) => {
    if (created === 0) return 0;
    return Math.round((completed / created) * 100);
  }
);

export const selectAccountAge = createSelector(
  [selectJoinedAt],
  (joinedAt) => {
    if (!joinedAt) return 0;
    const joined = new Date(joinedAt);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - joined.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24)); // Days
  }
);

// Onboarding selectors
export const selectIsOnboardingComplete = createSelector(
  [selectUserOnboarding],
  (onboarding) => onboarding.completed
);

export const selectOnboardingCurrentStep = createSelector(
  [selectUserOnboarding],
  (onboarding) => onboarding.currentStep
);

export const selectOnboardingSteps = createSelector(
  [selectUserOnboarding],
  (onboarding) => onboarding.steps
);

// Utility selectors
export const selectUserDisplayName = createSelector(
  [selectUserProfile],
  (profile) => {
    if (!profile) return 'Guest';
    if (profile.firstName && profile.lastName) {
      return `${profile.firstName} ${profile.lastName}`;
    }
    if (profile.firstName) return profile.firstName;
    if (profile.email) return profile.email.split('@')[0];
    return 'User';
  }
);

export const selectIsProfileComplete = createSelector(
  [selectUserProfile],
  (profile) => {
    if (!profile) return false;
    return !!(
      profile.firstName &&
      profile.lastName &&
      profile.email &&
      profile.preferences.timezone &&
      profile.preferences.language
    );
  }
);

export const selectRequiredProfileFields = createSelector(
  [selectUserProfile],
  (profile) => {
    const missing: string[] = [];
    if (!profile) return ['firstName', 'lastName', 'email'];
    
    if (!profile.firstName) missing.push('firstName');
    if (!profile.lastName) missing.push('lastName');
    if (!profile.email) missing.push('email');
    if (!profile.preferences.timezone) missing.push('timezone');
    if (!profile.preferences.language) missing.push('language');
    
    return missing;
  }
);
