import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

// User profile interface
export interface UserProfile {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  phone?: string;
  preferences: {
    theme: 'light' | 'dark' | 'system';
    language: string;
    timezone: string;
    notifications: {
      email: boolean;
      push: boolean;
      sms: boolean;
    };
  };
  subscription: {
    plan: 'free' | 'premium' | 'enterprise';
    expiresAt?: string;
    features: string[];
  };
  stats: {
    tasksCreated: number;
    tasksCompleted: number;
    streakDays: number;
    joinedAt: string;
  };
}

// User state interface
export interface UserState {
  profile: UserProfile | null;
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
  lastSyncAt: string | null;
}

// Initial state
const initialState: UserState = {
  profile: null,
  isAuthenticated: false,
  loading: false,
  error: null,
  lastSyncAt: null,
};

// Async thunks
export const fetchUserProfile = createAsyncThunk(
  'user/fetchProfile',
  async (userId: string, { rejectWithValue }) => {
    try {
      // This would typically fetch from an API
      // For now, return a mock profile
      const mockProfile: UserProfile = {
        id: userId,
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        preferences: {
          theme: 'system',
          language: 'en',
          timezone: 'UTC',
          notifications: {
            email: true,
            push: true,
            sms: false,
          },
        },
        subscription: {
          plan: 'free',
          features: ['basic-tasks', 'location-reminders'],
        },
        stats: {
          tasksCreated: 0,
          tasksCompleted: 0,
          streakDays: 0,
          joinedAt: new Date().toISOString(),
        },
      };
      
      return mockProfile;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch user profile');
    }
  }
);

export const updateUserProfile = createAsyncThunk(
  'user/updateProfile',
  async (updates: Partial<UserProfile>, { rejectWithValue }) => {
    try {
      // This would typically update via an API
      // For now, just return the updates
      return updates;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to update user profile');
    }
  }
);

// User slice
const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    setAuthenticated: (state, action: PayloadAction<boolean>) => {
      state.isAuthenticated = action.payload;
      if (!action.payload) {
        state.profile = null;
      }
    },
    
    setProfile: (state, action: PayloadAction<UserProfile | null>) => {
      state.profile = action.payload;
      state.isAuthenticated = !!action.payload;
    },
    
    updatePreferences: (state, action: PayloadAction<Partial<UserProfile['preferences']>>) => {
      if (state.profile) {
        state.profile.preferences = {
          ...state.profile.preferences,
          ...action.payload,
        };
      }
    },
    
    updateStats: (state, action: PayloadAction<Partial<UserProfile['stats']>>) => {
      if (state.profile) {
        state.profile.stats = {
          ...state.profile.stats,
          ...action.payload,
        };
      }
    },
    
    incrementTasksCreated: (state) => {
      if (state.profile) {
        state.profile.stats.tasksCreated += 1;
      }
    },
    
    incrementTasksCompleted: (state) => {
      if (state.profile) {
        state.profile.stats.tasksCompleted += 1;
      }
    },
    
    clearError: (state) => {
      state.error = null;
    },
    
    setLastSyncAt: (state, action: PayloadAction<string>) => {
      state.lastSyncAt = action.payload;
    },
    
    resetUser: () => initialState,
  },
  
  extraReducers: (builder) => {
    builder
      // Fetch user profile
      .addCase(fetchUserProfile.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchUserProfile.fulfilled, (state, action) => {
        state.loading = false;
        state.profile = action.payload;
        state.isAuthenticated = true;
        state.lastSyncAt = new Date().toISOString();
      })
      .addCase(fetchUserProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Update user profile
      .addCase(updateUserProfile.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateUserProfile.fulfilled, (state, action) => {
        state.loading = false;
        if (state.profile) {
          state.profile = {
            ...state.profile,
            ...action.payload,
          };
        }
        state.lastSyncAt = new Date().toISOString();
      })
      .addCase(updateUserProfile.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

// Export actions
export const {
  setAuthenticated,
  setProfile,
  updatePreferences,
  updateStats,
  incrementTasksCreated,
  incrementTasksCompleted,
  clearError,
  setLastSyncAt,
  resetUser,
} = userSlice.actions;

// Export reducer
export const userReducer = userSlice.reducer;

// Export default
export default userSlice.reducer;
