// Central export point for all Redux features
// This provides a clean API for importing feature functionality

// Tasks feature
export * from './tasks';

// User feature  
export * from './user';

// Location feature
export * from './location';

// Notifications feature
export * from './notifications';

// Settings feature
export * from './settings';

// UI feature
export * from './ui';

// Network feature
export * from './network';

// Feature keys for store configuration
export const FEATURE_KEYS = {
  TASKS: 'tasks',
  USER: 'user', 
  LOCATION: 'location',
  NOTIFICATIONS: 'notifications',
  SETTINGS: 'settings',
  UI: 'ui',
  NETWORK: 'network',
} as const;

// Type for feature keys
export type FeatureKey = typeof FEATURE_KEYS[keyof typeof FEATURE_KEYS];

// Feature metadata for development tools
export const FEATURE_METADATA = {
  [FEATURE_KEYS.TASKS]: {
    name: 'Tasks',
    description: 'Task management with offline support',
    version: '1.0.0',
    dependencies: ['location', 'notifications'],
  },
  [FEATURE_KEYS.USER]: {
    name: 'User',
    description: 'User authentication and profile management',
    version: '1.0.0',
    dependencies: [],
  },
  [FEATURE_KEYS.LOCATION]: {
    name: 'Location',
    description: 'Location tracking and geofencing',
    version: '1.0.0',
    dependencies: ['notifications'],
  },
  [FEATURE_KEYS.NOTIFICATIONS]: {
    name: 'Notifications',
    description: 'Push notifications and alerts',
    version: '1.0.0',
    dependencies: [],
  },
  [FEATURE_KEYS.SETTINGS]: {
    name: 'Settings',
    description: 'Application settings and preferences',
    version: '1.0.0',
    dependencies: ['user'],
  },
  [FEATURE_KEYS.UI]: {
    name: 'UI',
    description: 'User interface state management',
    version: '1.0.0',
    dependencies: [],
  },
  [FEATURE_KEYS.NETWORK]: {
    name: 'Network',
    description: 'Network connectivity and sync status',
    version: '1.0.0',
    dependencies: [],
  },
} as const;

// Helper function to get feature dependencies
export const getFeatureDependencies = (featureKey: FeatureKey): readonly string[] => {
  return FEATURE_METADATA[featureKey]?.dependencies || [];
};

// Helper function to get all feature keys
export const getAllFeatureKeys = (): FeatureKey[] => {
  return Object.values(FEATURE_KEYS);
};

// Helper function to validate feature key
export const isValidFeatureKey = (key: string): key is FeatureKey => {
  return Object.values(FEATURE_KEYS).includes(key as FeatureKey);
};
