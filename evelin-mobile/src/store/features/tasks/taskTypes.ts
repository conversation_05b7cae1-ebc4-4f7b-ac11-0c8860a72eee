import { Task } from '../../../types/task';

// Task filters and sorting interfaces
export interface TaskFilters {
  status: 'all' | 'active' | 'completed';
  priority: 'all' | 'high' | 'medium' | 'low';
  search: string;
  location: {
    enabled: boolean;
    radius: number;
    coordinates?: { latitude: number; longitude: number };
  };
  dateRange: {
    start?: string;
    end?: string;
  };
}

export interface TaskSorting {
  field: 'date' | 'priority' | 'distance' | 'alphabetical';
  direction: 'asc' | 'desc';
}

// Task state interface - RTK Query handles tasks, loading, and error states
export interface TasksState {
  // RTK Query handles tasks data, so we don't store it here
  filters: TaskFilters;
  sorting: TaskSorting;
  selectedTasks: string[];
  lastSync: string | null;
  offline: {
    isOffline: boolean;
    pendingOperations: number;
    lastOfflineSync: string | null;
    conflicts: number;
    storageSize: number;
  };
  sync: {
    isLoading: boolean;
    lastSyncResult: any | null;
    error: string | null;
  };
  stats: {
    total: number;
    active: number;
    completed: number;
    overdue: number;
  };
}

// Action payload types
export interface AddTaskPayload extends Omit<Task, 'id' | 'createdAt'> {}

export interface UpdateTaskPayload {
  id: string;
  updates: Partial<Task>;
}

export interface DeleteTaskPayload {
  id: string;
}

export interface SetFiltersPayload extends Partial<TaskFilters> {}

export interface SetSortingPayload extends Partial<TaskSorting> {}

export interface SyncTasksPayload {
  tasks: Task[];
  timestamp: string;
}

// Async thunk return types
export interface TaskOperationResult {
  task: Task;
  success: boolean;
  error?: string;
}

export interface BulkTaskOperationResult {
  tasks: Task[];
  successCount: number;
  errorCount: number;
  errors: string[];
}

// Task statistics
export interface TaskStats {
  total: number;
  active: number;
  completed: number;
  overdue: number;
  byCategory: Record<string, number>;
  byLocation: Record<string, number>;
  completionRate: number;
  averageCompletionTime: number;
}

// Task search and filter options
export interface TaskSearchOptions {
  query: string;
  filters: TaskFilters;
  sorting: TaskSorting;
  limit?: number;
  offset?: number;
}

// Task export/import types
export interface TaskExportData {
  tasks: Task[];
  exportDate: string;
  version: string;
  metadata: {
    totalTasks: number;
    completedTasks: number;
    categories: string[];
    locations: string[];
  };
}

export interface TaskImportResult {
  imported: number;
  skipped: number;
  errors: string[];
  duplicates: number;
}
