// Export all task-related functionality from a single entry point

// Types
export type {
  TasksState,
  TaskFilters,
  TaskSorting,
  AddTaskPayload,
  UpdateTaskPayload,
  DeleteTaskPayload,
  SetFiltersPayload,
  SetSortingPayload,
  SyncTasksPayload,
  TaskOperationResult,
  BulkTaskOperationResult,
  TaskStats,
  TaskSearchOptions,
  TaskExportData,
  TaskImportResult,
} from './taskTypes';

// Actions (async thunks)
export {
  addTaskAsync,
  updateTaskAsync,
  deleteTaskAsync,
  fetchTasksAsync,
  syncTasksAsync,
  bulkUpdateTasksAsync,
  bulkDeleteTasksAsync,
  loadOfflineTasksAsync,
  clearOfflineTasksAsync,
  taskActionTypes,
} from './taskActions';

// Slice and synchronous actions
export {
  default as taskReducer,
  taskSlice,
  setFilters,
  setSorting,
  toggleTaskSelection,
  clearTaskSelection,
  selectAllTasks,
  updateOfflineState,
  updateStats,
  clearError,
  resetFilters,
  resetSorting,
} from './taskSlice';

// Selectors
export {
  selectTasksState,
  selectTasksData,
  selectTasksLoading,
  selectTasksError,
  selectTaskFilters,
  selectTaskSorting,
  selectSelectedTasks,
  selectLastSync,
  selectOfflineState,
  selectSyncState,
  selectTaskStats,
  selectActiveTasks,
  selectCompletedTasks,
  selectTasksWithLocation,
  selectTasksWithoutLocation,
  selectTasksByCategory,
  selectTasksByLocation,
  selectFilteredTasks,
  selectSortedTasks,
  selectTaskStatistics,
  selectTaskById,
  selectTasksCount,
  selectActiveTasksCount,
  selectCompletedTasksCount,
  selectIsTaskSelected,
  selectHasPendingOperations,
  selectIsSyncing,
  selectLastSyncError,
} from './taskSelectors';

// Feature name constant
export const TASKS_FEATURE_KEY = 'tasks' as const;
