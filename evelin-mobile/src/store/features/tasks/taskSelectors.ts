import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../../index';
import { Task } from '../../../types/task';
import { TaskFilters, TaskSorting, TaskStats } from './taskTypes';
import { tasksApi } from '../../api/tasksApi';

// Base selectors for task slice state (no longer includes tasks data)
export const selectTasksState = (state: RootState) => state.tasks;
export const selectTaskFilters = (state: RootState) => state.tasks.filters;
export const selectTaskSorting = (state: RootState) => state.tasks.sorting;
export const selectSelectedTasks = (state: RootState) => state.tasks.selectedTasks;
export const selectLastSync = (state: RootState) => state.tasks.lastSync;
export const selectOfflineState = (state: RootState) => state.tasks.offline;
export const selectSyncState = (state: RootState) => state.tasks.sync;
export const selectTaskStats = (state: RootState) => state.tasks.stats;

// RTK Query selectors for tasks data
export const selectTasksQuery = (state: RootState) => tasksApi.endpoints.getTasks.select({})(state);
export const selectTasksData = createSelector(
  [selectTasksQuery],
  (tasksQuery) => tasksQuery.data || []
);
export const selectTasksLoading = createSelector(
  [selectTasksQuery],
  (tasksQuery) => tasksQuery.isLoading || (tasksQuery as any).isFetching
);
export const selectTasksError = createSelector(
  [selectTasksQuery],
  (tasksQuery) => tasksQuery.error
);

// Memoized selectors using RTK Query data
export const selectActiveTasks = createSelector(
  [selectTasksData],
  (tasks) => tasks.filter(task => !task.completed)
);

export const selectCompletedTasks = createSelector(
  [selectTasksData],
  (tasks) => tasks.filter(task => task.completed)
);

export const selectTasksWithLocation = createSelector(
  [selectTasksData],
  (tasks) => tasks.filter(task => task.location && task.coordinates)
);

export const selectTasksWithoutLocation = createSelector(
  [selectTasksData],
  (tasks) => tasks.filter(task => !task.coordinates)
);

export const selectTasksByCategory = createSelector(
  [selectTasksData],
  (tasks) => {
    const categories: Record<string, Task[]> = {};
    tasks.forEach(task => {
      const category = task.category || 'uncategorized';
      if (!categories[category]) {
        categories[category] = [];
      }
      categories[category].push(task);
    });
    return categories;
  }
);

export const selectTasksByLocation = createSelector(
  [selectTasksData],
  (tasks) => {
    const locations: Record<string, Task[]> = {};
    tasks.forEach(task => {
      if (task.location) {
        if (!locations[task.location]) {
          locations[task.location] = [];
        }
        locations[task.location].push(task);
      }
    });
    return locations;
  }
);

// Filtered and sorted tasks
export const selectFilteredTasks = createSelector(
  [selectTasksData, selectTaskFilters],
  (tasks, filters) => {
    let filteredTasks = [...tasks];

    // Filter by status
    if (filters.status !== 'all') {
      filteredTasks = filteredTasks.filter(task => 
        filters.status === 'completed' ? task.completed : !task.completed
      );
    }

    // Filter by search
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filteredTasks = filteredTasks.filter(task =>
        task.text.toLowerCase().includes(searchLower) ||
        (task.location && task.location.toLowerCase().includes(searchLower)) ||
        (task.category && task.category.toLowerCase().includes(searchLower))
      );
    }

    // Filter by date range
    if (filters.dateRange.start || filters.dateRange.end) {
      filteredTasks = filteredTasks.filter(task => {
        const taskDate = new Date(task.createdAt);
        const start = filters.dateRange.start ? new Date(filters.dateRange.start) : null;
        const end = filters.dateRange.end ? new Date(filters.dateRange.end) : null;
        
        if (start && taskDate < start) return false;
        if (end && taskDate > end) return false;
        return true;
      });
    }

    return filteredTasks;
  }
);

export const selectSortedTasks = createSelector(
  [selectFilteredTasks, selectTaskSorting],
  (tasks, sorting) => {
    const sortedTasks = [...tasks];
    
    sortedTasks.sort((a, b) => {
      let comparison = 0;
      
      switch (sorting.field) {
        case 'date':
          comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
          break;
        case 'alphabetical':
          comparison = a.text.localeCompare(b.text);
          break;
        case 'priority':
          // Implement priority comparison if priority field exists
          comparison = 0;
          break;
        case 'distance':
          // Implement distance comparison if coordinates exist
          comparison = 0;
          break;
        default:
          comparison = 0;
      }
      
      return sorting.direction === 'desc' ? -comparison : comparison;
    });
    
    return sortedTasks;
  }
);

// Task statistics selectors
export const selectTaskStatistics = createSelector(
  [selectTasksData],
  (tasks): TaskStats => {
    const total = tasks.length;
    const completed = tasks.filter(task => task.completed).length;
    const active = total - completed;
    const overdue = 0; // Implement overdue logic if needed
    
    const byCategory: Record<string, number> = {};
    const byLocation: Record<string, number> = {};
    
    tasks.forEach(task => {
      const category = task.category || 'uncategorized';
      byCategory[category] = (byCategory[category] || 0) + 1;
      
      if (task.location) {
        byLocation[task.location] = (byLocation[task.location] || 0) + 1;
      }
    });
    
    return {
      total,
      active,
      completed,
      overdue,
      byCategory,
      byLocation,
      completionRate: total > 0 ? (completed / total) * 100 : 0,
      averageCompletionTime: 0, // Implement if completion time tracking is added
    };
  }
);

// Individual task selectors
export const selectTaskById = (taskId: string) =>
  createSelector(
    [selectTasksData],
    (tasks) => tasks.find(task => task.id === taskId)
  );

export const selectTasksCount = createSelector(
  [selectTasksData],
  (tasks) => tasks.length
);

export const selectActiveTasksCount = createSelector(
  [selectActiveTasks],
  (activeTasks) => activeTasks.length
);

export const selectCompletedTasksCount = createSelector(
  [selectCompletedTasks],
  (completedTasks) => completedTasks.length
);

// Utility selectors
export const selectIsTaskSelected = (taskId: string) =>
  createSelector(
    [selectSelectedTasks],
    (selectedTasks) => selectedTasks.includes(taskId)
  );

export const selectHasPendingOperations = createSelector(
  [selectOfflineState],
  (offlineState) => offlineState.pendingOperations > 0
);

export const selectIsSyncing = createSelector(
  [selectSyncState],
  (syncState) => syncState.isLoading
);

export const selectLastSyncError = createSelector(
  [selectSyncState],
  (syncState) => syncState.error
);
