import { createAsyncThunk } from '@reduxjs/toolkit';
import { Task } from '../../../types/task';
import { networkService } from '../../../services/networkService';
import { offlineQueueService } from '../../../services/offlineQueueService';
import { offlineStorageService } from '../../../services/offlineStorageService';
import { mobileTaskService } from '../../../services/taskService';
import { 
  AddTaskPayload, 
  UpdateTaskPayload, 
  DeleteTaskPayload,
  TaskOperationResult,
  BulkTaskOperationResult,
  SyncTasksPayload
} from './taskTypes';

// Async thunks for task operations with offline support
export const addTaskAsync = createAsyncThunk(
  'tasks/addTaskAsync',
  async (task: AddTaskPayload): Promise<Task> => {
    const newTask: Task = {
      ...task,
      id: Date.now().toString(),
      createdAt: new Date(),
    };

    if (networkService.isOnline()) {
      try {
        // Try to create task online
        const serverTask = await mobileTaskService.createTask(newTask);
        if (serverTask) {
          // Save to offline storage as backup
          await offlineStorageService.addTaskOffline(serverTask);
          return serverTask;
        }
      } catch (error) {
        console.warn('Failed to create task online, queuing for offline:', error);
      }
    }

    // If offline or online creation failed, queue the operation
    await offlineQueueService.enqueue({
      type: 'CREATE_TASK',
      payload: newTask,
      priority: 'high',
      maxRetries: 3,
    });

    // Save to offline storage immediately
    await offlineStorageService.addTaskOffline(newTask);

    return newTask;
  }
);

export const updateTaskAsync = createAsyncThunk(
  'tasks/updateTaskAsync',
  async ({ id, updates }: UpdateTaskPayload): Promise<Task> => {
    const updatedData = {
      ...updates,
      id,
    };

    if (networkService.isOnline()) {
      try {
        // Try to update task online
        const serverTask = await mobileTaskService.updateTask(id, updates);
        if (serverTask) {
          // Update offline storage
          await offlineStorageService.updateTaskOffline(id, serverTask);
          return serverTask;
        }
      } catch (error) {
        console.warn('Failed to update task online, queuing for offline:', error);
      }
    }

    // If offline or online update failed, queue the operation
    await offlineQueueService.enqueue({
      type: 'UPDATE_TASK',
      payload: updatedData,
      priority: 'medium',
      maxRetries: 3,
    });

    // Update offline storage immediately
    await offlineStorageService.updateTaskOffline(id, updates);

    // Get the updated task from offline storage
    const tasks = await offlineStorageService.getTasksOffline();
    const updatedTask = tasks.find(task => task.id === id);
    if (!updatedTask) {
      throw new Error('Failed to update task offline');
    }

    return updatedTask;
  }
);

export const deleteTaskAsync = createAsyncThunk(
  'tasks/deleteTaskAsync',
  async ({ id }: DeleteTaskPayload): Promise<string> => {
    if (networkService.isOnline()) {
      try {
        // Try to delete task online
        await mobileTaskService.deleteTask(id);
        // Remove from offline storage
        await offlineStorageService.deleteTaskOffline(id);
        return id;
      } catch (error) {
        console.warn('Failed to delete task online, queuing for offline:', error);
      }
    }

    // If offline or online deletion failed, queue the operation
    await offlineQueueService.enqueue({
      type: 'DELETE_TASK',
      payload: { id },
      priority: 'medium',
      maxRetries: 3,
    });

    // Mark as deleted in offline storage
    await offlineStorageService.deleteTaskOffline(id);

    return id;
  }
);

export const fetchTasksAsync = createAsyncThunk(
  'tasks/fetchTasksAsync',
  async (): Promise<Task[]> => {
    if (networkService.isOnline()) {
      try {
        // Try to fetch from server
        const serverTasks = await mobileTaskService.getTasks();
        if (serverTasks && serverTasks.length > 0) {
          // Update offline storage with server data
          await offlineStorageService.saveTasks(serverTasks);
          return serverTasks;
        }
      } catch (error) {
        console.warn('Failed to fetch tasks online, using offline data:', error);
      }
    }

    // Fallback to offline storage
    const offlineTasks = await offlineStorageService.getTasksOffline();
    return offlineTasks || [];
  }
);

export const syncTasksAsync = createAsyncThunk(
  'tasks/syncTasksAsync',
  async (): Promise<SyncTasksPayload> => {
    if (!networkService.isOnline()) {
      throw new Error('Cannot sync while offline');
    }

    try {
      // Process offline queue first
      await offlineQueueService.processQueue();

      // Fetch latest data from server
      const serverTasks = await mobileTaskService.getTasks();
      
      // Update offline storage
      await offlineStorageService.saveTasks(serverTasks);

      return {
        tasks: serverTasks,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Sync failed:', error);
      throw error;
    }
  }
);

export const bulkUpdateTasksAsync = createAsyncThunk(
  'tasks/bulkUpdateTasksAsync',
  async (updates: Array<{ id: string; updates: Partial<Task> }>): Promise<BulkTaskOperationResult> => {
    const results: TaskOperationResult[] = [];
    const errors: string[] = [];

    for (const { id, updates: taskUpdates } of updates) {
      try {
        if (networkService.isOnline()) {
          const serverTask = await mobileTaskService.updateTask(id, taskUpdates);
          if (serverTask) {
            await offlineStorageService.updateTaskOffline(id, serverTask);
            results.push({ task: serverTask, success: true });
            continue;
          }
        }

        // Fallback to offline
        await offlineQueueService.enqueue({
          type: 'UPDATE_TASK',
          payload: { ...taskUpdates, id },
          priority: 'medium',
          maxRetries: 3,
        });

        await offlineStorageService.updateTaskOffline(id, taskUpdates);

        // Get the updated task from offline storage
        const tasks = await offlineStorageService.getTasksOffline();
        const updatedTask = tasks.find(task => task.id === id);
        if (updatedTask) {
          results.push({ task: updatedTask, success: true });
        } else {
          errors.push(`Failed to update task ${id}`);
        }
      } catch (error) {
        errors.push(`Error updating task ${id}: ${(error as Error).message}`);
      }
    }

    return {
      tasks: results.filter(r => r.success).map(r => r.task),
      successCount: results.filter(r => r.success).length,
      errorCount: errors.length,
      errors,
    };
  }
);

export const bulkDeleteTasksAsync = createAsyncThunk(
  'tasks/bulkDeleteTasksAsync',
  async (taskIds: string[]): Promise<BulkTaskOperationResult> => {
    const results: string[] = [];
    const errors: string[] = [];

    for (const id of taskIds) {
      try {
        if (networkService.isOnline()) {
          await mobileTaskService.deleteTask(id);
          await offlineStorageService.deleteTaskOffline(id);
          results.push(id);
          continue;
        }

        // Fallback to offline
        await offlineQueueService.enqueue({
          type: 'DELETE_TASK',
          payload: { id },
          priority: 'medium',
          maxRetries: 3,
        });

        await offlineStorageService.deleteTaskOffline(id);
        results.push(id);
      } catch (error) {
        errors.push(`Error deleting task ${id}: ${(error as Error).message}`);
      }
    }

    return {
      tasks: [], // No tasks returned for delete operations
      successCount: results.length,
      errorCount: errors.length,
      errors,
    };
  }
);

// Utility actions for offline operations
export const loadOfflineTasksAsync = createAsyncThunk(
  'tasks/loadOfflineTasksAsync',
  async (): Promise<Task[]> => {
    const offlineTasks = await offlineStorageService.getTasksOffline();
    return offlineTasks || [];
  }
);

export const clearOfflineTasksAsync = createAsyncThunk(
  'tasks/clearOfflineTasksAsync',
  async (): Promise<void> => {
    await offlineStorageService.clearAllOfflineData();
  }
);

// Export all action types for use in reducers
export const taskActionTypes = {
  addTaskAsync,
  updateTaskAsync,
  deleteTaskAsync,
  fetchTasksAsync,
  syncTasksAsync,
  bulkUpdateTasksAsync,
  bulkDeleteTasksAsync,
  loadOfflineTasksAsync,
  clearOfflineTasksAsync,
} as const;
