import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Task } from '../../../types/task';
import {
  TasksState,
  TaskFilters,
  TaskSorting,
  SetFiltersPayload,
  SetSortingPayload
} from './taskTypes';

// RTK Query integration - we'll listen to API state changes
import { tasksApi } from '../../api/tasksApi';

// Simplified initial state - RTK Query handles loading/error states
const initialState: Omit<TasksState, 'tasks' | 'loading' | 'error'> = {
  filters: {
    status: 'all',
    priority: 'all',
    search: '',
    location: {
      enabled: false,
      radius: 1000,
    },
    dateRange: {},
  },
  sorting: {
    field: 'date',
    direction: 'desc',
  },
  selectedTasks: [],
  lastSync: null,
  offline: {
    isOffline: false,
    pendingOperations: 0,
    lastOfflineSync: null,
    conflicts: 0,
    storageSize: 0,
  },
  sync: {
    isLoading: false,
    lastSyncResult: null,
    error: null,
  },
  stats: {
    total: 0,
    active: 0,
    completed: 0,
    overdue: 0,
  },
};

// Create the slice
const taskSlice = createSlice({
  name: 'tasks',
  initialState,
  reducers: {
    // Synchronous actions
    setFilters: (state, action: PayloadAction<SetFiltersPayload>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    setSorting: (state, action: PayloadAction<SetSortingPayload>) => {
      state.sorting = { ...state.sorting, ...action.payload };
    },
    toggleTaskSelection: (state, action: PayloadAction<string>) => {
      const taskId = action.payload;
      const index = state.selectedTasks.indexOf(taskId);
      if (index > -1) {
        state.selectedTasks.splice(index, 1);
      } else {
        state.selectedTasks.push(taskId);
      }
    },
    clearTaskSelection: (state) => {
      state.selectedTasks = [];
    },
    selectAllTasks: (state, action: PayloadAction<Task[]>) => {
      // Now we need to pass tasks as payload since we don't store them in state
      state.selectedTasks = action.payload.map(task => task.id);
    },
    updateOfflineState: (state, action: PayloadAction<Partial<TasksState['offline']>>) => {
      state.offline = { ...state.offline, ...action.payload };
    },
    updateStats: (state, action: PayloadAction<Task[]>) => {
      // Now we need to pass tasks as payload since we don't store them in state
      const tasks = action.payload;
      const total = tasks.length;
      const completed = tasks.filter(task => task.completed).length;
      const active = total - completed;

      state.stats = {
        total,
        active,
        completed,
        overdue: 0, // Implement overdue logic if needed
      };
    },
    clearError: (state) => {
      state.sync.error = null;
    },
    resetFilters: (state) => {
      state.filters = initialState.filters;
    },
    resetSorting: (state) => {
      state.sorting = initialState.sorting;
    },
    // Sync operations - these still need to be handled manually
    setSyncLoading: (state, action: PayloadAction<boolean>) => {
      state.sync.isLoading = action.payload;
    },
    setSyncResult: (state, action: PayloadAction<any>) => {
      state.sync.lastSyncResult = action.payload;
      state.lastSync = new Date().toISOString();
    },
    setSyncError: (state, action: PayloadAction<string | null>) => {
      state.sync.error = action.payload;
    },
  },
  // RTK Query handles all async operations now, so no extraReducers needed
  extraReducers: (builder) => {
    // We can listen to RTK Query actions if needed for sync operations
    // For now, we'll handle sync manually through actions
  },
});

// Export actions
export const {
  setFilters,
  setSorting,
  toggleTaskSelection,
  clearTaskSelection,
  selectAllTasks,
  updateOfflineState,
  updateStats,
  clearError,
  resetFilters,
  resetSorting,
  setSyncLoading,
  setSyncResult,
  setSyncError,
} = taskSlice.actions;

// Export reducer
export default taskSlice.reducer;

// Export slice for store configuration
export { taskSlice };
