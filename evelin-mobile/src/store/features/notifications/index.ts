// Export all notifications-related functionality from a single entry point

// Types
export type {
  NotificationsState,
  AddNotificationsPayload,
  UpdateNotificationsPayload,
  DeleteNotificationsPayload,
  NotificationsApiResponse,
  NotificationsExportData,
  NotificationsImportResult,
} from './notificationsTypes';

// Actions (async thunks)
export {
  addNotificationsAsync,
  updateNotificationsAsync,
  deleteNotificationsAsync,
  fetchNotificationsAsync,
  notificationsActionTypes,
} from './notificationsActions';

// Slice and synchronous actions
export {
  default as notificationsReducer,
  notificationsSlice,
  clearNotificationsError,
  resetNotificationsState,
} from './notificationsSlice';

// Selectors
export {
  selectNotificationsState,
  selectNotificationsLoading,
  selectNotificationsError,
  selectHasNotificationsError,
  selectIsNotificationsLoading,
} from './notificationsSelectors';

// Feature name constant
export const NOTIFICATIONS_FEATURE_KEY = 'notifications' as const;
