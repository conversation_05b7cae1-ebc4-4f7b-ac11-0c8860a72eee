import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { NotificationsState } from './notificationsTypes';
import {
  addNotificationsAsync,
  updateNotificationsAsync,
  deleteNotificationsAsync,
  fetchNotificationsAsync,
} from './notificationsActions';

// Initial state
const initialState: NotificationsState = {
  loading: false,
  error: null,
  // Initialize your state properties here
};

// Create the slice
const notificationsSlice = createSlice({
  name: 'notifications',
  initialState,
  reducers: {
    // Synchronous actions
    clearNotificationsError: (state) => {
      state.error = null;
    },
    resetNotificationsState: (state) => {
      return initialState;
    },
    // Add more synchronous actions as needed
  },
  extraReducers: (builder) => {
    // Add Notifications
    builder
      .addCase(addNotificationsAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(addNotificationsAsync.fulfilled, (state, action) => {
        state.loading = false;
        // Handle successful addition
      })
      .addCase(addNotificationsAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to add notifications';
      });

    // Update Notifications
    builder
      .addCase(updateNotificationsAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateNotificationsAsync.fulfilled, (state, action) => {
        state.loading = false;
        // Handle successful update
      })
      .addCase(updateNotificationsAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to update notifications';
      });

    // Delete Notifications
    builder
      .addCase(deleteNotificationsAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteNotificationsAsync.fulfilled, (state, action) => {
        state.loading = false;
        // Handle successful deletion
      })
      .addCase(deleteNotificationsAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to delete notifications';
      });

    // Fetch Notifications
    builder
      .addCase(fetchNotificationsAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchNotificationsAsync.fulfilled, (state, action) => {
        state.loading = false;
        // Handle successful fetch
      })
      .addCase(fetchNotificationsAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch notifications';
      });
  },
});

// Export actions
export const {
  clearNotificationsError,
  resetNotificationsState,
} = notificationsSlice.actions;

// Export reducer
export default notificationsSlice.reducer;

// Export slice for store configuration
export { notificationsSlice };
