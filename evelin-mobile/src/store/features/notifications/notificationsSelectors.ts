import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../../index';

// Base selectors
export const selectNotificationsState = (state: RootState) => state.notifications;
export const selectNotificationsLoading = (state: RootState) => state.notifications.loading;
export const selectNotificationsError = (state: RootState) => state.notifications.error;

// Memoized selectors
export const selectHasNotificationsError = createSelector(
  [selectNotificationsError],
  (error) => !!error
);

export const selectIsNotificationsLoading = createSelector(
  [selectNotificationsLoading],
  (loading) => loading
);

// Add more selectors as needed based on your state structure
