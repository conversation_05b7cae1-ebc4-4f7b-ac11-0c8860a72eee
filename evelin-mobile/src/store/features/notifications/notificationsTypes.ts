// Notifications types and interfaces

export interface NotificationsState {
  loading: boolean;
  error: string | null;
  // Add your state properties here
}

// Action payload types
export interface AddNotificationsPayload {
  // Define payload structure
}

export interface UpdateNotificationsPayload {
  id: string;
  // Define update payload structure
}

export interface DeleteNotificationsPayload {
  id: string;
}

// API response types
export interface NotificationsApiResponse {
  // Define API response structure
}

// Export/import types
export interface NotificationsExportData {
  exportDate: string;
  version: string;
  // Add export data structure
}

export interface NotificationsImportResult {
  imported: number;
  skipped: number;
  errors: string[];
}
