// Location types and interfaces

export interface LocationState {
  loading: boolean;
  error: string | null;
  // Add your state properties here
}

// Action payload types
export interface AddLocationPayload {
  // Define payload structure
}

export interface UpdateLocationPayload {
  id: string;
  // Define update payload structure
}

export interface DeleteLocationPayload {
  id: string;
}

// API response types
export interface LocationApiResponse {
  // Define API response structure
}

// Export/import types
export interface LocationExportData {
  exportDate: string;
  version: string;
  // Add export data structure
}

export interface LocationImportResult {
  imported: number;
  skipped: number;
  errors: string[];
}
