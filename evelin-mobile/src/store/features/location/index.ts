// Export all location-related functionality from a single entry point

// Types
export type {
  LocationState,
  AddLocationPayload,
  UpdateLocationPayload,
  DeleteLocationPayload,
  LocationApiResponse,
  LocationExportData,
  LocationImportResult,
} from './locationTypes';

// Actions (async thunks)
export {
  addLocationAsync,
  updateLocationAsync,
  deleteLocationAsync,
  fetchLocationAsync,
  locationActionTypes,
} from './locationActions';

// Slice and synchronous actions
export {
  default as locationReducer,
  locationSlice,
  clearLocationError,
  resetLocationState,
} from './locationSlice';

// Selectors
export {
  selectLocationState,
  selectLocationLoading,
  selectLocationError,
  selectHasLocationError,
  selectIsLocationLoading,
} from './locationSelectors';

// Feature name constant
export const LOCATION_FEATURE_KEY = 'location' as const;
