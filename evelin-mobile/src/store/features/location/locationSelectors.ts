import { createSelector } from '@reduxjs/toolkit';
import { RootState } from '../../index';

// Base selectors
export const selectLocationState = (state: RootState) => state.location;
export const selectLocationLoading = (state: RootState) => state.location.loading;
export const selectLocationError = (state: RootState) => state.location.error;

// Memoized selectors
export const selectHasLocationError = createSelector(
  [selectLocationError],
  (error) => !!error
);

export const selectIsLocationLoading = createSelector(
  [selectLocationLoading],
  (loading) => loading
);

// Add more selectors as needed based on your state structure
