import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { LocationState } from './locationTypes';
import {
  addLocationAsync,
  updateLocationAsync,
  deleteLocationAsync,
  fetchLocationAsync,
} from './locationActions';

// Initial state
const initialState: LocationState = {
  loading: false,
  error: null,
  // Initialize your state properties here
};

// Create the slice
const locationSlice = createSlice({
  name: 'location',
  initialState,
  reducers: {
    // Synchronous actions
    clearLocationError: (state) => {
      state.error = null;
    },
    resetLocationState: (state) => {
      return initialState;
    },
    // Add more synchronous actions as needed
  },
  extraReducers: (builder) => {
    // Add Location
    builder
      .addCase(addLocationAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(addLocationAsync.fulfilled, (state, action) => {
        state.loading = false;
        // Handle successful addition
      })
      .addCase(addLocationAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to add location';
      });

    // Update Location
    builder
      .addCase(updateLocationAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateLocationAsync.fulfilled, (state, action) => {
        state.loading = false;
        // Handle successful update
      })
      .addCase(updateLocationAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to update location';
      });

    // Delete Location
    builder
      .addCase(deleteLocationAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteLocationAsync.fulfilled, (state, action) => {
        state.loading = false;
        // Handle successful deletion
      })
      .addCase(deleteLocationAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to delete location';
      });

    // Fetch Location
    builder
      .addCase(fetchLocationAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchLocationAsync.fulfilled, (state, action) => {
        state.loading = false;
        // Handle successful fetch
      })
      .addCase(fetchLocationAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch location';
      });
  },
});

// Export actions
export const {
  clearLocationError,
  resetLocationState,
} = locationSlice.actions;

// Export reducer
export default locationSlice.reducer;

// Export slice for store configuration
export { locationSlice };
