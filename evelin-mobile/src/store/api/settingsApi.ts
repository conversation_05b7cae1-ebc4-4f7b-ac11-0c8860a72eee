import { api } from './baseApi';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Settings types
export interface AppSettings {
  darkMode: boolean;
  soundEnabled: boolean;
  notificationsEnabled: boolean;
  locationTrackingEnabled: boolean;
  defaultNotificationDistance: number;
  voiceLanguage: string;
  autoSync: boolean;
  offlineMode: boolean;
  debugMode: boolean;
}

export interface UserPreferences {
  id?: string;
  user_id: string;
  settings: AppSettings;
  created_at: string;
  updated_at: string;
}

// Default settings
const defaultSettings: AppSettings = {
  darkMode: false,
  soundEnabled: true,
  notificationsEnabled: true,
  locationTrackingEnabled: true,
  defaultNotificationDistance: 200,
  voiceLanguage: 'en-US',
  autoSync: true,
  offlineMode: false,
  debugMode: false,
};

// Define the settings API endpoints
export const settingsApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Load app settings from AsyncStorage
    loadAppSettings: builder.query<AppSettings, void>({
      queryFn: async () => {
        try {
          const settingsJson = await AsyncStorage.getItem('appSettings');
          if (settingsJson) {
            const savedSettings = JSON.parse(settingsJson);
            return { data: { ...defaultSettings, ...savedSettings } };
          }
          return { data: defaultSettings };
        } catch (error: any) {
          console.error('Error loading app settings:', error);
          return { data: defaultSettings };
        }
      },
      providesTags: [{ type: 'Settings', id: 'APP' }],
      // Keep settings cached for a while
      keepUnusedDataFor: 300, // 5 minutes
    }),

    // Save app settings to AsyncStorage
    saveAppSettings: builder.mutation<AppSettings, Partial<AppSettings>>({
      queryFn: async (newSettings) => {
        try {
          // Get current settings first
          const currentSettingsJson = await AsyncStorage.getItem('appSettings');
          const currentSettings = currentSettingsJson 
            ? JSON.parse(currentSettingsJson) 
            : defaultSettings;

          // Merge with new settings
          const updatedSettings = { ...currentSettings, ...newSettings };

          // Save to AsyncStorage
          await AsyncStorage.setItem('appSettings', JSON.stringify(updatedSettings));

          console.log('✅ App settings saved:', updatedSettings);
          return { data: updatedSettings };
        } catch (error: any) {
          console.error('Error saving app settings:', error);
          throw error;
        }
      },
      invalidatesTags: [{ type: 'Settings', id: 'APP' }],
      // Optimistic update
      async onQueryStarted(newSettings, { dispatch, queryFulfilled }) {
        const patchResult = dispatch(
          settingsApi.util.updateQueryData('loadAppSettings', undefined, (draft: any) => {
            Object.assign(draft, newSettings);
          })
        );

        try {
          await queryFulfilled;
        } catch {
          // Revert optimistic update on error
          patchResult.undo();
        }
      },
    }),

    // Load user preferences from server
    loadUserPreferences: builder.query<UserPreferences | null, void>({
      query: () => ({
        table: 'user_preferences',
        operation: 'select',
        columns: '*',
        single: true,
      }),
      providesTags: [{ type: 'Settings', id: 'USER_PREFS' }],
      transformResponse: (response: any): UserPreferences | null => {
        if (!response) return null;
        
        return {
          id: response.id,
          user_id: response.user_id,
          settings: response.settings || defaultSettings,
          created_at: response.created_at,
          updated_at: response.updated_at,
        };
      },
      // Handle case where preferences don't exist
      transformErrorResponse: (response: any) => {
        if (response.status === 400 && response.data?.code === 'PGRST116') {
          // No rows returned - preferences don't exist yet
          return null;
        }
        return response;
      },
    }),

    // Save user preferences to server
    saveUserPreferences: builder.mutation<UserPreferences, Partial<AppSettings>>({
      query: (settings) => ({
        table: 'user_preferences',
        operation: 'upsert',
        body: {
          settings: settings,
          updated_at: new Date().toISOString(),
        },
        single: true,
      }),
      invalidatesTags: [{ type: 'Settings', id: 'USER_PREFS' }],
      transformResponse: (response: any): UserPreferences => ({
        id: response.id,
        user_id: response.user_id,
        settings: response.settings,
        created_at: response.created_at,
        updated_at: response.updated_at,
      }),
      // Also update local settings
      async onQueryStarted(settings, { dispatch, queryFulfilled }) {
        try {
          await queryFulfilled;
          // Update local settings as well
          dispatch(settingsApi.endpoints.saveAppSettings.initiate(settings));
        } catch (error) {
          console.error('Error syncing user preferences to local storage:', error);
        }
      },
    }),

    // Sync settings between local and server
    syncSettings: builder.mutation<{ local: AppSettings; server: UserPreferences | null }, void>({
      queryFn: async (arg, api): Promise<{ data: { local: AppSettings; server: UserPreferences | null } }> => {
        try {
          // Load both local and server settings
          const localResult: any = await api.dispatch(
            settingsApi.endpoints.loadAppSettings.initiate()
          );
          const serverResult: any = await api.dispatch(
            settingsApi.endpoints.loadUserPreferences.initiate()
          );

          if (localResult.error || serverResult.error) {
            throw new Error('Failed to load settings for sync');
          }

          const localSettings: AppSettings = localResult.data!;
          const serverPreferences: UserPreferences | null = serverResult.data;

          // Determine which is more recent
          if (serverPreferences) {
            const serverUpdated = new Date(serverPreferences.updated_at);
            const localUpdated = new Date(); // Assume local is current

            // If server is more recent, update local
            if (serverUpdated > localUpdated) {
              await api.dispatch(
                settingsApi.endpoints.saveAppSettings.initiate(serverPreferences.settings)
              );
              return { data: { local: serverPreferences.settings, server: serverPreferences } };
            }
          }

          // If local is more recent or server doesn't exist, update server
          await api.dispatch(
            settingsApi.endpoints.saveUserPreferences.initiate(localSettings)
          );

          return { data: { local: localSettings, server: serverPreferences } };
        } catch (error: any) {
          console.error('Error syncing settings:', error);
          throw error;
        }
      },
      invalidatesTags: [
        { type: 'Settings', id: 'APP' },
        { type: 'Settings', id: 'USER_PREFS' },
      ],
    }),

    // Reset settings to defaults
    resetSettings: builder.mutation<AppSettings, void>({
      queryFn: async () => {
        try {
          // Clear AsyncStorage
          await AsyncStorage.removeItem('appSettings');
          
          console.log('✅ Settings reset to defaults');
          return { data: defaultSettings };
        } catch (error: any) {
          console.error('Error resetting settings:', error);
          throw error;
        }
      },
      invalidatesTags: [
        { type: 'Settings', id: 'APP' },
        { type: 'Settings', id: 'USER_PREFS' },
      ],
    }),

    // Export settings
    exportSettings: builder.query<string, void>({
      queryFn: async (arg, api): Promise<{ data: string }> => {
        try {
          const localResult: any = await api.dispatch(
            settingsApi.endpoints.loadAppSettings.initiate()
          );
          const serverResult: any = await api.dispatch(
            settingsApi.endpoints.loadUserPreferences.initiate()
          );

          const exportData = {
            local: localResult.data,
            server: serverResult.data,
            exportedAt: new Date().toISOString(),
            version: '1.0',
          };

          return { data: JSON.stringify(exportData, null, 2) };
        } catch (error: any) {
          console.error('Error exporting settings:', error);
          throw error;
        }
      },
    }),

    // Import settings
    importSettings: builder.mutation<AppSettings, string>({
      queryFn: async (settingsJson) => {
        try {
          const importData = JSON.parse(settingsJson);
          
          if (!importData.local || !importData.version) {
            return { 
              error: { 
                status: 400, 
                data: null, 
                error: 'Invalid settings format' 
              } 
            };
          }

          // Validate settings structure
          const settings = { ...defaultSettings, ...importData.local };

          // Save to AsyncStorage
          await AsyncStorage.setItem('appSettings', JSON.stringify(settings));

          console.log('✅ Settings imported successfully');
          return { data: settings };
        } catch (error: any) {
          console.error('Error importing settings:', error);
          throw error;
        }
      },
      invalidatesTags: [
        { type: 'Settings', id: 'APP' },
        { type: 'Settings', id: 'USER_PREFS' },
      ],
    }),
  }),
});

// Export hooks for use in components
export const {
  useLoadAppSettingsQuery,
  useSaveAppSettingsMutation,
  useLoadUserPreferencesQuery,
  useSaveUserPreferencesMutation,
  useSyncSettingsMutation,
  useResetSettingsMutation,
  useExportSettingsQuery,
  useImportSettingsMutation,
} = settingsApi;
