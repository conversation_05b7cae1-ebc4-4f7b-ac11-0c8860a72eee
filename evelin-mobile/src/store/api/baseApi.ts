import { createApi, fetchBaseQuery, BaseQueryFn } from '@reduxjs/toolkit/query/react';
import { getSupabaseClient } from '../../services/supabase';
import { networkService } from '../../services/networkService';
import { offlineQueueService } from '../../services/offlineQueueService';
import { offlineStorageService } from '../../services/offlineStorageService';
import { conflictResolutionService, ConflictResolutionStrategy } from '../../services/conflictResolutionService';

// Enhanced base query with comprehensive offline-first support
const supabaseBaseQuery: BaseQueryFn<
  {
    url?: string;
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
    body?: any;
    table?: string;
    operation?: 'select' | 'insert' | 'update' | 'delete' | 'upsert';
    filters?: Record<string, any>;
    columns?: string;
    single?: boolean;
    offlineFirst?: boolean;
    conflictResolution?: ConflictResolutionStrategy;
    atomicGroup?: string;
    rollbackData?: any;
  },
  unknown,
  {
    status: number;
    data?: any;
    error?: string;
    offline?: boolean;
    fromCache?: boolean;
    conflictDetected?: boolean;
  }
> = async (args, api, extraOptions) => {
  try {
    const isOffline = networkService.isOffline();
    const isReadOperation = args.operation === 'select';

    // Handle offline-first reads
    if (isReadOperation && (isOffline || args.offlineFirst)) {
      try {
        // Try to get data from offline storage first
        const offlineData = await getOfflineData(args);
        if (offlineData) {
          // If online, sync in background
          if (!isOffline) {
            backgroundSync(args);
          }
          return {
            data: offlineData,
            meta: { fromCache: true, offline: isOffline }
          };
        }
      } catch (error) {
        console.warn('Failed to get offline data:', error);
      }
    }

    // Handle offline writes
    if (!isReadOperation && isOffline) {
      // Queue the operation with enhanced metadata
      const operationId = await offlineQueueService.enqueue({
        type: `${args.operation?.toUpperCase()}_${args.table?.toUpperCase()}` as any,
        payload: args,
        priority: args.operation === 'delete' ? 'high' : 'medium',
        maxRetries: 3,
        atomicGroup: args.atomicGroup,
        rollbackData: args.rollbackData,
        metadata: {
          conflictResolution: args.conflictResolution || 'timestamp-based',
          networkCondition: 'offline',
        },
      });

      // Store optimistically in offline storage
      await storeOfflineOptimistic(args);

      // Return optimistic response
      return {
        data: args.body ? {
          ...args.body,
          id: args.body.id || `temp-${Date.now()}`,
          _optimistic: true,
          _operationId: operationId
        } : null,
        meta: { offline: true, optimistic: true }
      };
    }

    // Handle Supabase operations
    if (args.table && args.operation) {
      const supabase = await getSupabaseClient();
      const { data: { user } } = await supabase.auth.getUser();
      
      if (!user && args.operation !== 'select') {
        return {
          error: {
            status: 401,
            data: { error: 'User not authenticated' },
            error: 'Authentication required'
          }
        };
      }

      let query: any = supabase.from(args.table);

      switch (args.operation) {
        case 'select':
          query = query.select(args.columns || '*');
          
          // Apply filters
          if (args.filters) {
            Object.entries(args.filters).forEach(([key, value]) => {
              if (key === 'user_id' && user) {
                query = query.eq(key, user.id);
              } else {
                query = query.eq(key, value);
              }
            });
          } else if (user) {
            // Default to user-scoped queries
            query = query.eq('user_id', user.id);
          }

          // Apply ordering for consistent results
          query = query.order('created_at', { ascending: false });
          break;

        case 'insert':
          const insertData = {
            ...args.body,
            user_id: user?.id,
            created_at: new Date().toISOString(),
          };
          query = query.insert([insertData]).select();
          break;

        case 'update':
          query = query
            .update(args.body)
            .eq('id', args.filters?.id)
            .eq('user_id', user?.id)
            .select();
          break;

        case 'delete':
          query = query
            .delete()
            .eq('id', args.filters?.id)
            .eq('user_id', user?.id);
          break;

        case 'upsert':
          const upsertData = {
            ...args.body,
            user_id: user?.id,
          };
          query = query.upsert([upsertData]).select();
          break;
      }

      // Execute the query
      const { data, error } = args.single ? await query.single() : await query;

      if (error) {
        console.error(`Supabase ${args.operation} error:`, error);
        return {
          error: {
            status: 400,
            data: error,
            error: error.message || 'Database operation failed'
          }
        };
      }

      return { data };
    }

    // Handle external API calls (like Google Maps)
    if (args.url) {
      const response = await fetch(args.url, {
        method: args.method || 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        body: args.body ? JSON.stringify(args.body) : undefined,
      });

      const data = await response.json();

      if (!response.ok) {
        return {
          error: {
            status: response.status,
            data,
            error: data.error_message || 'API request failed'
          }
        };
      }

      return { data };
    }

    return {
      error: {
        status: 400,
        data: { error: 'Invalid query configuration' },
        error: 'Invalid query configuration'
      }
    };

  } catch (error: any) {
    console.error('Base query error:', error);
    return {
      error: {
        status: 500,
        data: { error: error.message },
        error: error.message || 'Unknown error occurred'
      }
    };
  }
};

// Helper functions for offline data management
async function getOfflineData(args: any): Promise<any> {
  if (args.table === 'tasks') {
    const tasks = await offlineStorageService.loadTasks();

    if (args.filters) {
      // Apply filters
      return tasks.filter(task => {
        return Object.entries(args.filters).every(([key, value]) => {
          return task[key as keyof typeof task] === value;
        });
      });
    }

    return args.single ? tasks[0] : tasks;
  }

  return null;
}

async function storeOfflineOptimistic(args: any): Promise<void> {
  if (args.table === 'tasks' && args.body) {
    try {
      const tasks = await offlineStorageService.loadTasks();

      switch (args.operation) {
        case 'insert':
          const newTask = {
            ...args.body,
            id: args.body.id || `temp-${Date.now()}`,
            createdAt: new Date(),
            updatedAt: new Date(),
            _optimistic: true,
          };
          tasks.push(newTask);
          break;

        case 'update':
          const updateIndex = tasks.findIndex(t => t.id === args.filters?.id);
          if (updateIndex !== -1) {
            tasks[updateIndex] = {
              ...tasks[updateIndex],
              ...args.body,
              updatedAt: new Date(),
              _optimistic: true,
            };
          }
          break;

        case 'delete':
          const deleteIndex = tasks.findIndex(t => t.id === args.filters?.id);
          if (deleteIndex !== -1) {
            tasks.splice(deleteIndex, 1);
          }
          break;
      }

      await offlineStorageService.saveTasks(tasks);
    } catch (error) {
      console.error('Failed to store optimistic update:', error);
    }
  }
}

async function backgroundSync(args: any): Promise<void> {
  // Perform background sync without blocking the UI
  setTimeout(async () => {
    try {
      if (args.table === 'tasks') {
        const syncResult = await offlineStorageService.syncWithServer();
        if (syncResult.conflicts.length > 0) {
          console.log(`Background sync detected ${syncResult.conflicts.length} conflicts`);
          // Emit event for UI to handle conflicts
          conflictResolutionService.emit('conflictsDetected', syncResult.conflicts);
        }
      }
    } catch (error) {
      console.warn('Background sync failed:', error);
    }
  }, 100);
}

// Create the main API service
export const api = createApi({
  reducerPath: 'api',
  baseQuery: supabaseBaseQuery,
  tagTypes: [
    'Task',
    'User', 
    'Settings',
    'UserProfile',
    'Location',
    'Place',
    'NetworkStatus'
  ],
  endpoints: () => ({}),
});

// Export hooks and utilities
export const { 
  util: apiUtil,
  reducer: apiReducer,
  middleware: apiMiddleware 
} = api;
