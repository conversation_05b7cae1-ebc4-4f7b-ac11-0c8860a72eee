import { api } from './baseApi';
import { getSupabaseClient } from '../../services/supabase';

// User types
export interface User {
  id: string;
  email?: string;
  created_at: string;
  last_sign_in_at?: string;
  is_anonymous?: boolean;
}

export interface UserProfile {
  id?: string;
  user_id: string;
  firstName: string;
  lastName: string;
  email: string;
  createdAt: string;
  updatedAt?: string;
}

// Define the user API endpoints
export const userApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Get current authenticated user
    getCurrentUser: builder.query<User | null, void>({
      queryFn: async () => {
        try {
          const supabase = await getSupabaseClient();
          const { data: { user }, error } = await supabase.auth.getUser();
          
          if (error) {
            console.error('Error getting current user:', error);
            return { error: { status: 401, data: error, error: error.message } };
          }

          if (!user) {
            return { data: null };
          }

          return {
            data: {
              id: user.id,
              email: user.email,
              created_at: user.created_at,
              last_sign_in_at: user.last_sign_in_at,
              is_anonymous: user.is_anonymous,
            }
          };
        } catch (error: any) {
          console.error('Error in getCurrentUser:', error);
          return { error: { status: 500, data: error, error: error.message } };
        }
      },
      providesTags: [{ type: 'User', id: 'CURRENT' }],
      // Keep user data fresh
      keepUnusedDataFor: 60, // 1 minute
    }),

    // Sign in anonymously
    signInAnonymously: builder.mutation<User, void>({
      queryFn: async () => {
        try {
          const supabase = await getSupabaseClient();
          const { data, error } = await supabase.auth.signInAnonymously();
          
          if (error) {
            console.error('Error signing in anonymously:', error);
            return { error: { status: 400, data: error, error: error.message } };
          }

          if (!data.user) {
            return { error: { status: 400, data: null, error: 'Failed to create anonymous user' } };
          }

          console.log('✅ Signed in anonymously:', data.user.id);
          
          return {
            data: {
              id: data.user.id,
              email: data.user.email,
              created_at: data.user.created_at,
              last_sign_in_at: data.user.last_sign_in_at,
              is_anonymous: data.user.is_anonymous,
            }
          };
        } catch (error: any) {
          console.error('Error in signInAnonymously:', error);
          return { error: { status: 500, data: error, error: error.message } };
        }
      },
      invalidatesTags: [{ type: 'User', id: 'CURRENT' }],
    }),

    // Check if user is authenticated
    checkAuthentication: builder.query<boolean, void>({
      queryFn: async () => {
        try {
          const supabase = await getSupabaseClient();
          const { data: { user } } = await supabase.auth.getUser();
          return { data: !!user };
        } catch (error: any) {
          console.error('Error checking authentication:', error);
          return { data: false };
        }
      },
      providesTags: [{ type: 'User', id: 'AUTH_STATUS' }],
      // Check auth status frequently
      keepUnusedDataFor: 30, // 30 seconds
    }),

    // Get user profile
    getUserProfile: builder.query<UserProfile | null, void>({
      query: () => ({
        table: 'user_profiles',
        operation: 'select',
        columns: '*',
        single: true,
      }),
      providesTags: [{ type: 'UserProfile', id: 'CURRENT' }],
      transformResponse: (response: any): UserProfile | null => {
        if (!response) return null;
        
        return {
          id: response.id,
          user_id: response.user_id,
          firstName: response.first_name,
          lastName: response.last_name,
          email: response.email,
          createdAt: response.created_at,
          updatedAt: response.updated_at,
        };
      },
      // Handle case where profile doesn't exist
      transformErrorResponse: (response: any) => {
        if (response.status === 400 && response.data?.code === 'PGRST116') {
          // No rows returned - profile doesn't exist yet
          return null;
        }
        return response;
      },
    }),

    // Create or update user profile
    upsertUserProfile: builder.mutation<UserProfile, Partial<UserProfile>>({
      query: (profileData) => ({
        table: 'user_profiles',
        operation: 'upsert',
        body: {
          first_name: profileData.firstName,
          last_name: profileData.lastName,
          email: profileData.email,
          updated_at: new Date().toISOString(),
        },
        single: true,
      }),
      invalidatesTags: [{ type: 'UserProfile', id: 'CURRENT' }],
      transformResponse: (response: any): UserProfile => ({
        id: response.id,
        user_id: response.user_id,
        firstName: response.first_name,
        lastName: response.last_name,
        email: response.email,
        createdAt: response.created_at,
        updatedAt: response.updated_at,
      }),
    }),

    // Sign out user
    signOut: builder.mutation<void, void>({
      queryFn: async () => {
        try {
          const { error } = await supabase.auth.signOut();
          
          if (error) {
            console.error('Error signing out:', error);
            return { error: { status: 400, data: error, error: error.message } };
          }

          console.log('✅ User signed out successfully');
          return { data: undefined };
        } catch (error: any) {
          console.error('Error in signOut:', error);
          return { error: { status: 500, data: error, error: error.message } };
        }
      },
      invalidatesTags: [
        { type: 'User', id: 'CURRENT' },
        { type: 'User', id: 'AUTH_STATUS' },
        { type: 'UserProfile', id: 'CURRENT' },
        { type: 'Task', id: 'LIST' }, // Clear tasks when signing out
      ],
    }),

    // Listen to auth state changes
    subscribeToAuthChanges: builder.query<User | null, void>({
      queryFn: () => ({ data: null }), // Initial data
      async onCacheEntryAdded(arg, { updateCachedData, cacheDataLoaded, cacheEntryRemoved }) {
        try {
          await cacheDataLoaded;
          const supabase = await getSupabaseClient();

          const { data: { subscription } } = supabase.auth.onAuthStateChange(
            (event, session) => {
              console.log('🔐 Auth state changed:', event, session?.user?.id);
              
              updateCachedData(() => {
                return session?.user ? {
                  id: session.user.id,
                  email: session.user.email,
                  created_at: session.user.created_at,
                  last_sign_in_at: session.user.last_sign_in_at,
                  is_anonymous: session.user.is_anonymous,
                } : null;
              });
            }
          );

          await cacheEntryRemoved;
          subscription.unsubscribe();
        } catch (error) {
          console.error('Error in auth subscription:', error);
        }
      },
      providesTags: [{ type: 'User', id: 'SUBSCRIPTION' }],
    }),
  }),
});

// Export hooks for use in components
export const {
  useGetCurrentUserQuery,
  useSignInAnonymouslyMutation,
  useCheckAuthenticationQuery,
  useGetUserProfileQuery,
  useUpsertUserProfileMutation,
  useSignOutMutation,
  useSubscribeToAuthChangesQuery,
} = userApi;
