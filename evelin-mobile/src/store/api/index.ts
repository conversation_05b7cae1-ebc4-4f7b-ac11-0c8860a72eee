// Export the main API service
export { api, apiReducer, apiMiddleware, apiUtil } from './baseApi';

// Export all API endpoints and hooks using wildcard exports
export * from './tasksApi';
export * from './userApi';
export * from './settingsApi';
export * from './locationApi';

// Error boundary helper for RTK Query errors
export const isRTKQueryError = (error: any): error is {
  status: number;
  data: any;
  error: string;
} => {
  return error && typeof error.status === 'number';
};

// Helper to extract error message from RTK Query error
export const getErrorMessage = (error: any): string => {
  if (isRTKQueryError(error)) {
    return error.error || error.data?.message || `Error ${error.status}`;
  }
  
  if (error?.message) {
    return error.message;
  }
  
  return 'An unknown error occurred';
};

// Helper to check if query is loading
export const isQueryLoading = (queryState: any): boolean => {
  return queryState?.isLoading || queryState?.isFetching;
};

// Helper to check if query has data
export const hasQueryData = (queryState: any): boolean => {
  return queryState?.data !== undefined && !queryState?.isError;
};

// Helper to get fresh data or cached data
export const getQueryData = (queryState: any, preferFresh: boolean = false): any => {
  if (preferFresh && queryState?.isFetching) {
    return undefined; // Wait for fresh data
  }
  return queryState?.data;
};

// Retry configuration helper
export const createRetryConfig = (maxRetries: number = 3, baseDelay: number = 1000) => ({
  maxRetries,
  retryCondition: (error: any) => {
    // Retry on network errors and 5xx server errors
    return !error.status || (error.status >= 500 && error.status < 600);
  },
  retryDelay: (attempt: number) => Math.min(baseDelay * Math.pow(2, attempt), 30000),
});

// Cache time helpers
export const CACHE_TIMES = {
  SHORT: 60, // 1 minute
  MEDIUM: 300, // 5 minutes
  LONG: 1800, // 30 minutes
  VERY_LONG: 3600, // 1 hour
} as const;

// Common tag types for cache invalidation
export const TAG_TYPES = {
  TASK: 'Task',
  USER: 'User',
  SETTINGS: 'Settings',
  USER_PROFILE: 'UserProfile',
  LOCATION: 'Location',
  PLACE: 'Place',
  NETWORK_STATUS: 'NetworkStatus',
} as const;
