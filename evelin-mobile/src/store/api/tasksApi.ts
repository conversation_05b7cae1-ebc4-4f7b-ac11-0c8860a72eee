import { api } from './baseApi';
import { Task } from '../../types/task';
import { ConflictResolutionStrategy } from '../../services/conflictResolutionService';

// Define the tasks API endpoints
export const tasksApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Get all tasks for the current user
    getTasks: builder.query<Task[], { offlineFirst?: boolean }>({
      query: (args = {}) => ({
        table: 'tasks',
        operation: 'select',
        columns: '*',
        offlineFirst: args.offlineFirst,
      }),
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ id }) => ({ type: 'Task' as const, id })),
              { type: 'Task', id: 'LIST' },
            ]
          : [{ type: 'Task', id: 'LIST' }],
      // Transform the response to match our Task interface
      transformResponse: (response: any[]): Task[] => {
        return response.map(task => ({
          id: task.id,
          text: task.text,
          location: task.location,
          category: task.category,
          completed: task.completed,
          createdAt: new Date(task.created_at),
          notificationTriggered: task.notification_triggered || false,
          notificationDistance: task.notification_distance || 100,
          coordinates: task.coordinates ? {
            lat: task.coordinates.lat,
            lng: task.coordinates.lng,
          } : undefined,
          _optimistic: task._optimistic,
          _operationId: task._operationId,
        }));
      },
    }),

    // Create a new task with offline-first support
    createTask: builder.mutation<Task, Omit<Task, 'id' | 'createdAt'> & {
      conflictResolution?: ConflictResolutionStrategy;
      atomicGroup?: string;
    }>({
      query: (newTask) => ({
        table: 'tasks',
        operation: 'insert',
        body: {
          text: newTask.text,
          location: newTask.location,
          category: newTask.category,
          completed: newTask.completed,
          notification_triggered: newTask.notificationTriggered,
          notification_distance: newTask.notificationDistance,
          coordinates: newTask.coordinates,
        },
        single: true,
        conflictResolution: newTask.conflictResolution || 'timestamp-based',
        atomicGroup: newTask.atomicGroup,
        rollbackData: {
          operation: 'delete',
          entityType: 'task',
        },
      }),
      invalidatesTags: [{ type: 'Task', id: 'LIST' }],
      // Optimistic update
      async onQueryStarted(newTask, { dispatch, queryFulfilled }) {
        const optimisticTask: Task = {
          ...newTask,
          id: `temp-${Date.now()}`,
          createdAt: new Date(),
        };

        const patchResult = dispatch(
          tasksApi.util.updateQueryData('getTasks', {}, (draft) => {
            draft.unshift(optimisticTask);
          })
        );

        try {
          const { data: createdTask } = await queryFulfilled;
          // Update with real task data
          dispatch(
            tasksApi.util.updateQueryData('getTasks', {}, (draft) => {
              const index = draft.findIndex(task => task.id === optimisticTask.id);
              if (index !== -1) {
                draft[index] = {
                  id: createdTask.id,
                  text: createdTask.text,
                  location: createdTask.location,
                  category: createdTask.category,
                  completed: createdTask.completed,
                  createdAt: new Date((createdTask as any).created_at),
                  notificationTriggered: (createdTask as any).notification_triggered || false,
                  notificationDistance: (createdTask as any).notification_distance || 100,
                  coordinates: createdTask.coordinates,
                };
              }
            })
          );
        } catch {
          // Revert optimistic update on error
          patchResult.undo();
        }
      },
      transformResponse: (response: any): Task => ({
        id: response.id,
        text: response.text,
        location: response.location,
        category: response.category,
        completed: response.completed,
        createdAt: new Date(response.created_at),
        notificationTriggered: response.notification_triggered || false,
        notificationDistance: response.notification_distance || 100,
        coordinates: response.coordinates,
      }),
    }),

    // Update a task
    updateTask: builder.mutation<Task, { id: string; updates: Partial<Task> }>({
      query: ({ id, updates }) => ({
        table: 'tasks',
        operation: 'update',
        body: {
          ...(updates.text && { text: updates.text }),
          ...(updates.location && { location: updates.location }),
          ...(updates.category && { category: updates.category }),
          ...(updates.completed !== undefined && { completed: updates.completed }),
          ...(updates.notificationTriggered !== undefined && { 
            notification_triggered: updates.notificationTriggered 
          }),
          ...(updates.notificationDistance && { 
            notification_distance: updates.notificationDistance 
          }),
          ...(updates.coordinates && { coordinates: updates.coordinates }),
        },
        filters: { id },
        single: true,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Task', id },
        { type: 'Task', id: 'LIST' },
      ],
      // Optimistic update
      async onQueryStarted({ id, updates }, { dispatch, queryFulfilled }) {
        const patchResult = dispatch(
          tasksApi.util.updateQueryData('getTasks', {}, (draft) => {
            const task = draft.find(task => task.id === id);
            if (task) {
              Object.assign(task, updates);
            }
          })
        );

        try {
          await queryFulfilled;
        } catch {
          // Revert optimistic update on error
          patchResult.undo();
        }
      },
      transformResponse: (response: any): Task => ({
        id: response.id,
        text: response.text,
        location: response.location,
        category: response.category,
        completed: response.completed,
        createdAt: new Date(response.created_at),
        notificationTriggered: response.notification_triggered || false,
        notificationDistance: response.notification_distance || 100,
        coordinates: response.coordinates,
      }),
    }),

    // Delete a task
    deleteTask: builder.mutation<void, string>({
      query: (id) => ({
        table: 'tasks',
        operation: 'delete',
        filters: { id },
      }),
      invalidatesTags: (result, error, id) => [
        { type: 'Task', id },
        { type: 'Task', id: 'LIST' },
      ],
      // Optimistic update
      async onQueryStarted(id, { dispatch, queryFulfilled }) {
        const patchResult = dispatch(
          tasksApi.util.updateQueryData('getTasks', {}, (draft) => {
            const index = draft.findIndex(task => task.id === id);
            if (index !== -1) {
              draft.splice(index, 1);
            }
          })
        );

        try {
          await queryFulfilled;
        } catch {
          // Revert optimistic update on error
          patchResult.undo();
        }
      },
    }),

    // Bulk update tasks
    bulkUpdateTasks: builder.mutation<Task[], Array<{ id: string; updates: Partial<Task> }>>({
      queryFn: async (updates, api, extraOptions, baseQuery) => {
        const results: Task[] = [];
        
        for (const update of updates) {
          const result = await baseQuery({
            table: 'tasks',
            operation: 'update',
            body: {
              ...(update.updates.text && { text: update.updates.text }),
              ...(update.updates.location && { location: update.updates.location }),
              ...(update.updates.category && { category: update.updates.category }),
              ...(update.updates.completed !== undefined && { completed: update.updates.completed }),
              ...(update.updates.notificationTriggered !== undefined && { 
                notification_triggered: update.updates.notificationTriggered 
              }),
              ...(update.updates.notificationDistance && { 
                notification_distance: update.updates.notificationDistance 
              }),
              ...(update.updates.coordinates && { coordinates: update.updates.coordinates }),
            },
            filters: { id: update.id },
            single: true,
          });

          if (result.error) {
            return { error: result.error };
          }

          if (result.data) {
            const data = result.data as any;
            results.push({
              id: data.id,
              text: data.text,
              location: data.location,
              category: data.category,
              completed: data.completed,
              createdAt: new Date(data.created_at),
              notificationTriggered: data.notification_triggered || false,
              notificationDistance: data.notification_distance || 100,
              coordinates: data.coordinates,
            });
          }
        }

        return { data: results };
      },
      invalidatesTags: [{ type: 'Task', id: 'LIST' }],
    }),
  }),
});

// Export hooks for use in components
export const {
  useGetTasksQuery,
  useCreateTaskMutation,
  useUpdateTaskMutation,
  useDeleteTaskMutation,
  useBulkUpdateTasksMutation,
} = tasksApi;
