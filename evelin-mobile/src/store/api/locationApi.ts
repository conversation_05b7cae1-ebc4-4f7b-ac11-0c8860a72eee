import { api } from './baseApi';

// Location types
export interface PlaceSearchResult {
  place_id: string;
  name: string;
  address: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  distance?: number;
  rating?: number;
  types: string[];
  business_status?: string;
  opening_hours?: {
    open_now: boolean;
  };
}

export interface ReverseGeocodeResult {
  formatted_address: string;
  address_components: Array<{
    long_name: string;
    short_name: string;
    types: string[];
  }>;
  place_id: string;
  types: string[];
}

// Google Maps API configuration
const GOOGLE_MAPS_API_KEY = 'AIzaSyBjsINSaKaFYKnIlFhKUJyWzJhHJhHJhHJ'; // Replace with actual key
const GOOGLE_MAPS_BASE_URL = 'https://maps.googleapis.com/maps/api';

// Define the location API endpoints
export const locationApi = api.injectEndpoints({
  endpoints: (builder) => ({
    // Search for nearby places using Google Places API
    searchNearbyPlaces: builder.query<
      PlaceSearchResult[],
      {
        query: string;
        userLocation: { latitude: number; longitude: number };
        radius?: number;
        type?: string;
      }
    >({
      query: ({ query, userLocation, radius = 500, type = 'establishment' }) => ({
        url: `${GOOGLE_MAPS_BASE_URL}/place/nearbysearch/json?` +
          `location=${userLocation.latitude},${userLocation.longitude}&` +
          `radius=${radius}&` +
          `keyword=${encodeURIComponent(query)}&` +
          `type=${type}&` +
          `key=${GOOGLE_MAPS_API_KEY}`,
        method: 'GET',
      }),
      providesTags: (result, error, { query, userLocation, radius }) => [
        { 
          type: 'Place', 
          id: `nearby-${query}-${userLocation.latitude}-${userLocation.longitude}-${radius}` 
        },
      ],
      transformResponse: (response: any): PlaceSearchResult[] => {
        if (response.status !== 'OK') {
          console.error('Google Places API error:', response.status);
          return [];
        }

        return response.results.map((place: any) => ({
          place_id: place.place_id,
          name: place.name,
          address: place.vicinity || place.formatted_address || '',
          coordinates: {
            lat: place.geometry.location.lat,
            lng: place.geometry.location.lng,
          },
          rating: place.rating,
          types: place.types || [],
          business_status: place.business_status,
          opening_hours: place.opening_hours,
        }));
      },
      // Cache for 5 minutes
      keepUnusedDataFor: 300,
    }),

    // Find a specific place using text search
    findSpecificPlace: builder.query<
      PlaceSearchResult | null,
      {
        placeName: string;
        userLocation: { latitude: number; longitude: number };
        radius?: number;
      }
    >({
      query: ({ placeName, userLocation, radius = 1000 }) => ({
        url: `${GOOGLE_MAPS_BASE_URL}/place/textsearch/json?` +
          `query=${encodeURIComponent(placeName)}&` +
          `location=${userLocation.latitude},${userLocation.longitude}&` +
          `radius=${radius}&` +
          `key=${GOOGLE_MAPS_API_KEY}`,
        method: 'GET',
      }),
      providesTags: (result, error, { placeName, userLocation, radius }) => [
        { 
          type: 'Place', 
          id: `specific-${placeName}-${userLocation.latitude}-${userLocation.longitude}-${radius}` 
        },
      ],
      transformResponse: (response: any): PlaceSearchResult | null => {
        if (response.status !== 'OK' || !response.results.length) {
          console.log(`No results found for: ${response.status}`);
          return null;
        }

        const place = response.results[0]; // Get the first (most relevant) result
        return {
          place_id: place.place_id,
          name: place.name,
          address: place.formatted_address || '',
          coordinates: {
            lat: place.geometry.location.lat,
            lng: place.geometry.location.lng,
          },
          rating: place.rating,
          types: place.types || [],
          business_status: place.business_status,
          opening_hours: place.opening_hours,
        };
      },
      // Cache for 10 minutes since specific places don't change often
      keepUnusedDataFor: 600,
    }),

    // Reverse geocode coordinates to get address
    reverseGeocode: builder.query<
      string,
      { latitude: number; longitude: number }
    >({
      query: ({ latitude, longitude }) => ({
        url: `${GOOGLE_MAPS_BASE_URL}/geocode/json?` +
          `latlng=${latitude},${longitude}&` +
          `key=${GOOGLE_MAPS_API_KEY}`,
        method: 'GET',
      }),
      providesTags: (result, error, { latitude, longitude }) => [
        { type: 'Location', id: `geocode-${latitude}-${longitude}` },
      ],
      transformResponse: (response: any): string => {
        if (response.status !== 'OK' || !response.results.length) {
          console.log(`Reverse geocoding failed: ${response.status}`);
          return `${response.latitude}, ${response.longitude}`;
        }

        const result = response.results[0];
        const components = result.address_components;

        // Try to create a user-friendly location name
        const neighborhood = components.find((c: any) => 
          c.types.includes('neighborhood') || c.types.includes('sublocality')
        )?.long_name;
        
        const locality = components.find((c: any) => 
          c.types.includes('locality') || c.types.includes('administrative_area_level_2')
        )?.long_name;
        
        const adminArea = components.find((c: any) => 
          c.types.includes('administrative_area_level_1')
        )?.long_name;
        
        const country = components.find((c: any) => 
          c.types.includes('country')
        )?.long_name;

        // Build location name with available components
        let locationName = '';
        if (neighborhood && locality) {
          locationName = `${neighborhood}, ${locality}`;
        } else if (locality) {
          locationName = locality;
        } else if (adminArea) {
          locationName = adminArea;
        } else if (country) {
          locationName = country;
        } else {
          locationName = result.formatted_address;
        }

        return locationName;
      },
      // Cache reverse geocoding for 30 minutes
      keepUnusedDataFor: 1800,
    }),

    // Get detailed place information
    getPlaceDetails: builder.query<
      PlaceSearchResult & { 
        phone?: string; 
        website?: string; 
        reviews?: any[]; 
        photos?: string[] 
      },
      string // place_id
    >({
      query: (placeId) => ({
        url: `${GOOGLE_MAPS_BASE_URL}/place/details/json?` +
          `place_id=${placeId}&` +
          `fields=name,formatted_address,geometry,rating,formatted_phone_number,website,reviews,photos&` +
          `key=${GOOGLE_MAPS_API_KEY}`,
        method: 'GET',
      }),
      providesTags: (result, error, placeId) => [
        { type: 'Place', id: `details-${placeId}` },
      ],
      transformResponse: (response: any) => {
        if (response.status !== 'OK' || !response.result) {
          throw new Error(`Place details failed: ${response.status}`);
        }

        const place = response.result;
        return {
          place_id: place.place_id,
          name: place.name,
          address: place.formatted_address || '',
          coordinates: {
            lat: place.geometry.location.lat,
            lng: place.geometry.location.lng,
          },
          rating: place.rating,
          types: place.types || [],
          phone: place.formatted_phone_number,
          website: place.website,
          reviews: place.reviews || [],
          photos: place.photos?.map((photo: any) => 
            `${GOOGLE_MAPS_BASE_URL}/place/photo?maxwidth=400&photoreference=${photo.photo_reference}&key=${GOOGLE_MAPS_API_KEY}`
          ) || [],
        };
      },
      // Cache place details for 1 hour
      keepUnusedDataFor: 3600,
    }),

    // Get autocomplete suggestions
    getPlaceAutocomplete: builder.query<
      Array<{ place_id: string; description: string; types: string[] }>,
      {
        input: string;
        userLocation?: { latitude: number; longitude: number };
        radius?: number;
      }
    >({
      query: ({ input, userLocation, radius = 50000 }) => {
        let url = `${GOOGLE_MAPS_BASE_URL}/place/autocomplete/json?` +
          `input=${encodeURIComponent(input)}&` +
          `key=${GOOGLE_MAPS_API_KEY}`;

        if (userLocation) {
          url += `&location=${userLocation.latitude},${userLocation.longitude}&radius=${radius}`;
        }

        return { url, method: 'GET' };
      },
      providesTags: (result, error, { input }) => [
        { type: 'Place', id: `autocomplete-${input}` },
      ],
      transformResponse: (response: any) => {
        if (response.status !== 'OK') {
          return [];
        }

        return response.predictions.map((prediction: any) => ({
          place_id: prediction.place_id,
          description: prediction.description,
          types: prediction.types || [],
        }));
      },
      // Cache autocomplete for 2 minutes
      keepUnusedDataFor: 120,
    }),
  }),
});

// Export hooks for use in components
export const {
  useSearchNearbyPlacesQuery,
  useFindSpecificPlaceQuery,
  useReverseGeocodeQuery,
  useGetPlaceDetailsQuery,
  useGetPlaceAutocompleteQuery,
  useLazySearchNearbyPlacesQuery,
  useLazyFindSpecificPlaceQuery,
  useLazyReverseGeocodeQuery,
} = locationApi;
