import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

// Settings interfaces
export interface AppSettings {
  // Appearance
  theme: 'light' | 'dark' | 'system';
  colorScheme: 'blue' | 'green' | 'purple' | 'orange';
  fontSize: 'small' | 'medium' | 'large';
  
  // Notifications
  notifications: {
    enabled: boolean;
    sound: boolean;
    vibration: boolean;
    badge: boolean;
    quietHours: {
      enabled: boolean;
      start: string;
      end: string;
    };
    categories: {
      taskReminders: boolean;
      locationAlerts: boolean;
      systemUpdates: boolean;
    };
  };
  
  // Location
  location: {
    enabled: boolean;
    backgroundTracking: boolean;
    highAccuracy: boolean;
    defaultRadius: number;
    autoDetectPlaces: boolean;
  };
  
  // Voice & Audio
  voice: {
    enabled: boolean;
    language: string;
    wakeWord: boolean;
    continuousListening: boolean;
    voiceFeedback: boolean;
  };
  
  // Privacy & Security
  privacy: {
    analytics: boolean;
    crashReporting: boolean;
    locationHistory: boolean;
    biometricAuth: boolean;
    autoLock: boolean;
    autoLockTimeout: number; // minutes
    // Enhanced privacy controls
    dataRetentionEnabled: boolean;
    encryptLocationData: boolean;
    anonymizeData: boolean;
    allowDataExport: boolean;
    requireBiometricForLocationAccess: boolean;
  };
  
  // Performance
  performance: {
    animations: boolean;
    hapticFeedback: boolean;
    backgroundSync: boolean;
    offlineMode: boolean;
    dataCompression: boolean;
  };
  
  // Advanced
  advanced: {
    developerMode: boolean;
    debugLogging: boolean;
    experimentalFeatures: boolean;
    betaUpdates: boolean;
  };
}

// Async thunks
export const loadSettingsAsync = createAsyncThunk(
  'settings/loadSettingsAsync',
  async () => {
    // Simulate loading from storage/API
    await new Promise(resolve => setTimeout(resolve, 200));
    
    // Return default settings or loaded settings
    return null; // Will use initial state
  }
);

export const saveSettingsAsync = createAsyncThunk(
  'settings/saveSettingsAsync',
  async (settings: Partial<AppSettings>) => {
    // Simulate saving to storage/API
    await new Promise(resolve => setTimeout(resolve, 100));
    return settings;
  }
);

export const resetSettingsAsync = createAsyncThunk(
  'settings/resetSettingsAsync',
  async () => {
    // Simulate resetting settings
    await new Promise(resolve => setTimeout(resolve, 150));
    return null; // Will reset to initial state
  }
);

// Initial state
const initialSettings: AppSettings = {
  theme: 'system',
  colorScheme: 'blue',
  fontSize: 'medium',
  
  notifications: {
    enabled: true,
    sound: true,
    vibration: true,
    badge: true,
    quietHours: {
      enabled: true,
      start: '22:00',
      end: '07:00',
    },
    categories: {
      taskReminders: true,
      locationAlerts: true,
      systemUpdates: false,
    },
  },
  
  location: {
    enabled: true,
    backgroundTracking: false,
    highAccuracy: true,
    defaultRadius: 200,
    autoDetectPlaces: true,
  },
  
  voice: {
    enabled: true,
    language: 'en-US',
    wakeWord: false,
    continuousListening: false,
    voiceFeedback: true,
  },
  
  privacy: {
    analytics: true,
    crashReporting: true,
    locationHistory: true,
    biometricAuth: false,
    autoLock: false,
    autoLockTimeout: 5,
    // Enhanced privacy controls (privacy-first defaults)
    dataRetentionEnabled: true,
    encryptLocationData: true,
    anonymizeData: true,
    allowDataExport: true,
    requireBiometricForLocationAccess: false,
  },
  
  performance: {
    animations: true,
    hapticFeedback: true,
    backgroundSync: true,
    offlineMode: true,
    dataCompression: false,
  },
  
  advanced: {
    developerMode: false,
    debugLogging: false,
    experimentalFeatures: false,
    betaUpdates: false,
  },
};

interface SettingsState {
  settings: AppSettings;
  loading: boolean;
  error: string | null;
  hasUnsavedChanges: boolean;
  lastSyncAt: string | null;
}

const initialState: SettingsState = {
  settings: initialSettings,
  loading: false,
  error: null,
  hasUnsavedChanges: false,
  lastSyncAt: null,
};

// Settings slice
const settingsSlice = createSlice({
  name: 'settings',
  initialState,
  reducers: {
    // General settings actions
    updateSettings: (state, action: PayloadAction<Partial<AppSettings>>) => {
      state.settings = { ...state.settings, ...action.payload };
      state.hasUnsavedChanges = true;
    },
    
    // Specific setting category updates
    updateNotificationSettings: (state, action: PayloadAction<Partial<AppSettings['notifications']>>) => {
      state.settings.notifications = { ...state.settings.notifications, ...action.payload };
      state.hasUnsavedChanges = true;
    },
    
    updateLocationSettings: (state, action: PayloadAction<Partial<AppSettings['location']>>) => {
      state.settings.location = { ...state.settings.location, ...action.payload };
      state.hasUnsavedChanges = true;
    },
    
    updateVoiceSettings: (state, action: PayloadAction<Partial<AppSettings['voice']>>) => {
      state.settings.voice = { ...state.settings.voice, ...action.payload };
      state.hasUnsavedChanges = true;
    },
    
    updatePrivacySettings: (state, action: PayloadAction<Partial<AppSettings['privacy']>>) => {
      state.settings.privacy = { ...state.settings.privacy, ...action.payload };
      state.hasUnsavedChanges = true;
    },
    
    updatePerformanceSettings: (state, action: PayloadAction<Partial<AppSettings['performance']>>) => {
      state.settings.performance = { ...state.settings.performance, ...action.payload };
      state.hasUnsavedChanges = true;
    },
    
    updateAdvancedSettings: (state, action: PayloadAction<Partial<AppSettings['advanced']>>) => {
      state.settings.advanced = { ...state.settings.advanced, ...action.payload };
      state.hasUnsavedChanges = true;
    },
    
    // Quick toggle actions
    toggleTheme: (state) => {
      const themes: AppSettings['theme'][] = ['light', 'dark', 'system'];
      const currentIndex = themes.indexOf(state.settings.theme);
      const nextIndex = (currentIndex + 1) % themes.length;
      state.settings.theme = themes[nextIndex];
      state.hasUnsavedChanges = true;
    },
    
    toggleNotifications: (state) => {
      state.settings.notifications.enabled = !state.settings.notifications.enabled;
      state.hasUnsavedChanges = true;
    },
    
    toggleLocationTracking: (state) => {
      state.settings.location.enabled = !state.settings.location.enabled;
      state.hasUnsavedChanges = true;
    },
    
    toggleVoiceInput: (state) => {
      state.settings.voice.enabled = !state.settings.voice.enabled;
      state.hasUnsavedChanges = true;
    },
    
    toggleDeveloperMode: (state) => {
      state.settings.advanced.developerMode = !state.settings.advanced.developerMode;
      state.hasUnsavedChanges = true;
    },
    
    // Utility actions
    markChangesSaved: (state) => {
      state.hasUnsavedChanges = false;
      state.lastSyncAt = new Date().toISOString();
    },
    
    clearError: (state) => {
      state.error = null;
    },
    
    resetToDefaults: (state) => {
      state.settings = initialSettings;
      state.hasUnsavedChanges = true;
    },
  },
  extraReducers: (builder) => {
    // Load settings async
    builder
      .addCase(loadSettingsAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(loadSettingsAsync.fulfilled, (state, action) => {
        state.loading = false;
        if (action.payload && typeof action.payload === 'object' && action.payload !== null && !Array.isArray(action.payload)) {
          state.settings = { ...state.settings, ...(action.payload as Partial<AppSettings>) };
        }
        state.lastSyncAt = new Date().toISOString();
      })
      .addCase(loadSettingsAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to load settings';
      });

    // Save settings async
    builder
      .addCase(saveSettingsAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(saveSettingsAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.hasUnsavedChanges = false;
        state.lastSyncAt = new Date().toISOString();
      })
      .addCase(saveSettingsAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to save settings';
      });

    // Reset settings async
    builder
      .addCase(resetSettingsAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(resetSettingsAsync.fulfilled, (state) => {
        state.loading = false;
        state.settings = initialSettings;
        state.hasUnsavedChanges = false;
        state.lastSyncAt = new Date().toISOString();
      })
      .addCase(resetSettingsAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to reset settings';
      });
  },
});

// Export actions
export const {
  updateSettings,
  updateNotificationSettings,
  updateLocationSettings,
  updateVoiceSettings,
  updatePrivacySettings,
  updatePerformanceSettings,
  updateAdvancedSettings,
  toggleTheme,
  toggleNotifications,
  toggleLocationTracking,
  toggleVoiceInput,
  toggleDeveloperMode,
  markChangesSaved,
  clearError,
  resetToDefaults,
} = settingsSlice.actions;

// Export reducer
export default settingsSlice.reducer;
