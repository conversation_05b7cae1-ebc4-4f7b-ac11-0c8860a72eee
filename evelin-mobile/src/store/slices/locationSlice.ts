import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

// Location interfaces
export interface LocationCoordinates {
  latitude: number;
  longitude: number;
  accuracy: number;
  altitude?: number;
  heading?: number;
  speed?: number;
  timestamp: number;
}

export interface LocationPlace {
  id: string;
  name: string;
  address: string;
  coordinates: LocationCoordinates;
  type: 'home' | 'work' | 'favorite' | 'recent';
  radius: number;
  visits: number;
  lastVisit: string;
}

// Async thunks
export const getCurrentLocationAsync = createAsyncThunk(
  'location/getCurrentLocationAsync',
  async (options?: { highAccuracy?: boolean; timeout?: number }) => {
    // Simulate getting current location
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock location data
    return {
      latitude: 37.7749 + (Math.random() - 0.5) * 0.01,
      longitude: -122.4194 + (Math.random() - 0.5) * 0.01,
      accuracy: Math.random() * 10 + 5,
      timestamp: Date.now(),
    };
  }
);

export const searchPlacesAsync = createAsyncThunk(
  'location/searchPlacesAsync',
  async (query: string) => {
    // Simulate place search
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Mock search results
    return [
      {
        id: '1',
        name: `${query} Coffee Shop`,
        address: '123 Main St, San Francisco, CA',
        coordinates: {
          latitude: 37.7749,
          longitude: -122.4194,
          accuracy: 5,
          timestamp: Date.now(),
        },
        type: 'recent' as const,
        radius: 100,
        visits: 0,
        lastVisit: new Date().toISOString(),
      },
    ];
  }
);

// Initial state
interface LocationState {
  currentLocation: LocationCoordinates | null;
  isTracking: boolean;
  places: LocationPlace[];
  searchResults: LocationPlace[];
  loading: boolean;
  error: string | null;
  permissions: {
    granted: boolean;
    backgroundGranted: boolean;
    requestedAt: string | null;
  };
  history: LocationCoordinates[];
  geofences: Array<{
    id: string;
    coordinates: LocationCoordinates;
    radius: number;
    active: boolean;
  }>;
}

const initialState: LocationState = {
  currentLocation: null,
  isTracking: false,
  places: [],
  searchResults: [],
  loading: false,
  error: null,
  permissions: {
    granted: false,
    backgroundGranted: false,
    requestedAt: null,
  },
  history: [],
  geofences: [],
};

// Location slice
const locationSlice = createSlice({
  name: 'location',
  initialState,
  reducers: {
    // Location tracking
    startTracking: (state) => {
      state.isTracking = true;
      state.error = null;
    },
    stopTracking: (state) => {
      state.isTracking = false;
    },
    updateCurrentLocation: (state, action: PayloadAction<LocationCoordinates>) => {
      state.currentLocation = action.payload;
      
      // Add to history (keep last 100 locations)
      state.history.unshift(action.payload);
      if (state.history.length > 100) {
        state.history = state.history.slice(0, 100);
      }
    },
    
    // Permissions
    setPermissions: (state, action: PayloadAction<Partial<LocationState['permissions']>>) => {
      state.permissions = { ...state.permissions, ...action.payload };
    },
    requestPermissions: (state) => {
      state.permissions.requestedAt = new Date().toISOString();
    },
    
    // Places management
    addPlace: (state, action: PayloadAction<Omit<LocationPlace, 'id' | 'visits' | 'lastVisit'>>) => {
      const newPlace: LocationPlace = {
        ...action.payload,
        id: Date.now().toString(),
        visits: 0,
        lastVisit: new Date().toISOString(),
      };
      state.places.push(newPlace);
    },
    updatePlace: (state, action: PayloadAction<{ id: string; updates: Partial<LocationPlace> }>) => {
      const { id, updates } = action.payload;
      const placeIndex = state.places.findIndex(place => place.id === id);
      if (placeIndex > -1) {
        state.places[placeIndex] = { ...state.places[placeIndex], ...updates };
      }
    },
    removePlace: (state, action: PayloadAction<string>) => {
      state.places = state.places.filter(place => place.id !== action.payload);
    },
    visitPlace: (state, action: PayloadAction<string>) => {
      const placeIndex = state.places.findIndex(place => place.id === action.payload);
      if (placeIndex > -1) {
        state.places[placeIndex].visits += 1;
        state.places[placeIndex].lastVisit = new Date().toISOString();
      }
    },
    
    // Geofences
    addGeofence: (state, action: PayloadAction<{
      coordinates: LocationCoordinates;
      radius: number;
    }>) => {
      const geofence = {
        id: Date.now().toString(),
        ...action.payload,
        active: true,
      };
      state.geofences.push(geofence);
    },
    removeGeofence: (state, action: PayloadAction<string>) => {
      state.geofences = state.geofences.filter(fence => fence.id !== action.payload);
    },
    toggleGeofence: (state, action: PayloadAction<string>) => {
      const fenceIndex = state.geofences.findIndex(fence => fence.id === action.payload);
      if (fenceIndex > -1) {
        state.geofences[fenceIndex].active = !state.geofences[fenceIndex].active;
      }
    },
    
    // Search
    clearSearchResults: (state) => {
      state.searchResults = [];
    },
    
    // Utility
    clearError: (state) => {
      state.error = null;
    },
    clearHistory: (state) => {
      state.history = [];
    },
    // Store cleanup actions
    cleanupLocationData: (state, action: PayloadAction<LocationState>) => {
      const oldHistoryCount = state.history.length;
      const oldPlacesCount = state.places.length;
      const oldGeofencesCount = state.geofences.length;

      // Update with cleaned data
      state.history = action.payload.history;
      state.places = action.payload.places;
      state.geofences = action.payload.geofences;

      const historyRemoved = oldHistoryCount - state.history.length;
      const placesRemoved = oldPlacesCount - state.places.length;
      const geofencesRemoved = oldGeofencesCount - state.geofences.length;

      if (historyRemoved > 0 || placesRemoved > 0 || geofencesRemoved > 0) {
        console.log(`🧹 Location cleanup: Removed ${historyRemoved} history, ${placesRemoved} places, ${geofencesRemoved} geofences`);
      }
    },
  },
  extraReducers: (builder) => {
    // Get current location async
    builder
      .addCase(getCurrentLocationAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getCurrentLocationAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.currentLocation = action.payload;
        
        // Add to history
        state.history.unshift(action.payload);
        if (state.history.length > 100) {
          state.history = state.history.slice(0, 100);
        }
      })
      .addCase(getCurrentLocationAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to get current location';
      });

    // Search places async
    builder
      .addCase(searchPlacesAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(searchPlacesAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.searchResults = action.payload;
      })
      .addCase(searchPlacesAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to search places';
      });
  },
});

// Export actions
export const {
  startTracking,
  stopTracking,
  updateCurrentLocation,
  setPermissions,
  requestPermissions,
  addPlace,
  updatePlace,
  removePlace,
  visitPlace,
  addGeofence,
  removeGeofence,
  toggleGeofence,
  clearSearchResults,
  clearError,
  clearHistory,
  cleanupLocationData,
} = locationSlice.actions;

// Export reducer
export default locationSlice.reducer;
