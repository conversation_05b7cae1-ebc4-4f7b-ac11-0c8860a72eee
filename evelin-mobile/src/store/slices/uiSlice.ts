import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// UI state interfaces
export interface ModalState {
  isVisible: boolean;
  type: 'task-edit' | 'task-create' | 'settings' | 'profile' | 'confirmation' | null;
  data?: any;
}

export interface ToastState {
  id: string;
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  duration: number;
  visible: boolean;
}

export interface LoadingState {
  global: boolean;
  tasks: boolean;
  location: boolean;
  sync: boolean;
  [key: string]: boolean;
}

// Initial state
interface UIState {
  // Navigation
  currentScreen: 'main' | 'settings' | 'history' | 'account' | 'version' | 'about';
  previousScreen: string | null;
  navigationHistory: string[];
  
  // Modals and overlays
  modal: ModalState;
  showHamburgerMenu: boolean;
  showVoiceInput: boolean;
  showLocationSettings: boolean;
  showUserProfile: boolean;
  showWelcome: boolean;

  
  // Loading states
  loading: LoadingState;
  
  // Toast notifications
  toasts: ToastState[];
  
  // Form states
  forms: {
    taskCreate: {
      isVisible: boolean;
      data: any;
    };
    taskEdit: {
      isVisible: boolean;
      taskId: string | null;
      data: any;
    };
  };
  
  // Layout and appearance
  layout: {
    headerHeight: number;
    tabBarHeight: number;
    safeAreaInsets: {
      top: number;
      bottom: number;
      left: number;
      right: number;
    };
  };
  
  // Interaction states
  interactions: {
    isScrolling: boolean;
    keyboardVisible: boolean;
    keyboardHeight: number;
    activeInput: string | null;
  };
  
  // Feature flags
  features: {
    voiceInput: boolean;
    locationTracking: boolean;
    backgroundSync: boolean;

    experimentalFeatures: boolean;
  };
}

const initialState: UIState = {
  currentScreen: 'main',
  previousScreen: null,
  navigationHistory: ['main'],
  
  modal: {
    isVisible: false,
    type: null,
    data: null,
  },
  showHamburgerMenu: false,
  showVoiceInput: false,
  showLocationSettings: false,
  showUserProfile: false,
  showWelcome: false,

  
  loading: {
    global: false,
    tasks: false,
    location: false,
    sync: false,
  },
  
  toasts: [],
  
  forms: {
    taskCreate: {
      isVisible: false,
      data: {},
    },
    taskEdit: {
      isVisible: false,
      taskId: null,
      data: {},
    },
  },
  
  layout: {
    headerHeight: 60,
    tabBarHeight: 80,
    safeAreaInsets: {
      top: 0,
      bottom: 0,
      left: 0,
      right: 0,
    },
  },
  
  interactions: {
    isScrolling: false,
    keyboardVisible: false,
    keyboardHeight: 0,
    activeInput: null,
  },
  
  features: {
    voiceInput: true,
    locationTracking: true,
    backgroundSync: true,

    experimentalFeatures: false,
  },
};

// UI slice
const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    // Navigation actions
    setCurrentScreen: (state, action: PayloadAction<UIState['currentScreen']>) => {
      state.previousScreen = state.currentScreen;
      state.currentScreen = action.payload;
      
      // Add to navigation history
      if (state.navigationHistory[state.navigationHistory.length - 1] !== action.payload) {
        state.navigationHistory.push(action.payload);
        
        // Keep only last 10 screens in history
        if (state.navigationHistory.length > 10) {
          state.navigationHistory = state.navigationHistory.slice(-10);
        }
      }
    },
    
    goBack: (state) => {
      if (state.previousScreen) {
        const temp = state.currentScreen;
        state.currentScreen = state.previousScreen as UIState['currentScreen'];
        state.previousScreen = temp;
      }
    },
    
    // Modal actions
    showModal: (state, action: PayloadAction<{ type: ModalState['type']; data?: any }>) => {
      state.modal = {
        isVisible: true,
        type: action.payload.type,
        data: action.payload.data,
      };
    },
    
    hideModal: (state) => {
      state.modal = {
        isVisible: false,
        type: null,
        data: null,
      };
    },
    
    // Overlay actions
    toggleHamburgerMenu: (state, action: PayloadAction<boolean | undefined>) => {
      state.showHamburgerMenu = action.payload !== undefined ? action.payload : !state.showHamburgerMenu;
    },
    
    toggleVoiceInput: (state, action?: PayloadAction<boolean>) => {
      state.showVoiceInput = action?.payload ?? !state.showVoiceInput;
    },
    
    toggleLocationSettings: (state, action?: PayloadAction<boolean>) => {
      state.showLocationSettings = action?.payload ?? !state.showLocationSettings;
    },
    
    toggleUserProfile: (state, action?: PayloadAction<boolean>) => {
      state.showUserProfile = action?.payload ?? !state.showUserProfile;
    },
    
    toggleWelcome: (state, action?: PayloadAction<boolean>) => {
      state.showWelcome = action?.payload ?? !state.showWelcome;
    },
    

    
    // Loading actions
    setLoading: (state, action: PayloadAction<{ key: string; loading: boolean }>) => {
      state.loading[action.payload.key] = action.payload.loading;
    },
    
    setGlobalLoading: (state, action: PayloadAction<boolean>) => {
      state.loading.global = action.payload;
    },
    
    // Toast actions
    showToast: (state, action: PayloadAction<Omit<ToastState, 'id' | 'visible'>>) => {
      const toast: ToastState = {
        ...action.payload,
        id: Date.now().toString(),
        visible: true,
      };
      state.toasts.push(toast);
    },
    
    hideToast: (state, action: PayloadAction<string>) => {
      const toastIndex = state.toasts.findIndex(toast => toast.id === action.payload);
      if (toastIndex > -1) {
        state.toasts[toastIndex].visible = false;
      }
    },
    
    removeToast: (state, action: PayloadAction<string>) => {
      state.toasts = state.toasts.filter(toast => toast.id !== action.payload);
    },
    
    clearAllToasts: (state) => {
      state.toasts = [];
    },
    
    // Form actions
    showTaskCreateForm: (state, action?: PayloadAction<any>) => {
      state.forms.taskCreate = {
        isVisible: true,
        data: action?.payload || {},
      };
    },
    
    hideTaskCreateForm: (state) => {
      state.forms.taskCreate = {
        isVisible: false,
        data: {},
      };
    },
    
    showTaskEditForm: (state, action: PayloadAction<{ taskId: string; data?: any }>) => {
      state.forms.taskEdit = {
        isVisible: true,
        taskId: action.payload.taskId,
        data: action.payload.data || {},
      };
    },
    
    hideTaskEditForm: (state) => {
      state.forms.taskEdit = {
        isVisible: false,
        taskId: null,
        data: {},
      };
    },
    
    // Layout actions
    setSafeAreaInsets: (state, action: PayloadAction<UIState['layout']['safeAreaInsets']>) => {
      state.layout.safeAreaInsets = action.payload;
    },
    
    setHeaderHeight: (state, action: PayloadAction<number>) => {
      state.layout.headerHeight = action.payload;
    },
    
    // Interaction actions
    setScrolling: (state, action: PayloadAction<boolean>) => {
      state.interactions.isScrolling = action.payload;
    },
    
    setKeyboardVisible: (state, action: PayloadAction<{ visible: boolean; height?: number }>) => {
      state.interactions.keyboardVisible = action.payload.visible;
      if (action.payload.height !== undefined) {
        state.interactions.keyboardHeight = action.payload.height;
      }
    },
    
    setActiveInput: (state, action: PayloadAction<string | null>) => {
      state.interactions.activeInput = action.payload;
    },
    
    // Feature flags
    toggleFeature: (state, action: PayloadAction<keyof UIState['features']>) => {
      state.features[action.payload] = !state.features[action.payload];
    },
    
    setFeature: (state, action: PayloadAction<{ feature: keyof UIState['features']; enabled: boolean }>) => {
      state.features[action.payload.feature] = action.payload.enabled;
    },
    
    // Utility actions
    resetUI: (state) => {
      return { ...initialState, features: state.features };
    },
    // Store cleanup actions
    cleanupUIData: (state, action: PayloadAction<UIState>) => {
      const oldNavigationCount = state.navigationHistory.length;
      const oldToastCount = state.toasts.length;

      // Update with cleaned data
      state.navigationHistory = action.payload.navigationHistory;
      state.toasts = action.payload.toasts;

      const navigationRemoved = oldNavigationCount - state.navigationHistory.length;
      const toastsRemoved = oldToastCount - state.toasts.length;

      if (navigationRemoved > 0 || toastsRemoved > 0) {
        console.log(`🧹 UI cleanup: Removed ${navigationRemoved} navigation entries, ${toastsRemoved} toasts`);
      }
    },
  },
});

// Export actions
export const {
  setCurrentScreen,
  goBack,
  showModal,
  hideModal,
  toggleHamburgerMenu,
  toggleVoiceInput,
  toggleLocationSettings,
  toggleUserProfile,
  toggleWelcome,

  setLoading,
  setGlobalLoading,
  showToast,
  hideToast,
  removeToast,
  clearAllToasts,
  showTaskCreateForm,
  hideTaskCreateForm,
  showTaskEditForm,
  hideTaskEditForm,
  setSafeAreaInsets,
  setHeaderHeight,
  setScrolling,
  setKeyboardVisible,
  setActiveInput,
  toggleFeature,
  setFeature,
  resetUI,
  cleanupUIData,
} = uiSlice.actions;

// Export reducer
export default uiSlice.reducer;
