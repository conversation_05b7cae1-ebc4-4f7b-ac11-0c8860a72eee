import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

// Notification interfaces
export interface AppNotification {
  id: string;
  title: string;
  body: string;
  type: 'task' | 'location' | 'system' | 'reminder';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  data?: Record<string, any>;
  scheduledFor?: string;
  createdAt: string;
  readAt?: string;
  actionButtons?: Array<{
    id: string;
    title: string;
    action: string;
  }>;
}

// Async thunks
export const scheduleNotificationAsync = createAsyncThunk(
  'notifications/scheduleNotificationAsync',
  async (notification: Omit<AppNotification, 'id' | 'createdAt'>) => {
    // Simulate scheduling notification
    await new Promise(resolve => setTimeout(resolve, 100));
    
    const scheduledNotification: AppNotification = {
      ...notification,
      id: Date.now().toString(),
      createdAt: new Date().toISOString(),
    };
    
    return scheduledNotification;
  }
);

export const cancelNotificationAsync = createAsyncThunk(
  'notifications/cancelNotificationAsync',
  async (notificationId: string) => {
    // Simulate canceling notification
    await new Promise(resolve => setTimeout(resolve, 50));
    return notificationId;
  }
);

// Initial state
interface NotificationsState {
  notifications: AppNotification[];
  unreadCount: number;
  loading: boolean;
  error: string | null;
  permissions: {
    granted: boolean;
    requestedAt: string | null;
  };
  settings: {
    enabled: boolean;
    sound: boolean;
    vibration: boolean;
    badge: boolean;
    quietHours: {
      enabled: boolean;
      start: string;
      end: string;
    };
  };
}

const initialState: NotificationsState = {
  notifications: [],
  unreadCount: 0,
  loading: false,
  error: null,
  permissions: {
    granted: false,
    requestedAt: null,
  },
  settings: {
    enabled: true,
    sound: true,
    vibration: true,
    badge: true,
    quietHours: {
      enabled: true,
      start: '22:00',
      end: '07:00',
    },
  },
};

// Notifications slice
const notificationsSlice = createSlice({
  name: 'notifications',
  initialState,
  reducers: {
    // Notification management
    addNotification: (state, action: PayloadAction<Omit<AppNotification, 'id' | 'createdAt'>>) => {
      const notification: AppNotification = {
        ...action.payload,
        id: Date.now().toString(),
        createdAt: new Date().toISOString(),
      };
      
      state.notifications.unshift(notification);
      state.unreadCount += 1;
      
      // Keep only last 100 notifications
      if (state.notifications.length > 100) {
        state.notifications = state.notifications.slice(0, 100);
      }
    },
    
    markAsRead: (state, action: PayloadAction<string>) => {
      const notificationIndex = state.notifications.findIndex(n => n.id === action.payload);
      if (notificationIndex > -1 && !state.notifications[notificationIndex].readAt) {
        state.notifications[notificationIndex].readAt = new Date().toISOString();
        state.unreadCount = Math.max(0, state.unreadCount - 1);
      }
    },
    
    markAllAsRead: (state) => {
      const now = new Date().toISOString();
      state.notifications.forEach(notification => {
        if (!notification.readAt) {
          notification.readAt = now;
        }
      });
      state.unreadCount = 0;
    },
    
    removeNotification: (state, action: PayloadAction<string>) => {
      const notificationIndex = state.notifications.findIndex(n => n.id === action.payload);
      if (notificationIndex > -1) {
        const wasUnread = !state.notifications[notificationIndex].readAt;
        state.notifications.splice(notificationIndex, 1);
        if (wasUnread) {
          state.unreadCount = Math.max(0, state.unreadCount - 1);
        }
      }
    },
    
    clearAllNotifications: (state) => {
      state.notifications = [];
      state.unreadCount = 0;
    },
    
    // Permissions
    setPermissions: (state, action: PayloadAction<Partial<NotificationsState['permissions']>>) => {
      state.permissions = { ...state.permissions, ...action.payload };
    },
    
    requestPermissions: (state) => {
      state.permissions.requestedAt = new Date().toISOString();
    },
    
    // Settings
    updateSettings: (state, action: PayloadAction<Partial<NotificationsState['settings']>>) => {
      state.settings = { ...state.settings, ...action.payload };
    },
    
    toggleNotifications: (state) => {
      state.settings.enabled = !state.settings.enabled;
    },
    
    toggleSound: (state) => {
      state.settings.sound = !state.settings.sound;
    },
    
    toggleVibration: (state) => {
      state.settings.vibration = !state.settings.vibration;
    },
    
    toggleBadge: (state) => {
      state.settings.badge = !state.settings.badge;
    },
    
    setQuietHours: (state, action: PayloadAction<{ start: string; end: string }>) => {
      state.settings.quietHours = {
        ...state.settings.quietHours,
        ...action.payload,
      };
    },
    
    toggleQuietHours: (state) => {
      state.settings.quietHours.enabled = !state.settings.quietHours.enabled;
    },
    
    // Utility
    clearError: (state) => {
      state.error = null;
    },
    
    // Bulk operations
    markMultipleAsRead: (state, action: PayloadAction<string[]>) => {
      const now = new Date().toISOString();
      let markedCount = 0;
      
      action.payload.forEach(id => {
        const notificationIndex = state.notifications.findIndex(n => n.id === id);
        if (notificationIndex > -1 && !state.notifications[notificationIndex].readAt) {
          state.notifications[notificationIndex].readAt = now;
          markedCount += 1;
        }
      });
      
      state.unreadCount = Math.max(0, state.unreadCount - markedCount);
    },
    
    removeMultiple: (state, action: PayloadAction<string[]>) => {
      let removedUnreadCount = 0;
      
      state.notifications = state.notifications.filter(notification => {
        const shouldRemove = action.payload.includes(notification.id);
        if (shouldRemove && !notification.readAt) {
          removedUnreadCount += 1;
        }
        return !shouldRemove;
      });
      
      state.unreadCount = Math.max(0, state.unreadCount - removedUnreadCount);
    },
  },
  extraReducers: (builder) => {
    // Schedule notification async
    builder
      .addCase(scheduleNotificationAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(scheduleNotificationAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.notifications.unshift(action.payload);
        state.unreadCount += 1;
      })
      .addCase(scheduleNotificationAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to schedule notification';
      });

    // Cancel notification async
    builder
      .addCase(cancelNotificationAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(cancelNotificationAsync.fulfilled, (state, action) => {
        state.loading = false;
        const notificationIndex = state.notifications.findIndex(n => n.id === action.payload);
        if (notificationIndex > -1) {
          const wasUnread = !state.notifications[notificationIndex].readAt;
          state.notifications.splice(notificationIndex, 1);
          if (wasUnread) {
            state.unreadCount = Math.max(0, state.unreadCount - 1);
          }
        }
      })
      .addCase(cancelNotificationAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to cancel notification';
      });
  },
});

// Export actions
export const {
  addNotification,
  markAsRead,
  markAllAsRead,
  removeNotification,
  clearAllNotifications,
  setPermissions,
  requestPermissions,
  updateSettings,
  toggleNotifications,
  toggleSound,
  toggleVibration,
  toggleBadge,
  setQuietHours,
  toggleQuietHours,
  clearError,
  markMultipleAsRead,
  removeMultiple,
} = notificationsSlice.actions;

// Export reducer
export default notificationsSlice.reducer;
