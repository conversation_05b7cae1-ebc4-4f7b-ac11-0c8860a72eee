import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { networkService, NetworkState, NetworkMetrics } from '../../services/networkService';
import { offlineQueueService, QueueStats } from '../../services/offlineQueueService';

// Async thunks for network operations
export const initializeNetworkService = createAsyncThunk(
  'network/initializeNetworkService',
  async () => {
    await networkService.initialize();
    return networkService.getNetworkState();
  }
);

export const testEndpoint = createAsyncThunk(
  'network/testEndpoint',
  async ({ url, timeout }: { url: string; timeout?: number }) => {
    const isReachable = await networkService.testEndpoint(url, timeout);
    return { url, isReachable, timestamp: new Date().toISOString() };
  }
);

export const waitForConnection = createAsyncThunk(
  'network/waitForConnection',
  async (timeout: number = 30000) => {
    const connected = await networkService.waitForConnection(timeout);
    return { connected, timestamp: new Date().toISOString() };
  }
);

export const updateQueueStats = createAsyncThunk(
  'network/updateQueueStats',
  async () => {
    const stats = offlineQueueService.getStats();
    return stats;
  }
);

// Network slice state
interface NetworkSliceState {
  networkState: NetworkState;
  metrics: NetworkMetrics[];
  queueStats: QueueStats;
  endpointTests: Array<{
    url: string;
    isReachable: boolean;
    timestamp: string;
  }>;
  connectionWaiting: {
    isWaiting: boolean;
    timeout: number;
    startTime: string | null;
  };
  loading: {
    initializing: boolean;
    testing: boolean;
    waiting: boolean;
  };
  error: string | null;
}

const initialState: NetworkSliceState = {
  networkState: {
    isConnected: false,
    isInternetReachable: false,
    type: 'unknown',
    connectionQuality: 'offline',
  },
  metrics: [],
  queueStats: {
    totalOperations: 0,
    pendingOperations: 0,
    failedOperations: 0,
    completedOperations: 0,
    processingOperations: 0,
    cancelledOperations: 0,
    averageProcessingTime: 0,
    totalDataSize: 0,
    atomicGroups: 0,
    conflictedOperations: 0,
    queueHealth: 'healthy' as const,
  },
  endpointTests: [],
  connectionWaiting: {
    isWaiting: false,
    timeout: 0,
    startTime: null,
  },
  loading: {
    initializing: false,
    testing: false,
    waiting: false,
  },
  error: null,
};

// Network slice
const networkSlice = createSlice({
  name: 'network',
  initialState,
  reducers: {
    // Update network state from service events
    updateNetworkState: (state, action: PayloadAction<NetworkState>) => {
      state.networkState = action.payload;
      state.error = null;
    },
    
    // Add new metrics
    addMetrics: (state, action: PayloadAction<NetworkMetrics>) => {
      state.metrics.push(action.payload);
      
      // Keep only last 20 measurements
      if (state.metrics.length > 20) {
        state.metrics = state.metrics.slice(-20);
      }
    },
    
    // Update queue statistics
    setQueueStats: (state, action: PayloadAction<QueueStats>) => {
      state.queueStats = action.payload;
    },
    
    // Clear endpoint tests
    clearEndpointTests: (state) => {
      state.endpointTests = [];
    },
    
    // Clear error
    clearError: (state) => {
      state.error = null;
    },
    
    // Reset connection waiting state
    resetConnectionWaiting: (state) => {
      state.connectionWaiting = {
        isWaiting: false,
        timeout: 0,
        startTime: null,
      };
      state.loading.waiting = false;
    },
    // Store cleanup actions
    cleanupNetworkData: (state, action: PayloadAction<NetworkSliceState>) => {
      const oldMetricsCount = state.metrics.length;
      const oldEndpointTestsCount = state.endpointTests.length;

      // Update with cleaned data
      state.metrics = action.payload.metrics;
      state.endpointTests = action.payload.endpointTests;

      const metricsRemoved = oldMetricsCount - state.metrics.length;
      const endpointTestsRemoved = oldEndpointTestsCount - state.endpointTests.length;

      if (metricsRemoved > 0 || endpointTestsRemoved > 0) {
        console.log(`🧹 Network cleanup: Removed ${metricsRemoved} metrics, ${endpointTestsRemoved} endpoint tests`);
      }
    },
  },
  extraReducers: (builder) => {
    // Initialize network service
    builder
      .addCase(initializeNetworkService.pending, (state) => {
        state.loading.initializing = true;
        state.error = null;
      })
      .addCase(initializeNetworkService.fulfilled, (state, action) => {
        state.loading.initializing = false;
        state.networkState = action.payload;
      })
      .addCase(initializeNetworkService.rejected, (state, action) => {
        state.loading.initializing = false;
        state.error = action.error.message || 'Failed to initialize network service';
      });

    // Test endpoint
    builder
      .addCase(testEndpoint.pending, (state) => {
        state.loading.testing = true;
        state.error = null;
      })
      .addCase(testEndpoint.fulfilled, (state, action) => {
        state.loading.testing = false;
        state.endpointTests.push(action.payload);
        
        // Keep only last 10 tests
        if (state.endpointTests.length > 10) {
          state.endpointTests = state.endpointTests.slice(-10);
        }
      })
      .addCase(testEndpoint.rejected, (state, action) => {
        state.loading.testing = false;
        state.error = action.error.message || 'Failed to test endpoint';
      });

    // Wait for connection
    builder
      .addCase(waitForConnection.pending, (state, action) => {
        state.loading.waiting = true;
        state.connectionWaiting = {
          isWaiting: true,
          timeout: action.meta.arg,
          startTime: new Date().toISOString(),
        };
        state.error = null;
      })
      .addCase(waitForConnection.fulfilled, (state, action) => {
        state.loading.waiting = false;
        state.connectionWaiting.isWaiting = false;
        
        if (!action.payload.connected) {
          state.error = 'Connection timeout - network not available';
        }
      })
      .addCase(waitForConnection.rejected, (state, action) => {
        state.loading.waiting = false;
        state.connectionWaiting.isWaiting = false;
        state.error = action.error.message || 'Failed to wait for connection';
      });

    // Update queue stats
    builder
      .addCase(updateQueueStats.fulfilled, (state, action) => {
        state.queueStats = action.payload;
      });
  },
});

// Export actions
export const {
  updateNetworkState,
  addMetrics,
  setQueueStats,
  clearEndpointTests,
  clearError,
  resetConnectionWaiting,
  cleanupNetworkData,
} = networkSlice.actions;

// Selectors
export const selectNetworkState = (state: { network: NetworkSliceState }) => state.network.networkState;
export const selectIsOnline = (state: { network: NetworkSliceState }) => 
  state.network.networkState.isConnected && state.network.networkState.isInternetReachable;
export const selectIsOffline = (state: { network: NetworkSliceState }) => !selectIsOnline(state);
export const selectConnectionQuality = (state: { network: NetworkSliceState }) => 
  state.network.networkState.connectionQuality;
export const selectLatestMetrics = (state: { network: NetworkSliceState }) => 
  state.network.metrics.length > 0 ? state.network.metrics[state.network.metrics.length - 1] : null;
export const selectAverageLatency = (state: { network: NetworkSliceState }) => {
  const metrics = state.network.metrics;
  if (metrics.length === 0) return 0;
  const sum = metrics.reduce((acc, metric) => acc + metric.latency, 0);
  return sum / metrics.length;
};
export const selectQueueStats = (state: { network: NetworkSliceState }) => state.network.queueStats;
export const selectPendingOperations = (state: { network: NetworkSliceState }) => 
  state.network.queueStats.pendingOperations;
export const selectFailedOperations = (state: { network: NetworkSliceState }) => 
  state.network.queueStats.failedOperations;
export const selectIsWaitingForConnection = (state: { network: NetworkSliceState }) => 
  state.network.connectionWaiting.isWaiting;

// Export reducer
export default networkSlice.reducer;
