import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { Task } from '../../types/task';
import { networkService } from '../../services/networkService';
import { offlineQueueService } from '../../services/offlineQueueService';
import { offlineStorageService } from '../../services/offlineStorageService';
import { mobileTaskService } from '../../services/taskService';

// Async thunks for task operations with offline support
export const addTaskAsync = createAsyncThunk(
  'tasks/addTaskAsync',
  async (task: Omit<Task, 'id' | 'createdAt'>) => {
    const newTask: Task = {
      ...task,
      id: Date.now().toString(),
      createdAt: new Date(),
    };

    if (networkService.isOnline()) {
      try {
        // Try to create task online
        const serverTask = await mobileTaskService.createTask(newTask);
        if (serverTask) {
          // Save to offline storage as backup
          await offlineStorageService.addTaskOffline(serverTask);
          return serverTask;
        }
      } catch (error) {
        console.warn('Failed to create task online, queuing for offline:', error);
      }
    }

    // If offline or online creation failed, queue the operation
    await offlineQueueService.enqueue({
      type: 'CREATE_TASK',
      payload: newTask,
      priority: 'high',
      maxRetries: 3,
    });

    // Save to offline storage immediately
    await offlineStorageService.addTaskOffline(newTask);

    return newTask;
  }
);

export const updateTaskAsync = createAsyncThunk(
  'tasks/updateTaskAsync',
  async ({ id, updates }: { id: string; updates: Partial<Task> }) => {
    const updatedData = {
      ...updates,
      updatedAt: new Date().toISOString(),
    };

    if (networkService.isOnline()) {
      try {
        // Try to update task online
        const serverTask = await mobileTaskService.updateTask(id, updatedData);
        if (serverTask) {
          // Update offline storage
          await offlineStorageService.updateTaskOffline(id, updatedData);
          return { id, updates: updatedData };
        }
      } catch (error) {
        console.warn('Failed to update task online, queuing for offline:', error);
      }
    }

    // If offline or online update failed, queue the operation
    await offlineQueueService.enqueue({
      type: 'UPDATE_TASK',
      payload: { id, updates: updatedData },
      priority: 'medium',
      maxRetries: 3,
    });

    // Update offline storage immediately
    await offlineStorageService.updateTaskOffline(id, updatedData);

    return { id, updates: updatedData };
  }
);

export const deleteTaskAsync = createAsyncThunk(
  'tasks/deleteTaskAsync',
  async (id: string) => {
    if (networkService.isOnline()) {
      try {
        // Try to delete task online
        const success = await mobileTaskService.deleteTask(id);
        if (success) {
          // Remove from offline storage
          await offlineStorageService.deleteTaskOffline(id);
          return id;
        }
      } catch (error) {
        console.warn('Failed to delete task online, queuing for offline:', error);
      }
    }

    // If offline or online deletion failed, queue the operation
    await offlineQueueService.enqueue({
      type: 'DELETE_TASK',
      payload: { id },
      priority: 'medium',
      maxRetries: 3,
    });

    // Remove from offline storage immediately
    await offlineStorageService.deleteTaskOffline(id);

    return id;
  }
);

// Load tasks from offline storage
export const loadOfflineTasksAsync = createAsyncThunk(
  'tasks/loadOfflineTasksAsync',
  async () => {
    try {
      const offlineTasks = await offlineStorageService.loadTasks();
      console.log(`📱 Loaded ${offlineTasks.length} tasks from offline storage`);
      return offlineTasks;
    } catch (error) {
      console.error('Failed to load offline tasks:', error);
      return [];
    }
  }
);

// Sync with server
export const syncTasksAsync = createAsyncThunk(
  'tasks/syncTasksAsync',
  async () => {
    try {
      if (networkService.isOffline()) {
        throw new Error('Cannot sync while offline');
      }

      const syncResult = await offlineStorageService.syncWithServer();

      if (syncResult.success) {
        // Load updated tasks after sync
        const updatedTasks = await offlineStorageService.loadTasks();
        return {
          tasks: updatedTasks,
          syncResult,
        };
      } else {
        throw new Error(`Sync failed: ${syncResult.errors.join(', ')}`);
      }
    } catch (error) {
      console.error('Sync failed:', error);
      throw error;
    }
  }
);

export const bulkUpdateTasksAsync = createAsyncThunk(
  'tasks/bulkUpdateTasksAsync',
  async (updates: Array<{ id: string; updates: Partial<Task> }>) => {
    if (networkService.isOnline()) {
      try {
        // Try bulk update online
        for (const update of updates) {
          await mobileTaskService.updateTask(update.id, update.updates);
          await offlineStorageService.updateTaskOffline(update.id, update.updates);
        }
        return updates;
      } catch (error) {
        console.warn('Failed to bulk update online, queuing for offline:', error);
      }
    }

    // Queue bulk update operation
    await offlineQueueService.enqueue({
      type: 'BULK_UPDATE',
      payload: { updates },
      priority: 'low',
      maxRetries: 3,
    });

    // Update offline storage immediately
    for (const update of updates) {
      await offlineStorageService.updateTaskOffline(update.id, update.updates);
    }

    return updates;
    await new Promise(resolve => setTimeout(resolve, 200));
    
    return updates.map(({ id, updates }) => ({
      id,
      updates: {
        ...updates,
        updatedAt: new Date().toISOString(),
      },
    }));
  }
);

// Task filters and sorting
export interface TaskFilters {
  status: 'all' | 'active' | 'completed';
  priority: 'all' | 'high' | 'medium' | 'low';
  search: string;
  location: {
    enabled: boolean;
    radius: number;
    coordinates?: { latitude: number; longitude: number };
  };
  dateRange: {
    start?: string;
    end?: string;
  };
}

export interface TaskSorting {
  field: 'date' | 'priority' | 'distance' | 'alphabetical';
  direction: 'asc' | 'desc';
}

// Initial state
interface TasksState {
  tasks: Task[];
  loading: boolean;
  error: string | null;
  filters: TaskFilters;
  sorting: TaskSorting;
  selectedTasks: string[];
  lastSync: string | null;
  offline: {
    isOffline: boolean;
    pendingOperations: number;
    lastOfflineSync: string | null;
    conflicts: number;
    storageSize: number;
  };
  sync: {
    isLoading: boolean;
    lastSyncResult: any | null;
    error: string | null;
  };
  stats: {
    total: number;
    active: number;
    completed: number;
    overdue: number;
  };
}

const initialState: TasksState = {
  tasks: [],
  loading: false,
  error: null,
  filters: {
    status: 'all',
    priority: 'all',
    search: '',
    location: {
      enabled: false,
      radius: 1000,
    },
    dateRange: {},
  },
  sorting: {
    field: 'date',
    direction: 'desc',
  },
  selectedTasks: [],
  lastSync: null,
  offline: {
    isOffline: false,
    pendingOperations: 0,
    lastOfflineSync: null,
    conflicts: 0,
    storageSize: 0,
  },
  sync: {
    isLoading: false,
    lastSyncResult: null,
    error: null,
  },
  stats: {
    total: 0,
    active: 0,
    completed: 0,
    overdue: 0,
  },
};

// Tasks slice
const tasksSlice = createSlice({
  name: 'tasks',
  initialState,
  reducers: {
    // Synchronous actions
    setFilters: (state, action: PayloadAction<Partial<TaskFilters>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    setSorting: (state, action: PayloadAction<TaskSorting>) => {
      state.sorting = action.payload;
    },
    setSelectedTasks: (state, action: PayloadAction<string[]>) => {
      state.selectedTasks = action.payload;
    },
    toggleTaskSelection: (state, action: PayloadAction<string>) => {
      const taskId = action.payload;
      const index = state.selectedTasks.indexOf(taskId);
      if (index > -1) {
        state.selectedTasks.splice(index, 1);
      } else {
        state.selectedTasks.push(taskId);
      }
    },
    clearSelectedTasks: (state) => {
      state.selectedTasks = [];
    },
    clearError: (state) => {
      state.error = null;
    },
    // Offline-specific actions
    setOfflineStatus: (state, action: PayloadAction<boolean>) => {
      // Ensure offline object exists
      if (!state.offline) {
        state.offline = {
          isOffline: false,
          pendingOperations: 0,
          lastOfflineSync: null,
          conflicts: 0,
          storageSize: 0,
        };
      }
      state.offline.isOffline = action.payload;
    },
    updateOfflineStats: (state, action: PayloadAction<Partial<TasksState['offline']>>) => {
      // Ensure offline object exists
      if (!state.offline) {
        state.offline = {
          isOffline: false,
          pendingOperations: 0,
          lastOfflineSync: null,
          conflicts: 0,
          storageSize: 0,
        };
      }
      state.offline = { ...state.offline, ...action.payload };
    },
    clearSyncError: (state) => {
      // Ensure sync object exists
      if (!state.sync) {
        state.sync = {
          isLoading: false,
          lastSyncResult: null,
          error: null,
        };
      }
      state.sync.error = null;
    },
    // Store cleanup actions
    cleanupOldTasks: (state, action: PayloadAction<Task[]>) => {
      const oldTaskCount = state.tasks.length;
      state.tasks = action.payload;
      const newTaskCount = state.tasks.length;

      if (oldTaskCount !== newTaskCount) {
        console.log(`🧹 Tasks cleanup: Removed ${oldTaskCount - newTaskCount} old tasks`);
        tasksSlice.caseReducers.updateStats(state);
      }
    },
    updateStats: (state) => {
      const now = new Date();
      state.stats = {
        total: state.tasks.length,
        active: state.tasks.filter(task => !task.completed).length,
        completed: state.tasks.filter(task => task.completed).length,
        overdue: state.tasks.filter(task => {
          // Since Task doesn't have dueDate, count tasks with pending notifications
          if (task.completed) return false;
          return task.coordinates && !task.notificationTriggered;
        }).length,
      };
    },
    // Local task operations (for offline support)
    addTaskLocal: (state, action: PayloadAction<Task>) => {
      state.tasks.unshift(action.payload);
      tasksSlice.caseReducers.updateStats(state);
    },
    updateTaskLocal: (state, action: PayloadAction<{ id: string; updates: Partial<Task> }>) => {
      const { id, updates } = action.payload;
      const taskIndex = state.tasks.findIndex(task => task.id === id);
      if (taskIndex > -1) {
        state.tasks[taskIndex] = { ...state.tasks[taskIndex], ...updates };
        tasksSlice.caseReducers.updateStats(state);
      }
    },
    deleteTaskLocal: (state, action: PayloadAction<string>) => {
      state.tasks = state.tasks.filter(task => task.id !== action.payload);
      state.selectedTasks = state.selectedTasks.filter(id => id !== action.payload);
      tasksSlice.caseReducers.updateStats(state);
    },
  },
  extraReducers: (builder) => {
    // Add task async
    builder
      .addCase(addTaskAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(addTaskAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.tasks.unshift(action.payload);
        tasksSlice.caseReducers.updateStats(state);
      })
      .addCase(addTaskAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to add task';
      });

    // Update task async
    builder
      .addCase(updateTaskAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateTaskAsync.fulfilled, (state, action) => {
        state.loading = false;
        const { id, updates } = action.payload;
        const taskIndex = state.tasks.findIndex(task => task.id === id);
        if (taskIndex > -1) {
          state.tasks[taskIndex] = { ...state.tasks[taskIndex], ...updates };
          tasksSlice.caseReducers.updateStats(state);
        }
      })
      .addCase(updateTaskAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to update task';
      });

    // Delete task async
    builder
      .addCase(deleteTaskAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteTaskAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.tasks = state.tasks.filter(task => task.id !== action.payload);
        state.selectedTasks = state.selectedTasks.filter(id => id !== action.payload);
        tasksSlice.caseReducers.updateStats(state);
      })
      .addCase(deleteTaskAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to delete task';
      });

    // Bulk update tasks async
    builder
      .addCase(bulkUpdateTasksAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(bulkUpdateTasksAsync.fulfilled, (state, action) => {
        state.loading = false;
        action.payload.forEach(({ id, updates }) => {
          const taskIndex = state.tasks.findIndex(task => task.id === id);
          if (taskIndex > -1) {
            state.tasks[taskIndex] = { ...state.tasks[taskIndex], ...updates };
          }
        });
        tasksSlice.caseReducers.updateStats(state);
      })
      .addCase(bulkUpdateTasksAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to update tasks';
      });

    // Load offline tasks async
    builder
      .addCase(loadOfflineTasksAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(loadOfflineTasksAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.tasks = action.payload;
        // Ensure offline object exists
        if (!state.offline) {
          state.offline = {
            isOffline: false,
            pendingOperations: 0,
            lastOfflineSync: null,
            conflicts: 0,
            storageSize: 0,
          };
        }
        state.offline.lastOfflineSync = new Date().toISOString();
        tasksSlice.caseReducers.updateStats(state);
      })
      .addCase(loadOfflineTasksAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to load offline tasks';
      });

    // Sync tasks async
    builder
      .addCase(syncTasksAsync.pending, (state) => {
        // Ensure sync object exists
        if (!state.sync) {
          state.sync = {
            isLoading: false,
            lastSyncResult: null,
            error: null,
          };
        }
        state.sync.isLoading = true;
        state.sync.error = null;
      })
      .addCase(syncTasksAsync.fulfilled, (state, action) => {
        // Ensure sync object exists
        if (!state.sync) {
          state.sync = {
            isLoading: false,
            lastSyncResult: null,
            error: null,
          };
        }
        state.sync.isLoading = false;
        state.tasks = action.payload.tasks;
        state.sync.lastSyncResult = action.payload.syncResult;
        state.lastSync = new Date().toISOString();
        // Ensure offline object exists
        if (!state.offline) {
          state.offline = {
            isOffline: false,
            pendingOperations: 0,
            lastOfflineSync: null,
            conflicts: 0,
            storageSize: 0,
          };
        }
        state.offline.conflicts = action.payload.syncResult.conflicts.length;
        tasksSlice.caseReducers.updateStats(state);
      })
      .addCase(syncTasksAsync.rejected, (state, action) => {
        // Ensure sync object exists
        if (!state.sync) {
          state.sync = {
            isLoading: false,
            lastSyncResult: null,
            error: null,
          };
        }
        state.sync.isLoading = false;
        state.sync.error = action.error.message || 'Failed to sync tasks';
      });
  },
});

// Export actions
export const {
  setFilters,
  setSorting,
  setSelectedTasks,
  toggleTaskSelection,
  clearSelectedTasks,
  clearError,
  setOfflineStatus,
  updateOfflineStats,
  clearSyncError,
  updateStats,
  addTaskLocal,
  updateTaskLocal,
  deleteTaskLocal,
  cleanupOldTasks,
} = tasksSlice.actions;

// Export reducer
export default tasksSlice.reducer;
