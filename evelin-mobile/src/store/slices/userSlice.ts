import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

// User profile interface
export interface UserProfile {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  avatar?: string;
  phone?: string;
  preferences: {
    theme: 'light' | 'dark' | 'system';
    language: string;
    timezone: string;
    notifications: {
      email: boolean;
      push: boolean;
      sms: boolean;
    };
  };
  subscription: {
    plan: 'free' | 'premium' | 'enterprise';
    expiresAt?: string;
    features: string[];
  };
  stats: {
    tasksCreated: number;
    tasksCompleted: number;
    streakDays: number;
    joinedAt: string;
    lastActiveAt: string;
  };
}

// Authentication state
export interface AuthState {
  isAuthenticated: boolean;
  accessToken: string | null;
  refreshToken: string | null;
  expiresAt: string | null;
}

// Async thunks
export const loginAsync = createAsyncThunk(
  'user/loginAsync',
  async (credentials: { email: string; password: string }) => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock successful login
    return {
      user: {
        id: '1',
        email: credentials.email,
        firstName: 'John',
        lastName: 'Doe',
        preferences: {
          theme: 'system' as const,
          language: 'en',
          timezone: 'UTC',
          notifications: {
            email: true,
            push: true,
            sms: false,
          },
        },
        subscription: {
          plan: 'free' as const,
          features: ['basic_tasks', 'voice_input'],
        },
        stats: {
          tasksCreated: 0,
          tasksCompleted: 0,
          streakDays: 0,
          joinedAt: new Date().toISOString(),
          lastActiveAt: new Date().toISOString(),
        },
      },
      auth: {
        accessToken: 'mock_access_token',
        refreshToken: 'mock_refresh_token',
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      },
    };
  }
);

export const updateProfileAsync = createAsyncThunk(
  'user/updateProfileAsync',
  async (updates: Partial<UserProfile>) => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    return updates;
  }
);

export const refreshTokenAsync = createAsyncThunk(
  'user/refreshTokenAsync',
  async (refreshToken: string) => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 300));
    
    return {
      accessToken: 'new_access_token',
      refreshToken: 'new_refresh_token',
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
    };
  }
);

// Initial state
interface UserState {
  profile: UserProfile | null;
  auth: AuthState;
  loading: boolean;
  error: string | null;
  onboarding: {
    completed: boolean;
    currentStep: number;
    steps: string[];
  };
}

const initialState: UserState = {
  profile: null,
  auth: {
    isAuthenticated: false,
    accessToken: null,
    refreshToken: null,
    expiresAt: null,
  },
  loading: false,
  error: null,
  onboarding: {
    completed: false,
    currentStep: 0,
    steps: ['welcome', 'profile', 'permissions', 'preferences'],
  },
};

// User slice
const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    // Authentication actions
    logout: (state) => {
      state.profile = null;
      state.auth = {
        isAuthenticated: false,
        accessToken: null,
        refreshToken: null,
        expiresAt: null,
      };
      state.error = null;
    },
    clearError: (state) => {
      state.error = null;
    },
    
    // Profile actions
    updateProfileLocal: (state, action: PayloadAction<Partial<UserProfile>>) => {
      if (state.profile) {
        state.profile = { ...state.profile, ...action.payload };
      }
    },
    updatePreferences: (state, action: PayloadAction<Partial<UserProfile['preferences']>>) => {
      if (state.profile) {
        state.profile.preferences = { ...state.profile.preferences, ...action.payload };
      }
    },
    updateStats: (state, action: PayloadAction<Partial<UserProfile['stats']>>) => {
      if (state.profile) {
        state.profile.stats = { ...state.profile.stats, ...action.payload };
        state.profile.stats.lastActiveAt = new Date().toISOString();
      }
    },
    
    // Onboarding actions
    setOnboardingStep: (state, action: PayloadAction<number>) => {
      state.onboarding.currentStep = action.payload;
    },
    completeOnboarding: (state) => {
      state.onboarding.completed = true;
      state.onboarding.currentStep = state.onboarding.steps.length;
    },
    resetOnboarding: (state) => {
      state.onboarding.completed = false;
      state.onboarding.currentStep = 0;
    },
    
    // Subscription actions
    updateSubscription: (state, action: PayloadAction<Partial<UserProfile['subscription']>>) => {
      if (state.profile) {
        state.profile.subscription = { ...state.profile.subscription, ...action.payload };
      }
    },
    
    // Activity tracking
    incrementTasksCreated: (state) => {
      if (state.profile) {
        state.profile.stats.tasksCreated += 1;
        state.profile.stats.lastActiveAt = new Date().toISOString();
      }
    },
    incrementTasksCompleted: (state) => {
      if (state.profile) {
        state.profile.stats.tasksCompleted += 1;
        state.profile.stats.lastActiveAt = new Date().toISOString();
        
        // Update streak (simplified logic)
        const today = new Date().toDateString();
        const lastActive = new Date(state.profile.stats.lastActiveAt).toDateString();
        if (today !== lastActive) {
          state.profile.stats.streakDays += 1;
        }
      }
    },
  },
  extraReducers: (builder) => {
    // Login async
    builder
      .addCase(loginAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(loginAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.profile = action.payload.user;
        state.auth = {
          isAuthenticated: true,
          ...action.payload.auth,
        };
      })
      .addCase(loginAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Login failed';
      });

    // Update profile async
    builder
      .addCase(updateProfileAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateProfileAsync.fulfilled, (state, action) => {
        state.loading = false;
        if (state.profile) {
          state.profile = { ...state.profile, ...action.payload };
        }
      })
      .addCase(updateProfileAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Profile update failed';
      });

    // Refresh token async
    builder
      .addCase(refreshTokenAsync.pending, (state) => {
        state.loading = true;
      })
      .addCase(refreshTokenAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.auth = {
          isAuthenticated: true,
          ...action.payload,
        };
      })
      .addCase(refreshTokenAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Token refresh failed';
        // Auto logout on refresh failure
        userSlice.caseReducers.logout(state);
      });
  },
});

// Export actions
export const {
  logout,
  clearError,
  updateProfileLocal,
  updatePreferences,
  updateStats,
  setOnboardingStep,
  completeOnboarding,
  resetOnboarding,
  updateSubscription,
  incrementTasksCreated,
  incrementTasksCompleted,
} = userSlice.actions;

// Export reducer
export default userSlice.reducer;
