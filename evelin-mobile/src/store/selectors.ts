import { createSelector } from 'reselect';
import { RootState } from './index';
import { Task } from '../types/task';
import { tasksApi } from './api/tasksApi';

// Base selectors - RTK Query handles tasks data
export const selectTasksQuery = (state: RootState) => tasksApi.endpoints.getTasks.select({})(state);
export const selectTasks = createSelector([selectTasksQuery], (query) => query.data || []);
export const selectTasksLoading = createSelector([selectTasksQuery], (query) => query.isLoading);
export const selectTasksError = createSelector([selectTasksQuery], (query) => query.error);
export const selectTasksFilters = (state: RootState) => state.tasks.filters;
export const selectTasksSorting = (state: RootState) => state.tasks.sorting;
export const selectSelectedTasks = (state: RootState) => state.tasks.selectedTasks;
export const selectTasksStats = (state: RootState) => state.tasks.stats;

export const selectUserProfile = (state: RootState) => state.user.profile;
export const selectUserAuth = (state: RootState) => state.user.auth;
export const selectUserLoading = (state: RootState) => state.user.loading;
export const selectUserOnboarding = (state: RootState) => state.user.onboarding;

export const selectSettings = (state: RootState) => state.settings.settings;
export const selectSettingsLoading = (state: RootState) => state.settings.loading;
export const selectHasUnsavedChanges = (state: RootState) => state.settings.hasUnsavedChanges;

export const selectCurrentLocation = (state: RootState) => state.location.currentLocation;
export const selectLocationTracking = (state: RootState) => state.location.isTracking;
export const selectLocationPlaces = (state: RootState) => state.location.places;
export const selectLocationPermissions = (state: RootState) => state.location.permissions;

export const selectNotifications = (state: RootState) => state.notifications.notifications;
export const selectUnreadCount = (state: RootState) => state.notifications.unreadCount;
export const selectNotificationSettings = (state: RootState) => state.notifications.settings;

export const selectCurrentScreen = (state: RootState) => state.ui.currentScreen;
export const selectUILoading = (state: RootState) => state.ui.loading;
export const selectToasts = (state: RootState) => state.ui.toasts;
export const selectModal = (state: RootState) => state.ui.modal;
export const selectUIFeatures = (state: RootState) => state.ui.features;



// Memoized selectors
export const selectActiveTasks = createSelector(
  [selectTasks],
  (tasks) => tasks.filter(task => !task.completed)
);

export const selectCompletedTasks = createSelector(
  [selectTasks],
  (tasks) => tasks.filter(task => task.completed)
);

export const selectTasksByCategory = createSelector(
  [selectTasks],
  (tasks) => {
    const grouped = tasks.reduce((acc, task) => {
      const category = task.category || 'uncategorized';
      if (!acc[category]) acc[category] = [];
      acc[category].push(task);
      return acc;
    }, {} as Record<string, Task[]>);

    return grouped;
  }
);

export const selectTasksByLocation = createSelector(
  [selectTasks],
  (tasks) => {
    return {
      withLocation: tasks.filter(task => task.coordinates || task.location),
      withoutLocation: tasks.filter(task => !task.coordinates && !task.location),
    };
  }
);

export const selectTasksNearLocation = createSelector(
  [selectTasks, selectCurrentLocation, (state: RootState, radius: number) => radius],
  (tasks, currentLocation, radius) => {
    if (!currentLocation) return [];
    
    return tasks.filter(task => {
      if (!task.coordinates) return false;
      
      // Simple distance calculation
      const distance = Math.sqrt(
        Math.pow(task.coordinates.lat - currentLocation.latitude, 2) +
        Math.pow(task.coordinates.lng - currentLocation.longitude, 2)
      ) * 111000; // Rough conversion to meters
      
      return distance <= radius;
    });
  }
);

export const selectFilteredAndSortedTasks = createSelector(
  [selectTasks, selectTasksFilters, selectTasksSorting, selectCurrentLocation],
  (tasks, filters, sorting, currentLocation) => {
    let filtered = [...tasks];
    
    // Apply status filter
    if (filters.status !== 'all') {
      filtered = filtered.filter(task => 
        filters.status === 'active' ? !task.completed : task.completed
      );
    }
    
    // Apply priority filter (TaskFilters has priority, not category)
    if (filters.priority !== 'all') {
      filtered = filtered.filter(task => {
        // Since Task doesn't have priority, we'll skip this filter for now
        // In a real app, you'd either add priority to Task or remove this filter
        return true;
      });
    }

    // Apply search filter
    if (filters.search.trim()) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(task =>
        task.text.toLowerCase().includes(searchLower) ||
        task.location?.toLowerCase().includes(searchLower) ||
        task.category?.toLowerCase().includes(searchLower)
      );
    }
    
    // Apply location filter
    if (filters.location.enabled && filters.location.coordinates) {
      filtered = filtered.filter(task => {
        if (!task.coordinates) return false;
        
        const distance = Math.sqrt(
          Math.pow(task.coordinates.lat - filters.location.coordinates!.latitude, 2) +
          Math.pow(task.coordinates.lng - filters.location.coordinates!.longitude, 2)
        ) * 111000;
        
        return distance <= filters.location.radius;
      });
    }
    
    // Apply date range filter
    if (filters.dateRange.start || filters.dateRange.end) {
      filtered = filtered.filter(task => {
        const taskDate = new Date(task.createdAt);
        const start = filters.dateRange.start ? new Date(filters.dateRange.start) : null;
        const end = filters.dateRange.end ? new Date(filters.dateRange.end) : null;
        
        if (start && taskDate < start) return false;
        if (end && taskDate > end) return false;
        return true;
      });
    }
    
    // Apply sorting
    filtered.sort((a, b) => {
      let comparison = 0;
      
      switch (sorting.field) {
        case 'date':
          comparison = new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
          break;
        case 'priority':
          // Since Task doesn't have priority, sort by category instead
          const categoryA = a.category || 'zzz'; // Put uncategorized at end
          const categoryB = b.category || 'zzz';
          comparison = categoryA.localeCompare(categoryB);
          break;
        case 'alphabetical':
          comparison = a.text.localeCompare(b.text);
          break;
        case 'distance':
          if (currentLocation) {
            const distanceA = a.coordinates 
              ? Math.sqrt(
                  Math.pow(a.coordinates.lat - currentLocation.latitude, 2) +
                  Math.pow(a.coordinates.lng - currentLocation.longitude, 2)
                ) * 111000
              : Infinity;
            
            const distanceB = b.coordinates
              ? Math.sqrt(
                  Math.pow(b.coordinates.lat - currentLocation.latitude, 2) +
                  Math.pow(b.coordinates.lng - currentLocation.longitude, 2)
                ) * 111000
              : Infinity;
            
            comparison = distanceA - distanceB;
          }
          break;
      }
      
      return sorting.direction === 'desc' ? comparison : -comparison;
    });
    
    return filtered;
  }
);

export const selectUnreadNotifications = createSelector(
  [selectNotifications],
  (notifications) => notifications.filter(n => !n.readAt)
);

export const selectNotificationsByType = createSelector(
  [selectNotifications],
  (notifications) => {
    const grouped = {
      task: [] as typeof notifications,
      location: [] as typeof notifications,
      system: [] as typeof notifications,
      reminder: [] as typeof notifications,
    };
    
    notifications.forEach(notification => {
      if (grouped[notification.type]) {
        grouped[notification.type].push(notification);
      }
    });
    
    return grouped;
  }
);

export const selectUserStats = createSelector(
  [selectUserProfile, selectTasks],
  (profile, tasks) => {
    if (!profile) return null;
    
    const completedTasks = tasks.filter(task => task.completed).length;
    const activeTasks = tasks.filter(task => !task.completed).length;
    // Since Task interface doesn't have dueDate, we'll count tasks with notifications not triggered
    const pendingNotificationTasks = tasks.filter(task => {
      return !task.completed && task.coordinates && !task.notificationTriggered;
    }).length;
    
    return {
      ...profile.stats,
      currentActiveTasks: activeTasks,
      currentCompletedTasks: completedTasks,
      currentOverdueTasks: pendingNotificationTasks,
      completionRate: tasks.length > 0 ? (completedTasks / tasks.length) * 100 : 0,
    };
  }
);



export const selectAppHealthStatus = createSelector(
  [selectTasksError, selectUserAuth, selectLocationPermissions],
  (tasksError, auth, locationPermissions) => {
    const issues = [];

    if (tasksError) {
      issues.push('Task synchronization error');
    }

    if (!auth.isAuthenticated) {
      issues.push('Authentication required');
    }

    if (!locationPermissions.granted) {
      issues.push('Location permissions needed');
    }

    return {
      healthy: issues.length === 0,
      issues,
      score: Math.max(0, 100 - (issues.length * 25)),
    };
  }
);

// ===== ENHANCED MEMOIZED SELECTORS FOR PERFORMANCE =====

// Task selectors with heavy memoization
export const selectTasksWithLocationData = createSelector(
  [selectTasks, selectLocationPlaces],
  (tasks, places) => {
    return tasks.map(task => {
      if (!task.coordinates) return task;

      // Find nearby places for each task
      const nearbyPlaces = places.filter(place => {
        if (!place.coordinates) return false;

        const distance = Math.sqrt(
          Math.pow(task.coordinates!.lat - place.coordinates.latitude, 2) +
          Math.pow(task.coordinates!.lng - place.coordinates.longitude, 2)
        ) * 111000; // Rough conversion to meters

        return distance <= (task.notificationDistance || 100);
      });

      return {
        ...task,
        nearbyPlaces,
        hasLocationContext: nearbyPlaces.length > 0,
      };
    });
  }
);

export const selectTasksByCreationDate = createSelector(
  [selectTasks],
  (tasks) => {
    return [...tasks].sort((a, b) => {
      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    });
  }
);



export const selectTasksGroupedByDate = createSelector(
  [selectTasks],
  (tasks) => {
    return tasks.reduce((acc, task) => {
      const date = new Date(task.createdAt).toDateString();
      if (!acc[date]) acc[date] = [];
      acc[date].push(task);
      return acc;
    }, {} as Record<string, Task[]>);
  }
);

// Location selectors with memoization
export const selectLocationHistory = (state: RootState) => state.location.history;
export const selectGeofences = (state: RootState) => state.location.geofences;

export const selectActiveGeofences = createSelector(
  [selectGeofences],
  (geofences) => geofences.filter(fence => fence.active)
);

export const selectRecentLocationHistory = createSelector(
  [selectLocationHistory],
  (history) => {
    const oneHourAgo = Date.now() - (60 * 60 * 1000);
    return history.filter(location => location.timestamp > oneHourAgo);
  }
);

export const selectLocationPlacesByType = createSelector(
  [selectLocationPlaces],
  (places) => {
    return places.reduce((acc, place) => {
      if (!acc[place.type]) acc[place.type] = [];
      acc[place.type].push(place);
      return acc;
    }, {} as Record<string, typeof places>);
  }
);

export const selectMostVisitedPlaces = createSelector(
  [selectLocationPlaces],
  (places) => {
    return [...places]
      .sort((a, b) => b.visits - a.visits)
      .slice(0, 10);
  }
);





// UI selectors with memoization
export const selectActiveToasts = createSelector(
  [selectToasts],
  (toasts) => {
    // Since ToastState doesn't have createdAt, just return visible toasts
    return toasts.filter(toast => toast.visible);
  }
);

export const selectNavigationBreadcrumb = createSelector(
  [selectCurrentScreen, (state: RootState) => state.ui.navigationHistory],
  (currentScreen, history) => {
    const breadcrumb = history.slice(-3); // Last 3 screens
    return {
      current: currentScreen,
      previous: breadcrumb,
      canGoBack: history.length > 1,
    };
  }
);

// Network selectors with memoization
export const selectNetworkMetrics = (state: RootState) => state.network.metrics;
export const selectQueueStats = (state: RootState) => state.network.queueStats;

export const selectNetworkHealth = createSelector(
  [selectNetworkMetrics, selectQueueStats, (state: RootState) => state.network.networkState],
  (metrics, queueStats, networkState) => {
    const recentMetrics = metrics.slice(0, 10);
    const avgLatency = recentMetrics.length > 0
      ? recentMetrics.reduce((sum, metric) => sum + metric.latency, 0) / recentMetrics.length
      : 0;

    const avgPacketLoss = recentMetrics.length > 0
      ? recentMetrics.reduce((sum, metric) => sum + metric.packetLoss, 0) / recentMetrics.length
      : 0;

    let healthScore = 100;
    if (!networkState.isConnected) healthScore -= 50;
    if (avgLatency > 2000) healthScore -= 20;
    if (avgPacketLoss > 0.1) healthScore -= 20;
    if (queueStats.failedOperations > 5) healthScore -= 10;

    return {
      score: Math.max(0, healthScore),
      status: healthScore > 80 ? 'excellent' : healthScore > 60 ? 'good' : healthScore > 40 ? 'poor' : 'critical',
      avgLatency,
      packetLoss: avgPacketLoss * 100,
      pendingOperations: queueStats.pendingOperations,
    };
  }
);

// Combined selectors for dashboard/overview screens
export const selectDashboardData = createSelector(
  [
    selectTasksStats,
    selectNetworkHealth,
    selectActiveGeofences,
  ],
  (taskStats, networkHealth, activeGeofences) => ({
    tasks: taskStats,
    network: networkHealth,
    location: {
      activeGeofences: activeGeofences.length,
      trackingEnabled: activeGeofences.length > 0,
    },
    overallHealth: {
      score: Math.min(
        (taskStats.total > 0 ? 100 : 80),
        networkHealth.score
      ),
    },
  })
);
