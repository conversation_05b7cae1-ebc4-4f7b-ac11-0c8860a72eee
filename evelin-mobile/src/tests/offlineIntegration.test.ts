import { offlineManager } from '../services/offlineManager';
import { networkService } from '../services/networkService';
import { offlineQueueService } from '../services/offlineQueueService';
import { offlineStorageService } from '../services/offlineStorageService';
import { store } from '../store';
import { addTaskAsync, updateTaskAsync, deleteTaskAsync } from '../store/slices/tasksSlice';

// Mock network conditions
const mockNetworkOffline = () => {
  jest.spyOn(networkService, 'isOnline').mockReturnValue(false);
  jest.spyOn(networkService, 'isOffline').mockReturnValue(true);
  jest.spyOn(networkService, 'getNetworkState').mockReturnValue({
    isConnected: false,
    isInternetReachable: false,
    type: 'none',
    connectionQuality: 'offline',
  });
};

const mockNetworkOnline = () => {
  jest.spyOn(networkService, 'isOnline').mockReturnValue(true);
  jest.spyOn(networkService, 'isOffline').mockReturnValue(false);
  jest.spyOn(networkService, 'getNetworkState').mockReturnValue({
    isConnected: true,
    isInternetReachable: true,
    type: 'wifi',
    connectionQuality: 'excellent',
  });
};

describe('Offline Integration Tests', () => {
  beforeEach(async () => {
    // Clear all data before each test
    await offlineStorageService.clearAllOfflineData();
    await offlineQueueService.clearAll();
    
    // Initialize offline manager
    await offlineManager.initialize({
      autoSync: false, // Disable auto sync for testing
      syncInterval: 60000,
      maxRetries: 3,
      enableBackgroundSync: false,
      conflictResolution: 'server-wins',
    });
  });

  afterEach(() => {
    // Clean up mocks
    jest.restoreAllMocks();
  });

  describe('Offline Task Operations', () => {
    it('should queue task creation when offline', async () => {
      // Arrange
      mockNetworkOffline();
      
      const newTask = {
        text: 'Test Offline Task',
        location: 'Test Location',
        category: 'personal' as const,
        completed: false,
        notificationTriggered: false,
        notificationDistance: 100,
        coordinates: {
          lat: 40.7128,
          lng: -74.0060,
        },
      };

      // Act
      const result = await store.dispatch(addTaskAsync(newTask));

      // Assert
      expect(result.meta.requestStatus).toBe('fulfilled');
      
      const queueStats = offlineQueueService.getStats();
      expect(queueStats.pendingOperations).toBe(1);
      expect(queueStats.totalOperations).toBe(1);
    });

    it('should queue task updates when offline', async () => {
      // Arrange
      mockNetworkOffline();
      
      // First create a task offline
      const newTask = {
        text: 'Test Task',
        location: 'Test Location',
        category: 'personal' as const,
        completed: false,
        notificationTriggered: false,
        notificationDistance: 100,
        coordinates: {
          lat: 40.7128,
          lng: -74.0060,
        },
      };
      
      const createResult = await store.dispatch(addTaskAsync(newTask));
      const taskId = (createResult.payload as any).id;

      // Act - Update the task
      const updateResult = await store.dispatch(updateTaskAsync({
        id: taskId,
        updates: {
          text: 'Updated Task Title',
          location: 'Updated location',
        },
      }));

      // Assert
      expect(updateResult.meta.requestStatus).toBe('fulfilled');
      
      const queueStats = offlineQueueService.getStats();
      expect(queueStats.pendingOperations).toBe(2); // Create + Update
    });

    it('should queue task deletion when offline', async () => {
      // Arrange
      mockNetworkOffline();
      
      // First create a task offline
      const newTask = {
        text: 'Task to Delete',
        location: 'Test Location',
        category: 'work' as const,
        completed: false,
        notificationTriggered: false,
        notificationDistance: 100,
        coordinates: {
          lat: 40.7128,
          lng: -74.0060,
        },
      };

      const createResult = await store.dispatch(addTaskAsync(newTask));
      const taskId = (createResult.payload as any).id;

      // Act - Delete the task
      const deleteResult = await store.dispatch(deleteTaskAsync(taskId));

      // Assert
      expect(deleteResult.meta.requestStatus).toBe('fulfilled');
      
      const queueStats = offlineQueueService.getStats();
      expect(queueStats.pendingOperations).toBe(2); // Create + Delete
    });
  });

  describe('Online/Offline Transitions', () => {
    it('should process queued operations when coming back online', async () => {
      // Arrange - Start offline
      mockNetworkOffline();
      
      const newTask = {
        text: 'Queued Task',
        location: 'Test Location',
        category: 'urgent' as const,
        completed: false,
        notificationTriggered: false,
        notificationDistance: 100,
        coordinates: {
          lat: 40.7128,
          lng: -74.0060,
        },
      };

      // Create task while offline
      await store.dispatch(addTaskAsync(newTask));
      
      let queueStats = offlineQueueService.getStats();
      expect(queueStats.pendingOperations).toBe(1);

      // Act - Come back online and sync
      mockNetworkOnline();
      const syncSuccess = await offlineManager.syncNow();

      // Assert
      expect(syncSuccess).toBe(true);
      
      queueStats = offlineQueueService.getStats();
      expect(queueStats.pendingOperations).toBe(0);
      expect(queueStats.completedOperations).toBe(1);
    });

    it('should handle network state changes correctly', async () => {
      // Arrange
      mockNetworkOnline();
      expect(offlineManager.isOnline()).toBe(true);

      // Act - Go offline
      mockNetworkOffline();
      
      // Simulate network state change event
      networkService.emit('connectionLost', networkService.getNetworkState());

      // Assert
      expect(offlineManager.isOffline()).toBe(true);
      
      // Act - Come back online
      mockNetworkOnline();
      
      // Simulate network state change event
      networkService.emit('connectionRestored', networkService.getNetworkState());

      // Assert
      expect(offlineManager.isOnline()).toBe(true);
    });
  });

  describe('Data Persistence', () => {
    it('should persist tasks locally when offline', async () => {
      // Arrange
      mockNetworkOffline();
      
      const newTask = {
        text: 'Persistent Task',
        location: 'Test Location',
        category: 'personal' as const,
        completed: false,
        notificationTriggered: false,
        notificationDistance: 100,
        coordinates: {
          lat: 40.7128,
          lng: -74.0060,
        },
      };

      // Act
      const result = await store.dispatch(addTaskAsync(newTask));
      const taskId = (result.payload as any).id;

      // Assert - Task should be in local storage
      const offlineTasks = await offlineStorageService.getTasksOffline();
      expect(offlineTasks).toHaveLength(1);
      expect(offlineTasks[0].id).toBe(taskId);
      expect(offlineTasks[0].text).toBe('Persistent Task');
    });

    it('should load offline tasks on app startup', async () => {
      // Arrange - Create some offline tasks first
      mockNetworkOffline();
      
      const tasks = [
        {
          text: 'Offline Task 1',
          location: 'Location 1',
          category: 'work' as const,
          completed: false,
          notificationTriggered: false,
          notificationDistance: 100,
          coordinates: {
            lat: 40.7128,
            lng: -74.0060,
          },
        },
        {
          text: 'Offline Task 2',
          location: 'Location 2',
          category: 'personal' as const,
          completed: false,
          notificationTriggered: false,
          notificationDistance: 200,
          coordinates: {
            lat: 40.7589,
            lng: -73.9851,
          },
        },
      ];

      for (const task of tasks) {
        await store.dispatch(addTaskAsync(task));
      }

      // Act - Simulate app restart by reinitializing
      await offlineManager.destroy();
      await offlineManager.initialize({
        autoSync: false,
        syncInterval: 60000,
        maxRetries: 3,
        enableBackgroundSync: false,
        conflictResolution: 'server-wins',
      });

      // Assert - Tasks should be loaded from offline storage
      // TODO: Update test to work with RTK Query
      // const state = store.getState();
      // expect(state.tasks.tasks).toHaveLength(2);
      // expect(state.tasks.tasks[0].text).toBe('Offline Task 1');
      // expect(state.tasks.tasks[1].text).toBe('Offline Task 2');
    });
  });

  describe('Error Handling', () => {
    it('should handle queue processing errors gracefully', async () => {
      // Arrange
      mockNetworkOffline();
      
      const newTask = {
        text: 'Error Test Task',
        location: 'Test Location',
        category: 'personal' as const,
        completed: false,
        notificationTriggered: false,
        notificationDistance: 100,
        coordinates: {
          lat: 40.7128,
          lng: -74.0060,
        },
      };

      await store.dispatch(addTaskAsync(newTask));

      // Mock a network error when trying to sync
      mockNetworkOnline();
      jest.spyOn(offlineQueueService, 'processQueue').mockRejectedValue(new Error('Network error'));

      // Act
      const syncSuccess = await offlineManager.syncNow();

      // Assert
      expect(syncSuccess).toBe(false);
      
      const queueStats = offlineQueueService.getStats();
      expect(queueStats.pendingOperations).toBe(1); // Should still be pending
    });

    it('should retry failed operations', async () => {
      // Arrange
      mockNetworkOffline();
      
      const newTask = {
        text: 'Retry Test Task',
        location: 'Test Location',
        category: 'personal' as const,
        completed: false,
        notificationTriggered: false,
        notificationDistance: 100,
        coordinates: {
          lat: 40.7128,
          lng: -74.0060,
        },
      };

      await store.dispatch(addTaskAsync(newTask));

      // Simulate a failed operation
      mockNetworkOnline();
      jest.spyOn(offlineQueueService, 'processQueue')
        .mockRejectedValueOnce(new Error('First attempt failed'))
        .mockResolvedValueOnce(undefined);

      // Act - First sync attempt should fail
      let syncSuccess = await offlineManager.syncNow();
      expect(syncSuccess).toBe(false);

      // Act - Retry should succeed
      await offlineManager.retryFailedOperations();
      syncSuccess = await offlineManager.syncNow();

      // Assert
      expect(syncSuccess).toBe(true);
    });
  });
});
