import { createClient, SupabaseClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { SecureStorageService, SecureStorageKeys, SecureStorageUtils } from './secureStorageService';

// Environment variables (these should be set in your build process)
const SUPABASE_URL = process.env.EXPO_PUBLIC_SUPABASE_URL || "https://cwsygtogkpqdijbkvvns.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImN3c3lndG9na3BxZGlqYmt2dm5zIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQwNTA5NDAsImV4cCI6MjA2OTYyNjk0MH0.89zyE13N2XXWLMpvw25twaNikl98M29ryMTt79uHO_c";

/**
 * Secure Supabase Client with enhanced security features
 */
class SecureSupabaseClient {
  private client: SupabaseClient | null = null;
  private initialized = false;

  /**
   * Initialize the Supabase client with secure configuration
   */
  async initialize(): Promise<SupabaseClient> {
    if (this.client && this.initialized) {
      return this.client;
    }

    try {
      // Try to get credentials from secure storage first
      const secureConfig = await SecureStorageUtils.getAPIConfig();

      const supabaseUrl = secureConfig.supabaseUrl || SUPABASE_URL;
      const supabaseKey = secureConfig.supabaseAnonKey || SUPABASE_PUBLISHABLE_KEY;

      // Store credentials securely if they came from environment variables
      if (!secureConfig.supabaseUrl || !secureConfig.supabaseAnonKey) {
        await SecureStorageUtils.storeAPIConfig({
          supabaseUrl: SUPABASE_URL,
          supabaseAnonKey: SUPABASE_PUBLISHABLE_KEY,
        });
        console.log('🔐 Stored Supabase credentials securely');
      }

      // Create Supabase client with enhanced security
      this.client = createClient(supabaseUrl, supabaseKey, {
        auth: {
          storage: AsyncStorage,
          persistSession: true,
          autoRefreshToken: true,
          detectSessionInUrl: false, // Disable for mobile
        },
        global: {
          headers: {
            'X-Client-Info': 'evelin-mobile-app',
          },
        },
      });

      // Set up auth state change listener for token management
      this.client.auth.onAuthStateChange(async (event, session) => {
        console.log('🔐 Auth state changed:', event);

        if (session) {
          // Store tokens securely
          await SecureStorageUtils.storeAuthTokens({
            jwtToken: session.access_token,
            refreshToken: session.refresh_token,
            userSession: session,
          });
        } else if (event === 'SIGNED_OUT') {
          // Clear tokens on sign out
          await SecureStorageUtils.clearAuthData();
        }
      });

      this.initialized = true;
      console.log('✅ Supabase client initialized securely');
      return this.client;
    } catch (error) {
      console.error('❌ Failed to initialize secure Supabase client:', error);

      // Fallback to basic client
      this.client = createClient(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
        auth: {
          storage: AsyncStorage,
          persistSession: true,
          autoRefreshToken: true,
        }
      });

      console.warn('⚠️ Using fallback Supabase client');
      return this.client;
    }
  }

  /**
   * Get the Supabase client instance
   */
  getClient(): SupabaseClient {
    if (!this.client) {
      throw new Error('Supabase client not initialized. Call initialize() first.');
    }
    return this.client;
  }

  /**
   * Refresh the authentication token
   */
  async refreshToken(): Promise<void> {
    if (!this.client) {
      throw new Error('Supabase client not initialized');
    }

    try {
      const { data, error } = await this.client.auth.refreshSession();
      if (error) {
        console.error('❌ Token refresh failed:', error);
        throw error;
      }

      if (data.session) {
        await SecureStorageUtils.storeAuthTokens({
          jwtToken: data.session.access_token,
          refreshToken: data.session.refresh_token,
          userSession: data.session,
        });
        console.log('✅ Token refreshed successfully');
      }
    } catch (error) {
      console.error('❌ Failed to refresh token:', error);
      throw error;
    }
  }

  /**
   * Sign out and clear all stored credentials
   */
  async signOut(): Promise<void> {
    if (!this.client) {
      throw new Error('Supabase client not initialized');
    }

    try {
      await this.client.auth.signOut();
      await SecureStorageUtils.clearAuthData();
      console.log('✅ Signed out and cleared credentials');
    } catch (error) {
      console.error('❌ Failed to sign out:', error);
      throw error;
    }
  }
}

// Create singleton instance
const secureSupabaseClient = new SecureSupabaseClient();

// Initialize and export the client
export const initializeSupabase = () => secureSupabaseClient.initialize();
export const getSupabaseClient = () => secureSupabaseClient.getClient();
export const refreshSupabaseToken = () => secureSupabaseClient.refreshToken();
export const signOutSupabase = () => secureSupabaseClient.signOut();

// Legacy export removed - all code should use getSupabaseClient() instead
