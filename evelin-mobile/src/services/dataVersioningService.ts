import AsyncStorage from '@react-native-async-storage/async-storage';
import { EventEmitter } from '../utils/EventEmitter';

export interface DataVersion {
  version: number;
  timestamp: string;
  description: string;
  migrations: Migration[];
  rollbackMigrations?: Migration[];
}

export interface Migration {
  id: string;
  name: string;
  version: number;
  targetVersion: number;
  type: 'schema' | 'data' | 'index' | 'cleanup';
  priority: 'low' | 'medium' | 'high' | 'critical';
  execute: (data: any) => Promise<any>;
  rollback?: (data: any) => Promise<any>;
  validate?: (data: any) => Promise<boolean>;
  dependencies?: string[]; // Migration IDs this depends on
  metadata?: {
    affectedTables?: string[];
    dataSize?: number;
    estimatedTime?: number;
    backupRequired?: boolean;
  };
}

export interface MigrationResult {
  success: boolean;
  version: number;
  migrationsApplied: string[];
  errors: string[];
  warnings: string[];
  rollbackAvailable: boolean;
  backupCreated?: string;
  duration: number;
}

export interface DataSchema {
  version: number;
  tables: {
    [tableName: string]: {
      version: number;
      fields: {
        [fieldName: string]: {
          type: 'string' | 'number' | 'boolean' | 'object' | 'array' | 'date';
          required: boolean;
          default?: any;
          validation?: (value: any) => boolean;
        };
      };
      indexes?: string[];
      constraints?: string[];
    };
  };
}

class DataVersioningService extends EventEmitter {
  private readonly VERSION_KEY = 'data_version';
  private readonly SCHEMA_KEY = 'data_schema';
  private readonly MIGRATION_HISTORY_KEY = 'migration_history';
  private readonly BACKUP_PREFIX = 'backup_v';
  
  private currentVersion = 1;
  private currentSchema: DataSchema | null = null;
  private migrationHistory: Array<{id: string, version: number, timestamp: string, success: boolean}> = [];
  
  // Define all available migrations
  private migrations: Migration[] = [
    {
      id: 'add_task_category_v2',
      name: 'Add category field to tasks',
      version: 1,
      targetVersion: 2,
      type: 'schema',
      priority: 'medium',
      execute: async (data: any) => {
        if (data.tasks) {
          data.tasks = data.tasks.map((task: any) => ({
            ...task,
            category: task.category || 'general',
          }));
        }
        return data;
      },
      rollback: async (data: any) => {
        if (data.tasks) {
          data.tasks = data.tasks.map((task: any) => {
            const { category, ...taskWithoutCategory } = task;
            return taskWithoutCategory;
          });
        }
        return data;
      },
      validate: async (data: any) => {
        return data.tasks?.every((task: any) => typeof task.category === 'string') ?? true;
      },
      metadata: {
        affectedTables: ['tasks'],
        estimatedTime: 1000,
        backupRequired: true,
      },
    },
    {
      id: 'add_task_coordinates_v3',
      name: 'Add coordinates field to tasks',
      version: 2,
      targetVersion: 3,
      type: 'schema',
      priority: 'medium',
      execute: async (data: any) => {
        if (data.tasks) {
          data.tasks = data.tasks.map((task: any) => ({
            ...task,
            coordinates: task.coordinates || null,
          }));
        }
        return data;
      },
      rollback: async (data: any) => {
        if (data.tasks) {
          data.tasks = data.tasks.map((task: any) => {
            const { coordinates, ...taskWithoutCoordinates } = task;
            return taskWithoutCoordinates;
          });
        }
        return data;
      },
      validate: async (data: any) => {
        return data.tasks?.every((task: any) => 
          task.coordinates === null || 
          (typeof task.coordinates === 'object' && 
           typeof task.coordinates.lat === 'number' && 
           typeof task.coordinates.lng === 'number')
        ) ?? true;
      },
      metadata: {
        affectedTables: ['tasks'],
        estimatedTime: 1500,
        backupRequired: true,
      },
    },
    {
      id: 'add_notification_fields_v4',
      name: 'Add notification fields to tasks',
      version: 3,
      targetVersion: 4,
      type: 'schema',
      priority: 'high',
      execute: async (data: any) => {
        if (data.tasks) {
          data.tasks = data.tasks.map((task: any) => ({
            ...task,
            notificationDistance: task.notificationDistance || 2000,
            notificationTriggered: task.notificationTriggered || false,
          }));
        }
        return data;
      },
      rollback: async (data: any) => {
        if (data.tasks) {
          data.tasks = data.tasks.map((task: any) => {
            const { notificationDistance, notificationTriggered, ...taskWithoutNotifications } = task;
            return taskWithoutNotifications;
          });
        }
        return data;
      },
      validate: async (data: any) => {
        return data.tasks?.every((task: any) => 
          typeof task.notificationDistance === 'number' &&
          typeof task.notificationTriggered === 'boolean'
        ) ?? true;
      },
      metadata: {
        affectedTables: ['tasks'],
        estimatedTime: 1000,
        backupRequired: true,
      },
    },
    {
      id: 'cleanup_old_temp_tasks_v4',
      name: 'Clean up old temporary tasks',
      version: 4,
      targetVersion: 4,
      type: 'cleanup',
      priority: 'low',
      execute: async (data: any) => {
        if (data.tasks) {
          // Remove tasks older than 30 days with temp IDs
          const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
          data.tasks = data.tasks.filter((task: any) => {
            if (task.id.startsWith('temp-')) {
              const taskDate = new Date(task.createdAt);
              return taskDate > thirtyDaysAgo;
            }
            return true;
          });
        }
        return data;
      },
      metadata: {
        affectedTables: ['tasks'],
        estimatedTime: 500,
        backupRequired: false,
      },
    },
  ];

  async initialize(): Promise<void> {
    try {
      await this.loadCurrentVersion();
      await this.loadCurrentSchema();
      await this.loadMigrationHistory();
      
      console.log(`✅ Data versioning service initialized (v${this.currentVersion})`);
    } catch (error) {
      console.error('❌ Failed to initialize data versioning service:', error);
    }
  }

  async checkForMigrations(): Promise<boolean> {
    const targetVersion = this.getLatestVersion();
    return this.currentVersion < targetVersion;
  }

  async migrate(targetVersion?: number): Promise<MigrationResult> {
    const startTime = Date.now();
    const finalTargetVersion = targetVersion || this.getLatestVersion();
    
    const result: MigrationResult = {
      success: false,
      version: this.currentVersion,
      migrationsApplied: [],
      errors: [],
      warnings: [],
      rollbackAvailable: false,
      duration: 0,
    };

    try {
      if (this.currentVersion >= finalTargetVersion) {
        result.success = true;
        result.duration = Date.now() - startTime;
        return result;
      }

      // Get migrations to apply
      const migrationsToApply = this.getMigrationsForVersionRange(this.currentVersion, finalTargetVersion);
      
      if (migrationsToApply.length === 0) {
        result.success = true;
        result.duration = Date.now() - startTime;
        return result;
      }

      // Create backup if any migration requires it
      const requiresBackup = migrationsToApply.some(m => m.metadata?.backupRequired);
      let backupKey: string | undefined;
      
      if (requiresBackup) {
        backupKey = await this.createBackup();
        result.backupCreated = backupKey;
        result.rollbackAvailable = true;
      }

      // Load current data
      let data = await this.loadAllData();

      // Apply migrations in order
      for (const migration of migrationsToApply) {
        try {
          console.log(`🔄 Applying migration: ${migration.name}`);
          this.emit('migrationStarted', migration);
          
          // Execute migration
          data = await migration.execute(data);
          
          // Validate if validation function exists
          if (migration.validate) {
            const isValid = await migration.validate(data);
            if (!isValid) {
              throw new Error(`Migration validation failed: ${migration.name}`);
            }
          }
          
          result.migrationsApplied.push(migration.id);
          this.migrationHistory.push({
            id: migration.id,
            version: migration.targetVersion,
            timestamp: new Date().toISOString(),
            success: true,
          });
          
          console.log(`✅ Migration completed: ${migration.name}`);
          this.emit('migrationCompleted', migration);
          
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown migration error';
          result.errors.push(`Migration ${migration.name} failed: ${errorMessage}`);
          console.error(`❌ Migration failed: ${migration.name}`, error);
          
          this.migrationHistory.push({
            id: migration.id,
            version: migration.targetVersion,
            timestamp: new Date().toISOString(),
            success: false,
          });
          
          // Stop migration process on error
          break;
        }
      }

      // Save migrated data
      if (result.errors.length === 0) {
        await this.saveAllData(data);
        this.currentVersion = finalTargetVersion;
        await this.saveCurrentVersion();
        result.version = finalTargetVersion;
        result.success = true;
        
        console.log(`✅ Migration completed successfully to version ${finalTargetVersion}`);
      } else {
        // Rollback if backup exists and there were errors
        if (backupKey) {
          await this.rollbackFromBackup(backupKey);
          result.warnings.push('Rolled back to backup due to migration errors');
        }
      }

      await this.saveMigrationHistory();
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      result.errors.push(`Migration process failed: ${errorMessage}`);
      console.error('Migration process failed:', error);
    }

    result.duration = Date.now() - startTime;
    this.emit('migrationCompleted', result);
    return result;
  }

  async rollbackToVersion(targetVersion: number): Promise<MigrationResult> {
    const startTime = Date.now();
    
    const result: MigrationResult = {
      success: false,
      version: this.currentVersion,
      migrationsApplied: [],
      errors: [],
      warnings: [],
      rollbackAvailable: false,
      duration: 0,
    };

    try {
      if (this.currentVersion <= targetVersion) {
        result.success = true;
        result.duration = Date.now() - startTime;
        return result;
      }

      // Get migrations to rollback (in reverse order)
      const migrationsToRollback = this.getMigrationsForVersionRange(targetVersion, this.currentVersion)
        .reverse()
        .filter(m => m.rollback);

      if (migrationsToRollback.length === 0) {
        result.errors.push('No rollback migrations available for this version range');
        result.duration = Date.now() - startTime;
        return result;
      }

      // Create backup before rollback
      const backupKey = await this.createBackup();
      result.backupCreated = backupKey;

      // Load current data
      let data = await this.loadAllData();

      // Apply rollback migrations
      for (const migration of migrationsToRollback) {
        try {
          console.log(`🔄 Rolling back migration: ${migration.name}`);
          data = await migration.rollback!(data);
          result.migrationsApplied.push(`rollback_${migration.id}`);
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown rollback error';
          result.errors.push(`Rollback ${migration.name} failed: ${errorMessage}`);
          break;
        }
      }

      if (result.errors.length === 0) {
        await this.saveAllData(data);
        this.currentVersion = targetVersion;
        await this.saveCurrentVersion();
        result.version = targetVersion;
        result.success = true;
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      result.errors.push(`Rollback process failed: ${errorMessage}`);
    }

    result.duration = Date.now() - startTime;
    return result;
  }

  private getLatestVersion(): number {
    return Math.max(...this.migrations.map(m => m.targetVersion));
  }

  private getMigrationsForVersionRange(fromVersion: number, toVersion: number): Migration[] {
    return this.migrations
      .filter(m => m.version >= fromVersion && m.targetVersion <= toVersion)
      .sort((a, b) => a.targetVersion - b.targetVersion);
  }

  private async createBackup(): Promise<string> {
    const backupKey = `${this.BACKUP_PREFIX}${this.currentVersion}_${Date.now()}`;
    const data = await this.loadAllData();
    await AsyncStorage.setItem(backupKey, JSON.stringify({
      version: this.currentVersion,
      timestamp: new Date().toISOString(),
      data,
    }));
    return backupKey;
  }

  private async rollbackFromBackup(backupKey: string): Promise<void> {
    const backupData = await AsyncStorage.getItem(backupKey);
    if (backupData) {
      const backup = JSON.parse(backupData);
      await this.saveAllData(backup.data);
      this.currentVersion = backup.version;
      await this.saveCurrentVersion();
    }
  }

  private async loadAllData(): Promise<any> {
    const data: any = {};
    
    // Load tasks
    const tasksData = await AsyncStorage.getItem('offline_tasks');
    if (tasksData) {
      data.tasks = JSON.parse(tasksData);
    }
    
    // Load other data types as needed
    const settingsData = await AsyncStorage.getItem('app_settings');
    if (settingsData) {
      data.settings = JSON.parse(settingsData);
    }
    
    return data;
  }

  private async saveAllData(data: any): Promise<void> {
    if (data.tasks) {
      await AsyncStorage.setItem('offline_tasks', JSON.stringify(data.tasks));
    }
    
    if (data.settings) {
      await AsyncStorage.setItem('app_settings', JSON.stringify(data.settings));
    }
  }

  private async loadCurrentVersion(): Promise<void> {
    try {
      const versionData = await AsyncStorage.getItem(this.VERSION_KEY);
      if (versionData) {
        this.currentVersion = parseInt(versionData, 10);
      }
    } catch (error) {
      console.error('Failed to load current version:', error);
    }
  }

  private async saveCurrentVersion(): Promise<void> {
    try {
      await AsyncStorage.setItem(this.VERSION_KEY, this.currentVersion.toString());
    } catch (error) {
      console.error('Failed to save current version:', error);
    }
  }

  private async loadCurrentSchema(): Promise<void> {
    try {
      const schemaData = await AsyncStorage.getItem(this.SCHEMA_KEY);
      if (schemaData) {
        this.currentSchema = JSON.parse(schemaData);
      }
    } catch (error) {
      console.error('Failed to load current schema:', error);
    }
  }

  private async loadMigrationHistory(): Promise<void> {
    try {
      const historyData = await AsyncStorage.getItem(this.MIGRATION_HISTORY_KEY);
      if (historyData) {
        this.migrationHistory = JSON.parse(historyData);
      }
    } catch (error) {
      console.error('Failed to load migration history:', error);
      this.migrationHistory = [];
    }
  }

  private async saveMigrationHistory(): Promise<void> {
    try {
      await AsyncStorage.setItem(this.MIGRATION_HISTORY_KEY, JSON.stringify(this.migrationHistory));
    } catch (error) {
      console.error('Failed to save migration history:', error);
    }
  }

  // Public methods
  getCurrentVersion(): number {
    return this.currentVersion;
  }

  getAvailableVersions(): number[] {
    return [...new Set(this.migrations.map(m => m.targetVersion))].sort();
  }

  getMigrationHistory(): Array<{id: string, version: number, timestamp: string, success: boolean}> {
    return [...this.migrationHistory];
  }

  async cleanupOldBackups(keepCount: number = 5): Promise<void> {
    try {
      const allKeys = await AsyncStorage.getAllKeys();
      const backupKeys = allKeys
        .filter(key => key.startsWith(this.BACKUP_PREFIX))
        .sort()
        .reverse();
      
      if (backupKeys.length > keepCount) {
        const keysToDelete = backupKeys.slice(keepCount);
        await AsyncStorage.multiRemove(keysToDelete);
        console.log(`🧹 Cleaned up ${keysToDelete.length} old backups`);
      }
    } catch (error) {
      console.error('Failed to cleanup old backups:', error);
    }
  }

  async validateDataIntegrity(): Promise<{valid: boolean, errors: string[]}> {
    const errors: string[] = [];
    
    try {
      const data = await this.loadAllData();
      
      // Validate tasks structure
      if (data.tasks) {
        for (const task of data.tasks) {
          if (!task.id || typeof task.text !== 'string') {
            errors.push(`Invalid task structure: ${JSON.stringify(task)}`);
          }
        }
      }
      
      return { valid: errors.length === 0, errors };
    } catch (error) {
      errors.push(`Data integrity check failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return { valid: false, errors };
    }
  }
}

export const dataVersioningService = new DataVersioningService();
