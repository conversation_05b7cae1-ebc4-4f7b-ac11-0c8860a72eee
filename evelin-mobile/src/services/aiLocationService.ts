import { mobileLocationService } from './locationService';
import { mobileGoogleMapsService } from './googleMapsService';
import { apiConfigService } from './apiConfigService';

interface LocationMatch {
  placeName: string;
  searchQuery: string;
  category: string;
  confidence: number;
  reasoning: string;
}

interface PlaceResult {
  name: string;
  coordinates: { lat: number; lng: number };
  address: string;
  category: string;
  distance?: number;
}

/**
 * AI-Powered Location Service
 * Uses multiple AI techniques to intelligently match location names without hard-coded databases
 */
export class AILocationService {
  
  /**
   * Extract location information from text using AI-powered natural language processing
   */
  async parseLocationFromText(taskText: string): Promise<LocationMatch[]> {
    const matches: LocationMatch[] = [];
    
    try {
      // 1. Use AI to extract potential location entities
      const aiExtractedLocations = await this.extractLocationEntitiesWithAI(taskText);
      matches.push(...aiExtractedLocations);
      
      // 2. Pattern-based extraction as fallback
      const patternMatches = this.extractLocationPatterns(taskText);
      matches.push(...patternMatches);
      
      // 3. Context-aware business type detection
      const contextMatches = await this.detectBusinessContext(taskText);
      matches.push(...contextMatches);
      
    } catch (error) {
      console.error('Error in AI location parsing:', error);
      // Fallback to basic pattern matching
      const basicMatches = this.extractLocationPatterns(taskText);
      matches.push(...basicMatches);
    }
    
    // Remove duplicates and sort by confidence
    const uniqueMatches = this.deduplicateMatches(matches);
    return uniqueMatches.sort((a, b) => b.confidence - a.confidence);
  }

  /**
   * Use Hugging Face AI APIs to extract location entities from text
   */
  private async extractLocationEntitiesWithAI(text: string): Promise<LocationMatch[]> {
    const matches: LocationMatch[] = [];

    try {
      // Get secure Hugging Face API token
      const hfToken = apiConfigService.getHuggingFaceToken();
      if (!hfToken) {
        console.log('Hugging Face API token not available for NER');
        return matches;
      }

      // Use Hugging Face NER (Named Entity Recognition) model
      const response = await fetch('https://api-inference.huggingface.co/models/dbmdz/bert-large-cased-finetuned-conll03-english', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${hfToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          inputs: text
        })
      });

      if (response.ok) {
        const entities = await response.json();

        // Extract location and organization entities
        for (const entity of entities) {
          if (entity.entity_group === 'LOC' || entity.entity_group === 'ORG') {
            const locationName = entity.word.replace(/##/g, ''); // Clean BERT tokens

            matches.push({
              placeName: locationName,
              searchQuery: locationName,
              category: this.inferCategoryFromContext(text, locationName),
              confidence: entity.score,
              reasoning: `AI NER detected ${entity.entity_group} entity`
            });
          }
        }

        console.log(`🧠 AI NER extracted ${matches.length} location entities from: "${text}"`);
      } else {
        console.log('Hugging Face NER API request failed:', response.status);
      }
    } catch (error) {
      console.log('AI NER unavailable, using fallback methods:', error);
    }

    return matches;
  }

  /**
   * Extract location patterns using intelligent regex and linguistic analysis
   */
  private extractLocationPatterns(text: string): LocationMatch[] {
    const matches: LocationMatch[] = [];
    const lowerText = text.toLowerCase();
    
    // Enhanced pattern matching for various languages and formats
    const locationPatterns = [
      // Direct location mentions
      /(?:at|from|to|near|in)\s+([a-zA-Z][a-zA-Z0-9\s&'-]{2,30})/gi,
      /(?:go to|visit|find)\s+([a-zA-Z][a-zA-Z0-9\s&'-]{2,30})/gi,
      
      // Store/business patterns
      /(?:buy|get|purchase).*?(?:at|from)\s+([a-zA-Z][a-zA-Z0-9\s&'-]{2,30})/gi,
      
      // Numbers in business names (7-Eleven, etc.)
      /\b([a-zA-Z]*\d+[a-zA-Z\s-]*[a-zA-Z]+)\b/gi,
      
      // Hyphenated business names
      /\b([a-zA-Z]+-[a-zA-Z]+(?:\s+[a-zA-Z]+)*)\b/gi,
      
      // Possessive forms (McDonald's, etc.)
      /\b([a-zA-Z]+(?:'s|\s+[a-zA-Z]+)*)\b/gi
    ];

    for (const pattern of locationPatterns) {
      let match;
      while ((match = pattern.exec(text)) !== null) {
        const locationName = match[1].trim();
        
        // Filter out common words that aren't locations
        if (this.isLikelyLocationName(locationName)) {
          matches.push({
            placeName: locationName,
            searchQuery: locationName,
            category: this.inferCategoryFromContext(text, locationName),
            confidence: 0.7,
            reasoning: 'Pattern-based extraction'
          });
        }
      }
    }
    
    return matches;
  }

  /**
   * Detect business context using AI-powered text analysis
   */
  private async detectBusinessContext(text: string): Promise<LocationMatch[]> {
    const matches: LocationMatch[] = [];
    
    try {
      // Use AI to classify the business intent
      const businessType = await this.classifyBusinessIntent(text);
      
      if (businessType) {
        // Generate intelligent search queries based on business type
        const searchQueries = this.generateSmartSearchQueries(text, businessType);
        
        for (const query of searchQueries) {
          matches.push({
            placeName: query,
            searchQuery: query,
            category: businessType,
            confidence: 0.6,
            reasoning: `AI-inferred ${businessType} context`
          });
        }
      }
    } catch (error) {
      console.log('Business context detection unavailable');
    }
    
    return matches;
  }

  /**
   * Classify business intent using Hugging Face AI
   */
  private async classifyBusinessIntent(text: string): Promise<string | null> {
    try {
      // Get secure Hugging Face API token
      const hfToken = apiConfigService.getHuggingFaceToken();
      if (!hfToken) {
        console.log('Hugging Face API token not available for business classification');
        return null;
      }

      // Use Hugging Face's text classification for business type detection
      const response = await fetch('https://api-inference.huggingface.co/models/facebook/bart-large-mnli', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${hfToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          inputs: text,
          parameters: {
            candidate_labels: [
              'grocery store', 'pharmacy', 'restaurant', 'gas station',
              'convenience store', 'shopping mall', 'bank', 'hospital',
              'hardware store', 'electronics store', 'clothing store'
            ]
          }
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.scores && result.scores[0] > 0.5) {
          console.log(`🏪 AI classified business intent: "${result.labels[0]}" (confidence: ${result.scores[0].toFixed(3)})`);
          return result.labels[0];
        }
      } else {
        console.log('Hugging Face business classification API request failed:', response.status);
      }
    } catch (error) {
      console.log('Business intent classification unavailable:', error);
    }

    return null;
  }

  /**
   * Generate smart search queries based on context
   */
  private generateSmartSearchQueries(text: string, businessType: string): string[] {
    const queries: string[] = [];
    
    // Extract potential brand names or specific mentions
    const words = text.split(/\s+/);
    const capitalizedWords = words.filter(word => 
      /^[A-Z]/.test(word) && word.length > 2 && !this.isCommonWord(word)
    );
    
    // Add capitalized words as potential business names
    for (const word of capitalizedWords) {
      queries.push(word);
    }
    
    // Add business type as fallback
    queries.push(businessType);
    
    return queries.slice(0, 3); // Limit to top 3 queries
  }

  /**
   * Check if a string is likely a location name
   */
  private isLikelyLocationName(name: string): boolean {
    const cleanName = name.trim();
    
    // Too short or too long
    if (cleanName.length < 2 || cleanName.length > 50) return false;
    
    // Common words that aren't locations
    const commonWords = [
      'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with',
      'buy', 'get', 'go', 'come', 'take', 'make', 'have', 'do', 'will', 'can',
      'some', 'any', 'all', 'this', 'that', 'these', 'those', 'my', 'your',
      'his', 'her', 'its', 'our', 'their', 'me', 'you', 'him', 'her', 'us', 'them'
    ];
    
    return !commonWords.includes(cleanName.toLowerCase());
  }

  /**
   * Check if a word is a common English word
   */
  private isCommonWord(word: string): boolean {
    const commonWords = [
      'Buy', 'Get', 'Go', 'Come', 'Take', 'Make', 'Have', 'Do', 'Will', 'Can',
      'The', 'And', 'Or', 'But', 'In', 'On', 'At', 'To', 'For', 'Of', 'With',
      'Some', 'Any', 'All', 'This', 'That', 'These', 'Those'
    ];
    
    return commonWords.includes(word);
  }

  /**
   * Infer business category from context
   */
  private inferCategoryFromContext(text: string, locationName: string): string {
    const lowerText = text.toLowerCase();
    const lowerLocation = locationName.toLowerCase();
    
    // Food-related keywords
    if (/\b(food|eat|restaurant|cafe|coffee|pizza|burger|sandwich)\b/.test(lowerText)) {
      return 'restaurant';
    }
    
    // Shopping keywords
    if (/\b(buy|shop|store|market|mall|purchase)\b/.test(lowerText)) {
      return 'store';
    }
    
    // Medical keywords
    if (/\b(pharmacy|medicine|drug|prescription|health)\b/.test(lowerText)) {
      return 'pharmacy';
    }
    
    // Gas/fuel keywords
    if (/\b(gas|fuel|station|petrol)\b/.test(lowerText)) {
      return 'gas_station';
    }
    
    // Numbers in name suggest convenience store
    if (/\d/.test(lowerLocation)) {
      return 'convenience_store';
    }
    
    return 'establishment';
  }

  /**
   * Remove duplicate matches and merge similar ones
   */
  private deduplicateMatches(matches: LocationMatch[]): LocationMatch[] {
    const unique: LocationMatch[] = [];
    
    for (const match of matches) {
      const existing = unique.find(u => 
        this.areSimilarMatches(u.placeName, match.placeName)
      );
      
      if (existing) {
        // Merge with higher confidence
        if (match.confidence > existing.confidence) {
          existing.confidence = match.confidence;
          existing.reasoning = `${existing.reasoning} + ${match.reasoning}`;
        }
      } else {
        unique.push(match);
      }
    }
    
    return unique;
  }

  /**
   * Check if two location names are similar
   */
  private areSimilarMatches(name1: string, name2: string): boolean {
    const clean1 = name1.toLowerCase().trim();
    const clean2 = name2.toLowerCase().trim();
    
    // Exact match
    if (clean1 === clean2) return true;
    
    // One contains the other
    if (clean1.includes(clean2) || clean2.includes(clean1)) return true;
    
    // Similar length and high character overlap
    const minLength = Math.min(clean1.length, clean2.length);
    if (minLength > 3) {
      const overlap = this.calculateCharacterOverlap(clean1, clean2);
      return overlap > 0.8;
    }
    
    return false;
  }

  /**
   * Calculate character overlap between two strings
   */
  private calculateCharacterOverlap(str1: string, str2: string): number {
    const chars1 = new Set(str1.split(''));
    const chars2 = new Set(str2.split(''));
    
    const intersection = new Set([...chars1].filter(x => chars2.has(x)));
    const union = new Set([...chars1, ...chars2]);
    
    return intersection.size / union.size;
  }

  /**
   * Main function to get intelligent location suggestions
   */
  async getLocationSuggestions(taskText: string, radius: number = 500): Promise<PlaceResult[]> {
    try {
      console.log('🤖 Starting AI-powered location analysis for:', taskText);
      
      // Get current user location
      const userLocation = await mobileLocationService.getCurrentLocation();
      if (!userLocation) {
        throw new Error('Could not get user location');
      }

      // Parse task text for location clues using AI
      const locationMatches = await this.parseLocationFromText(taskText);
      console.log('🧠 AI location analysis results:', locationMatches);

      if (locationMatches.length === 0) {
        console.log('No location matches found for:', taskText);
        return [];
      }

      // Find actual places nearby using Google Maps with intelligent matching
      const places = await this.findNearbyPlacesWithAI(locationMatches, userLocation, radius);
      console.log('📍 AI-enhanced nearby places:', places);

      return places;

    } catch (error) {
      console.error('Error in AI location detection:', error);
      return [];
    }
  }

  /**
   * Find nearby places using AI-enhanced Google Maps search
   */
  private async findNearbyPlacesWithAI(
    locationMatches: LocationMatch[], 
    userLocation: { latitude: number; longitude: number }, 
    radius: number
  ): Promise<PlaceResult[]> {
    const results: PlaceResult[] = [];

    for (const match of locationMatches.slice(0, 3)) { // Limit to top 3 matches for performance
      try {
        console.log(`🔍 AI-enhanced search for: ${match.searchQuery} (${match.reasoning})`);

        // Use the enhanced Google Maps search with AI matching
        const googleResults = await mobileGoogleMapsService.searchNearbyPlaces(
          match.searchQuery,
          userLocation,
          radius
        );

        // Convert Google Maps results to our format
        const convertedResults = googleResults.slice(0, 3).map(place => ({
          name: place.name,
          coordinates: place.coordinates,
          address: place.address,
          category: match.category,
          distance: place.distance
        }));

        results.push(...convertedResults);

        console.log(`✅ Found ${convertedResults.length} ${match.placeName} locations via AI`);

      } catch (error) {
        console.error(`Error in AI search for ${match.placeName}:`, error);
      }
    }

    return results.sort((a, b) => (a.distance || 0) - (b.distance || 0));
  }

  /**
   * Find a specific place by name using AI-enhanced matching
   */
  async findSpecificPlace(placeName: string, radius: number = 500): Promise<PlaceResult | null> {
    try {
      // Get current user location
      const userLocation = await mobileLocationService.getCurrentLocation();
      if (!userLocation) {
        throw new Error('Could not get user location');
      }

      console.log(`🎯 AI-enhanced search for specific place: ${placeName} within ${radius}m`);

      // Use the enhanced Google Maps search with AI matching
      const result = await mobileGoogleMapsService.findSpecificPlace(placeName, userLocation, radius);

      if (result) {
        console.log(`✅ AI found ${placeName}: ${result.name} at ${result.address} (${result.distance}m)`);
        return {
          name: result.name,
          coordinates: result.coordinates,
          address: result.address,
          category: 'store',
          distance: result.distance
        };
      }

      console.log(`❌ No ${placeName} found within ${radius}m using AI`);
      return null;

    } catch (error) {
      console.error(`Error in AI-enhanced search for ${placeName}:`, error);
      return null;
    }
  }
}

export const aiLocationService = new AILocationService();
