import AsyncStorage from '@react-native-async-storage/async-storage';
import { Linking, Alert } from 'react-native';
import * as Haptics from 'expo-haptics';
import { Appearance } from 'react-native';

export interface AppSettings {
  darkMode: boolean;
  soundEffects: boolean;
  hapticFeedback: boolean;
  notifications: boolean;
  backgroundLocation: boolean;
  defaultNotificationDistance: number;
  quietHours: {
    enabled: boolean;
    start: string;
    end: string;
  };
}

export interface UserProfile {
  firstName: string;
  lastName: string;
  email: string;
  avatar?: string;
  createdAt: string;
}

class AppSettingsService {
  private settings: AppSettings = {
    darkMode: false,
    soundEffects: true,
    hapticFeedback: true,
    notifications: true,
    backgroundLocation: false,
    defaultNotificationDistance: 200,
    quietHours: {
      enabled: true,
      start: '22:00',
      end: '07:00',
    },
  };

  private userProfile: UserProfile | null = null;

  async initialize(): Promise<void> {
    try {
      await this.loadSettings();
      await this.loadUserProfile();
      await this.loadSoundEffects();
      this.applyDarkMode();
    } catch (error) {
      console.error('Error initializing app settings:', error);
    }
  }

  // Settings Management
  async loadSettings(): Promise<AppSettings> {
    try {
      const settingsJson = await AsyncStorage.getItem('appSettings');
      if (settingsJson) {
        this.settings = { ...this.settings, ...JSON.parse(settingsJson) };
      }
      return this.settings;
    } catch (error) {
      console.error('Error loading settings:', error);
      return this.settings;
    }
  }

  async saveSettings(newSettings: Partial<AppSettings>): Promise<void> {
    try {
      this.settings = { ...this.settings, ...newSettings };
      await AsyncStorage.setItem('appSettings', JSON.stringify(this.settings));
      
      // Apply settings immediately
      if (newSettings.darkMode !== undefined) {
        this.applyDarkMode();
      }
    } catch (error) {
      console.error('Error saving settings:', error);
    }
  }

  getSettings(): AppSettings {
    return this.settings;
  }

  // User Profile Management
  async loadUserProfile(): Promise<UserProfile | null> {
    try {
      const profileJson = await AsyncStorage.getItem('userProfile');
      if (profileJson) {
        this.userProfile = JSON.parse(profileJson);
      }
      return this.userProfile;
    } catch (error) {
      console.error('Error loading user profile:', error);
      return null;
    }
  }

  async saveUserProfile(profile: Partial<UserProfile>): Promise<void> {
    try {
      if (!this.userProfile) {
        this.userProfile = {
          firstName: '',
          lastName: '',
          email: '',
          createdAt: new Date().toISOString(),
          ...profile,
        };
      } else {
        this.userProfile = { ...this.userProfile, ...profile };
      }
      
      await AsyncStorage.setItem('userProfile', JSON.stringify(this.userProfile));
    } catch (error) {
      console.error('Error saving user profile:', error);
    }
  }

  getUserProfile(): UserProfile | null {
    return this.userProfile;
  }

  // Dark Mode
  private applyDarkMode(): void {
    if (this.settings.darkMode) {
      Appearance.setColorScheme('dark');
    } else {
      Appearance.setColorScheme('light');
    }
  }

  async toggleDarkMode(): Promise<boolean> {
    const newValue = !this.settings.darkMode;
    await this.saveSettings({ darkMode: newValue });
    return newValue;
  }

  // Sound Effects
  private async loadSoundEffects(): Promise<void> {
    try {
      if (this.settings.soundEffects) {
        // For now, we'll use system sounds or haptic feedback
        // In the future, you can add actual sound files
        console.log('Sound effects enabled');
      }
    } catch (error) {
      console.error('Error loading sound effects:', error);
    }
  }

  async playSound(soundName: string): Promise<void> {
    if (!this.settings.soundEffects) return;

    try {
      // For now, use haptic feedback as sound substitute
      switch (soundName) {
        case 'success':
          await this.triggerHaptic('success');
          break;
        case 'error':
          await this.triggerHaptic('error');
          break;
        case 'tap':
          await this.triggerHaptic('light');
          break;
        default:
          await this.triggerHaptic('light');
      }
    } catch (error) {
      console.error(`Error playing sound ${soundName}:`, error);
    }
  }

  async toggleSoundEffects(): Promise<boolean> {
    const newValue = !this.settings.soundEffects;
    await this.saveSettings({ soundEffects: newValue });
    
    if (newValue) {
      await this.loadSoundEffects();
      await this.playSound('success');
    }
    
    return newValue;
  }

  // Haptic Feedback
  async triggerHaptic(type: 'light' | 'medium' | 'heavy' | 'success' | 'warning' | 'error' = 'light'): Promise<void> {
    if (!this.settings.hapticFeedback) return;

    try {
      switch (type) {
        case 'light':
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
          break;
        case 'medium':
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
          break;
        case 'heavy':
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
          break;
        case 'success':
          await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          break;
        case 'warning':
          await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
          break;
        case 'error':
          await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
          break;
      }
    } catch (error) {
      console.error('Error triggering haptic feedback:', error);
    }
  }

  async toggleHapticFeedback(): Promise<boolean> {
    const newValue = !this.settings.hapticFeedback;
    await this.saveSettings({ hapticFeedback: newValue });
    
    if (newValue) {
      await this.triggerHaptic('success');
    }
    
    return newValue;
  }

  // System Settings Navigation
  async openAppSettings(): Promise<void> {
    try {
      await Linking.openSettings();
    } catch (error) {
      console.error('Error opening app settings:', error);
      Alert.alert(
        'Settings Unavailable',
        'Could not open device settings. Please open Settings app manually and navigate to this app.'
      );
    }
  }

  async openLocationSettings(): Promise<void> {
    try {
      // Try to open location settings directly
      const url = 'app-settings:location';
      const canOpen = await Linking.canOpenURL(url);
      
      if (canOpen) {
        await Linking.openURL(url);
      } else {
        // Fallback to general app settings
        await this.openAppSettings();
      }
    } catch (error) {
      console.error('Error opening location settings:', error);
      await this.openAppSettings();
    }
  }

  async openNotificationSettings(): Promise<void> {
    try {
      // Try to open notification settings directly
      const url = 'app-settings:notification';
      const canOpen = await Linking.canOpenURL(url);
      
      if (canOpen) {
        await Linking.openURL(url);
      } else {
        // Fallback to general app settings
        await this.openAppSettings();
      }
    } catch (error) {
      console.error('Error opening notification settings:', error);
      await this.openAppSettings();
    }
  }

  // Data Management
  async exportUserData(): Promise<string> {
    try {
      const data = {
        settings: this.settings,
        profile: this.userProfile,
        exportDate: new Date().toISOString(),
        version: '1.0.0',
      };
      
      return JSON.stringify(data, null, 2);
    } catch (error) {
      console.error('Error exporting user data:', error);
      throw error;
    }
  }

  async clearAllData(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([
        'appSettings',
        'userProfile',
        'locationSettings',
        'tasks',
        'taskHistory',
      ]);
      
      // Reset to defaults
      this.settings = {
        darkMode: false,
        soundEffects: true,
        hapticFeedback: true,
        notifications: true,
        backgroundLocation: false,
        defaultNotificationDistance: 200,
        quietHours: {
          enabled: true,
          start: '22:00',
          end: '07:00',
        },
      };
      
      this.userProfile = null;
      
      // Sound effects cleanup (no longer needed with expo-audio removal)
      console.log('Sound effects cleaned up');
      
    } catch (error) {
      console.error('Error clearing all data:', error);
      throw error;
    }
  }

  async deleteAccount(): Promise<void> {
    try {
      // Clear all local data
      await this.clearAllData();
      
      // Note: In a real app, you would also call your backend API
      // to delete the user account from the server
      console.log('Account deletion completed (local data cleared)');
    } catch (error) {
      console.error('Error deleting account:', error);
      throw error;
    }
  }

  // Utility Methods
  getGreeting(): string {
    const hour = new Date().getHours();
    const firstName = this.userProfile?.firstName || 'there';
    
    if (hour < 12) {
      return `Good morning, ${firstName}!`;
    } else if (hour < 17) {
      return `Good afternoon, ${firstName}!`;
    } else {
      return `Good evening, ${firstName}!`;
    }
  }

  getWelcomeMessage(): string {
    const firstName = this.userProfile?.firstName;
    if (firstName) {
      return `Hi, ${firstName}! 👋`;
    }
    return 'Welcome to Evelin! 👋';
  }
}

export const appSettingsService = new AppSettingsService();
