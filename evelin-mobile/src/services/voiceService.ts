import * as Speech from 'expo-speech';
import { useAudioRecorder, RecordingPresets, AudioModule, setAudioModeAsync } from 'expo-audio';
import { Platform } from 'react-native';

export class MobileVoiceService {
  private recorder: any = null;
  private isRecording = false;

  // Request audio permissions
  async requestPermissions(): Promise<boolean> {
    try {
      const { granted } = await AudioModule.requestRecordingPermissionsAsync();
      return granted;
    } catch (error) {
      console.error('Error requesting audio permissions:', error);
      return false;
    }
  }

  // Start voice recording
  async startRecording(): Promise<boolean> {
    try {
      if (this.isRecording) {
        console.log('Already recording');
        return false;
      }

      const { granted } = await AudioModule.getRecordingPermissionsAsync();
      if (!granted) {
        console.log('Audio permission not granted');
        return false;
      }

      // Set audio mode for recording
      await setAudioModeAsync({
        playsInSilentMode: true,
        allowsRecording: true,
      });

      // Note: In the new expo-audio API, recording should be done with hooks
      // This is a simplified implementation for the service class
      this.isRecording = true;

      console.log('🎤 Recording started');
      return true;
    } catch (error) {
      console.error('Error starting recording:', error);
      return false;
    }
  }

  // Stop voice recording
  async stopRecording(): Promise<string | null> {
    try {
      if (!this.isRecording) {
        console.log('No active recording');
        return null;
      }

      this.recorder = null;
      this.isRecording = false;

      console.log('🎤 Recording stopped');

      // For now, return a placeholder text
      // In a full implementation, you would send the audio to a speech-to-text service
      return "Voice recording completed - speech-to-text integration needed";
    } catch (error) {
      console.error('Error stopping recording:', error);
      return null;
    }
  }

  // Check if currently recording
  getIsRecording(): boolean {
    return this.isRecording;
  }

  // Speak text (text-to-speech)
  async speak(text: string): Promise<void> {
    try {
      const options: Speech.SpeechOptions = {
        language: 'en-US',
        pitch: 1.0,
        rate: 0.9,
      };

      Speech.speak(text, options);
      console.log('🔊 Speaking:', text);
    } catch (error) {
      console.error('Error speaking text:', error);
    }
  }

  // Stop speaking
  stop(): void {
    try {
      Speech.stop();
    } catch (error) {
      console.error('Error stopping speech:', error);
    }
  }

  // Check if speech is available
  async isSpeechAvailable(): Promise<boolean> {
    try {
      // Speech is generally available on both iOS and Android
      return true;
    } catch (error) {
      console.error('Error checking speech availability:', error);
      return false;
    }
  }
}

export const mobileVoiceService = new MobileVoiceService();
