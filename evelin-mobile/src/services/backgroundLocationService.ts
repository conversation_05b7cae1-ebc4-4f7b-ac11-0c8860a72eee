import * as Location from 'expo-location';
import * as TaskManager from 'expo-task-manager';
import * as BackgroundTask from 'expo-background-task';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AppState, AppStateStatus } from 'react-native';
import { mobileNotificationService } from './notificationService';
import { mobileTaskService } from './taskService';
import { intelligentLocationService } from './intelligentLocationService';
import { Task } from '../types/task';

const BACKGROUND_LOCATION_TASK = 'background-location';
const LOCATION_CHECK_INTERVAL = 30000; // 30 seconds
const HOME_WORK_RADIUS = 200; // 200 meters
const BATTERY_OPTIMIZATION_RADIUS = 100; // 100 meters for battery optimization

interface LocationSettings {
  backgroundEnabled: boolean;
  homeLocation?: { latitude: number; longitude: number; name: string };
  workLocation?: { latitude: number; longitude: number; name: string };
  batteryOptimization: boolean;
  onlyWhenMoving: boolean;
  quietHours: { start: string; end: string; enabled: boolean };
}

interface UserMovementState {
  isAtHome: boolean;
  isAtWork: boolean;
  isMoving: boolean;
  lastKnownLocation?: Location.LocationObject;
  lastMovementTime: number;
}

class BackgroundLocationService {
  private isInitialized = false;
  private isTracking = false;
  private appStateSubscription: any = null;
  private cleanupTimer: NodeJS.Timeout | null = null;
  private lastActivityTime = Date.now();
  private memoryCleanupInterval: NodeJS.Timeout | null = null;

  private userMovementState: UserMovementState = {
    isAtHome: false,
    isAtWork: false,
    isMoving: false,
    lastMovementTime: Date.now(),
  };

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Register background task
      await this.registerBackgroundTask();

      // Initialize background task
      await BackgroundTask.registerTaskAsync('background-location-check', {
        minimumInterval: 30, // 30 seconds minimum
      });

      // Set up app state monitoring for aggressive cleanup
      this.setupAppStateMonitoring();

      // Set up memory cleanup interval
      this.setupMemoryCleanup();

      this.isInitialized = true;
      console.log('✅ Background location service initialized');
    } catch (error) {
      console.error('❌ Failed to initialize background location service:', error);
    }
  }

  private setupAppStateMonitoring(): void {
    // Monitor app state changes for aggressive cleanup
    this.appStateSubscription = AppState.addEventListener('change', (nextAppState: AppStateStatus) => {
      this.handleAppStateChange(nextAppState);
    });
  }

  private setupMemoryCleanup(): void {
    // Set up periodic memory cleanup
    this.memoryCleanupInterval = setInterval(() => {
      this.performMemoryCleanup();
    }, 300000); // Every 5 minutes
  }

  private handleAppStateChange(nextAppState: AppStateStatus): void {
    console.log(`📱 App state changed to: ${nextAppState}`);

    if (nextAppState === 'background') {
      // App went to background - start aggressive cleanup
      this.onAppBackground();
    } else if (nextAppState === 'active') {
      // App became active - restore normal operation
      this.onAppForeground();
    }
  }

  private onAppBackground(): void {
    console.log('📱 App backgrounded - starting aggressive cleanup');

    // Update last activity time
    this.lastActivityTime = Date.now();

    // Start cleanup timer for extended background time
    this.cleanupTimer = setTimeout(() => {
      this.performAggressiveCleanup();
    }, 600000); // 10 minutes in background

    // Reduce location update frequency when in background
    this.optimizeForBackground();
  }

  private onAppForeground(): void {
    console.log('📱 App foregrounded - restoring normal operation');

    // Cancel cleanup timer
    if (this.cleanupTimer) {
      clearTimeout(this.cleanupTimer);
      this.cleanupTimer = null;
    }

    // Restore normal location tracking if needed
    this.restoreFromBackground();
  }

  private async optimizeForBackground(): Promise<void> {
    try {
      const settings = await this.getLocationSettings();
      if (!settings.backgroundEnabled || !this.isTracking) return;

      // Restart with more aggressive battery optimization
      await this.stopBackgroundLocationTracking();
      await this.startBackgroundLocationTracking(true, true); // Force start with background optimization
    } catch (error) {
      console.error('Error optimizing for background:', error);
    }
  }

  private async restoreFromBackground(): Promise<void> {
    try {
      const settings = await this.getLocationSettings();
      if (!settings.backgroundEnabled) return;

      // Restart with normal settings
      if (this.isTracking) {
        await this.stopBackgroundLocationTracking();
        await this.startBackgroundLocationTracking(true, false); // Normal optimization
      }
    } catch (error) {
      console.error('Error restoring from background:', error);
    }
  }

  private performMemoryCleanup(): void {
    try {
      // Clean up old movement state data
      const now = Date.now();
      const oneHourAgo = now - 3600000; // 1 hour

      if (this.userMovementState.lastMovementTime < oneHourAgo) {
        // Reset movement state if no activity for 1 hour
        this.userMovementState.isMoving = false;
      }

      // Force garbage collection if available (development only)
      if (__DEV__ && global.gc) {
        global.gc();
        console.log('🧹 Memory cleanup performed');
      }
    } catch (error) {
      console.error('Error during memory cleanup:', error);
    }
  }

  private async performAggressiveCleanup(): Promise<void> {
    console.log('🧹 Performing aggressive cleanup after extended background time');

    try {
      // Stop location tracking if app has been in background for too long
      const timeSinceBackground = Date.now() - this.lastActivityTime;
      const maxBackgroundTime = 1800000; // 30 minutes

      if (timeSinceBackground > maxBackgroundTime) {
        console.log('⏰ App in background too long, stopping location tracking');
        await this.stopBackgroundLocationTracking();
        this.isTracking = false;
      }

      // Clear old cached data
      this.userMovementState = {
        isAtHome: false,
        isAtWork: false,
        isMoving: false,
        lastMovementTime: Date.now(),
      };

    } catch (error) {
      console.error('Error during aggressive cleanup:', error);
    }
  }

  private async registerBackgroundTask(): Promise<void> {
    TaskManager.defineTask(BACKGROUND_LOCATION_TASK, async ({ data, error }) => {
      if (error) {
        console.error('Background location task error:', error);
        return;
      }

      if (data) {
        const { locations } = data as any;
        await this.handleBackgroundLocationUpdate(locations[0]);
      }
    });

    // Register background task
    TaskManager.defineTask('background-location-check', async () => {
      try {
        await this.performLocationCheck();
        return { finished: true };
      } catch (error) {
        console.error('Background task error:', error);
        return { finished: false };
      }
    });
  }

  async requestBackgroundPermissions(): Promise<boolean> {
    try {
      // Request foreground location permission first
      const { status: foregroundStatus } = await Location.requestForegroundPermissionsAsync();
      if (foregroundStatus !== 'granted') {
        return false;
      }

      // Request background location permission
      const { status: backgroundStatus } = await Location.requestBackgroundPermissionsAsync();
      return backgroundStatus === 'granted';
    } catch (error) {
      console.error('Error requesting background permissions:', error);
      return false;
    }
  }

  async startBackgroundLocationTracking(forceStart: boolean = false, backgroundOptimized: boolean = false): Promise<boolean> {
    try {
      const settings = await this.getLocationSettings();
      if (!forceStart && !settings.backgroundEnabled) {
        console.log('⚠️ Background tracking not enabled in settings');
        return false;
      }

      const hasPermission = await this.requestBackgroundPermissions();
      if (!hasPermission) {
        console.log('⚠️ Background location permissions not granted');
        return false;
      }

      // Configure location options based on optimization level
      const locationOptions = backgroundOptimized ? {
        accuracy: Location.Accuracy.Low, // More aggressive battery optimization
        timeInterval: LOCATION_CHECK_INTERVAL * 2, // Double the interval
        distanceInterval: 100, // Only update if moved 100 meters
        deferredUpdatesInterval: 300000, // Batch updates for 5 minutes
        foregroundService: {
          notificationTitle: 'Evelin Location Helper',
          notificationBody: 'Background location tracking (optimized)',
          notificationColor: '#3b82f6',
        },
        pausesLocationUpdatesAutomatically: true,
        showsBackgroundLocationIndicator: false,
      } : {
        accuracy: Location.Accuracy.Balanced, // Normal battery optimization
        timeInterval: LOCATION_CHECK_INTERVAL,
        distanceInterval: 50, // Only update if moved 50 meters
        deferredUpdatesInterval: 60000, // Batch updates for 1 minute
        foregroundService: {
          notificationTitle: 'Evelin Location Helper',
          notificationBody: 'Tracking location for task reminders',
          notificationColor: '#3b82f6',
        },
        pausesLocationUpdatesAutomatically: true,
        showsBackgroundLocationIndicator: false,
      };

      // Start background location updates
      await Location.startLocationUpdatesAsync(BACKGROUND_LOCATION_TASK, locationOptions);

      this.isTracking = true;
      console.log(`✅ Background location tracking started ${backgroundOptimized ? '(background optimized)' : '(normal)'}`);
      return true;
    } catch (error) {
      console.error('❌ Failed to start background location tracking:', error);
      return false;
    }
  }

  async stopBackgroundLocationTracking(): Promise<void> {
    try {
      const isRegistered = await TaskManager.isTaskRegisteredAsync(BACKGROUND_LOCATION_TASK);
      if (isRegistered) {
        await Location.stopLocationUpdatesAsync(BACKGROUND_LOCATION_TASK);
      }
      this.isTracking = false;
      console.log('✅ Background location tracking stopped');
    } catch (error) {
      console.error('❌ Failed to stop background location tracking:', error);
    }
  }

  private async handleBackgroundLocationUpdate(location: Location.LocationObject): Promise<void> {
    try {
      const settings = await this.getLocationSettings();
      
      // Check if we should process this location update
      if (!this.shouldProcessLocation(location, settings)) {
        return;
      }

      // Update movement state
      await this.updateMovementState(location, settings);

      // Only check for tasks if user is moving or away from home/work
      if (this.userMovementState.isMoving || 
          (!this.userMovementState.isAtHome && !this.userMovementState.isAtWork)) {
        
        await this.checkNearbyTasks(location);
      }

    } catch (error) {
      console.error('Error handling background location update:', error);
    }
  }

  private shouldProcessLocation(location: Location.LocationObject, settings: LocationSettings): boolean {
    // Check quiet hours
    if (settings.quietHours.enabled && this.isInQuietHours(settings.quietHours)) {
      return false;
    }

    // Check if location is accurate enough
    if (location.coords.accuracy && location.coords.accuracy > 100) {
      return false;
    }

    return true;
  }

  private async updateMovementState(location: Location.LocationObject, settings: LocationSettings): Promise<void> {
    const { latitude, longitude } = location.coords;
    
    // Check if at home
    if (settings.homeLocation) {
      const distanceToHome = this.calculateDistance(
        latitude, longitude,
        settings.homeLocation.latitude, settings.homeLocation.longitude
      );
      this.userMovementState.isAtHome = distanceToHome <= HOME_WORK_RADIUS;
    }

    // Check if at work
    if (settings.workLocation) {
      const distanceToWork = this.calculateDistance(
        latitude, longitude,
        settings.workLocation.latitude, settings.workLocation.longitude
      );
      this.userMovementState.isAtWork = distanceToWork <= HOME_WORK_RADIUS;
    }

    // Check if moving
    if (this.userMovementState.lastKnownLocation) {
      const distanceMoved = this.calculateDistance(
        latitude, longitude,
        this.userMovementState.lastKnownLocation.coords.latitude,
        this.userMovementState.lastKnownLocation.coords.longitude
      );
      
      if (distanceMoved > 50) { // Moved more than 50 meters
        this.userMovementState.isMoving = true;
        this.userMovementState.lastMovementTime = Date.now();
      } else if (Date.now() - this.userMovementState.lastMovementTime > 300000) { // 5 minutes stationary
        this.userMovementState.isMoving = false;
      }
    }

    this.userMovementState.lastKnownLocation = location;
  }

  private async checkNearbyTasks(location: Location.LocationObject): Promise<void> {
    try {
      const tasks = await mobileTaskService.getTasks();
      const activeTasks = tasks.filter((task: Task) =>
        !task.completed &&
        !task.notificationTriggered &&
        !task.id.startsWith('temp-') // Skip temporary IDs from optimistic updates
      );

      for (const task of activeTasks) {
        if (task.coordinates) {
          const distance = this.calculateDistance(
            location.coords.latitude,
            location.coords.longitude,
            task.coordinates.lat,
            task.coordinates.lng
          );

          if (distance <= (task.notificationDistance || 100)) {
            // Send notification
            await mobileNotificationService.showLocationNotification(
              task,
              Math.round(distance)
            );

            // Mark as notified
            await mobileTaskService.updateTask(task.id, { notificationTriggered: true });
            
            console.log(`📍 Background notification sent for task: ${task.text}`);
          }
        }
      }
    } catch (error) {
      console.error('Error checking nearby tasks:', error);
    }
  }

  private async performLocationCheck(): Promise<void> {
    try {
      const settings = await this.getLocationSettings();
      if (!settings.backgroundEnabled) return;

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
      });

      await this.handleBackgroundLocationUpdate(location);
    } catch (error) {
      console.error('Error performing location check:', error);
    }
  }

  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = lat1 * Math.PI/180;
    const φ2 = lat2 * Math.PI/180;
    const Δφ = (lat2-lat1) * Math.PI/180;
    const Δλ = (lon2-lon1) * Math.PI/180;

    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));

    return R * c;
  }

  private isInQuietHours(quietHours: { start: string; end: string }): boolean {
    const now = new Date();
    const currentTime = now.getHours() * 60 + now.getMinutes();
    
    const [startHour, startMin] = quietHours.start.split(':').map(Number);
    const [endHour, endMin] = quietHours.end.split(':').map(Number);
    
    const startTime = startHour * 60 + startMin;
    const endTime = endHour * 60 + endMin;

    if (startTime <= endTime) {
      return currentTime >= startTime && currentTime <= endTime;
    } else {
      return currentTime >= startTime || currentTime <= endTime;
    }
  }

  async getLocationSettings(): Promise<LocationSettings> {
    try {
      const settings = await AsyncStorage.getItem('locationSettings');
      return settings ? JSON.parse(settings) : {
        backgroundEnabled: false,
        batteryOptimization: true,
        onlyWhenMoving: true,
        quietHours: { start: '22:00', end: '07:00', enabled: true },
      };
    } catch (error) {
      console.error('Error getting location settings:', error);
      return {
        backgroundEnabled: false,
        batteryOptimization: true,
        onlyWhenMoving: true,
        quietHours: { start: '22:00', end: '07:00', enabled: true },
      };
    }
  }

  async updateLocationSettings(settings: Partial<LocationSettings>): Promise<void> {
    try {
      const currentSettings = await this.getLocationSettings();
      const newSettings = { ...currentSettings, ...settings };
      await AsyncStorage.setItem('locationSettings', JSON.stringify(newSettings));
      
      // Restart background tracking if settings changed
      if (settings.backgroundEnabled !== undefined) {
        if (settings.backgroundEnabled) {
          await this.startBackgroundLocationTracking();
        } else {
          await this.stopBackgroundLocationTracking();
        }
      }
    } catch (error) {
      console.error('Error updating location settings:', error);
    }
  }

  getUserMovementState(): UserMovementState {
    return this.userMovementState;
  }

  async cleanup(): Promise<void> {
    console.log('🧹 Cleaning up background location service');

    try {
      // Stop location tracking
      await this.stopBackgroundLocationTracking();

      // Clear timers
      if (this.cleanupTimer) {
        clearTimeout(this.cleanupTimer);
        this.cleanupTimer = null;
      }

      if (this.memoryCleanupInterval) {
        clearInterval(this.memoryCleanupInterval);
        this.memoryCleanupInterval = null;
      }

      // Remove app state listener
      if (this.appStateSubscription) {
        this.appStateSubscription.remove();
        this.appStateSubscription = null;
      }

      // Unregister background tasks
      try {
        const isLocationTaskRegistered = await TaskManager.isTaskRegisteredAsync(BACKGROUND_LOCATION_TASK);
        if (isLocationTaskRegistered) {
          await TaskManager.unregisterTaskAsync(BACKGROUND_LOCATION_TASK);
        }

        const isFetchTaskRegistered = await TaskManager.isTaskRegisteredAsync('background-location-check');
        if (isFetchTaskRegistered) {
          await BackgroundTask.unregisterTaskAsync('background-location-check');
        }
      } catch (error) {
        console.warn('Warning: Could not unregister background tasks:', error);
      }

      // Reset state
      this.isInitialized = false;
      this.isTracking = false;
      this.userMovementState = {
        isAtHome: false,
        isAtWork: false,
        isMoving: false,
        lastMovementTime: Date.now(),
      };

      console.log('✅ Background location service cleanup completed');
    } catch (error) {
      console.error('❌ Error during background location service cleanup:', error);
    }
  }

  // Method to check if service needs cleanup (for external monitoring)
  shouldPerformCleanup(): boolean {
    const timeSinceLastActivity = Date.now() - this.lastActivityTime;
    const maxInactiveTime = 3600000; // 1 hour

    return timeSinceLastActivity > maxInactiveTime;
  }

  // Method to get service status for debugging
  getServiceStatus(): {
    isInitialized: boolean;
    isTracking: boolean;
    lastActivityTime: number;
    timeSinceLastActivity: number;
    hasCleanupTimer: boolean;
    hasMemoryCleanupInterval: boolean;
    hasAppStateSubscription: boolean;
  } {
    return {
      isInitialized: this.isInitialized,
      isTracking: this.isTracking,
      lastActivityTime: this.lastActivityTime,
      timeSinceLastActivity: Date.now() - this.lastActivityTime,
      hasCleanupTimer: this.cleanupTimer !== null,
      hasMemoryCleanupInterval: this.memoryCleanupInterval !== null,
      hasAppStateSubscription: this.appStateSubscription !== null,
    };
  }
}

export const backgroundLocationService = new BackgroundLocationService();
