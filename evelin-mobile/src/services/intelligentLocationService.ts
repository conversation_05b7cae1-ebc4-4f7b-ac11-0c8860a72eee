import { mobileLocationService } from './locationService';
import { mobileGoogleMapsService } from './googleMapsService';

interface LocationMatch {
  placeName: string;
  searchQuery: string;
  category: string;
  confidence: number;
}

interface PlaceResult {
  name: string;
  coordinates: { lat: number; lng: number };
  address: string;
  category: string;
  distance?: number;
}

// Comprehensive location database including Polish stores
const LOCATION_DATABASE: { [key: string]: { category: string; searchTerms: string[]; type: string; } } = {
  // Polish stores
  'mrówka': { category: 'hardware', searchTerms: ['mrówka', 'mrowka'], type: 'hardware store' },
  'psb': { category: 'hardware', searchTerms: ['psb', 'psb mrówka'], type: 'hardware store' },
  'castorama': { category: 'hardware', searchTerms: ['castorama'], type: 'hardware store' },
  'leroy merlin': { category: 'hardware', searchTerms: ['leroy merlin', 'leroy'], type: 'hardware store' },
  'obi': { category: 'hardware', searchTerms: ['obi'], type: 'hardware store' },
  
  'biedronka': { category: 'grocery', searchTerms: ['biedronka'], type: 'grocery store' },
  'żabka': { category: 'grocery', searchTerms: ['żabka', 'zabka'], type: 'convenience store' },
  'carrefour': { category: 'grocery', searchTerms: ['carrefour'], type: 'hypermarket' },
  'auchan': { category: 'grocery', searchTerms: ['auchan'], type: 'hypermarket' },
  'kaufland': { category: 'grocery', searchTerms: ['kaufland'], type: 'hypermarket' },
  'netto': { category: 'grocery', searchTerms: ['netto'], type: 'grocery store' },
  'polo market': { category: 'grocery', searchTerms: ['polo market', 'polo'], type: 'grocery store' },
  'intermarche': { category: 'grocery', searchTerms: ['intermarche'], type: 'grocery store' },
  
  // UK stores
  'lidl': { category: 'grocery', searchTerms: ['lidl'], type: 'grocery store' },
  'tesco': { category: 'grocery', searchTerms: ['tesco'], type: 'grocery store' },
  'asda': { category: 'grocery', searchTerms: ['asda'], type: 'grocery store' },
  'sainsburys': { category: 'grocery', searchTerms: ['sainsburys', 'sainsbury'], type: 'grocery store' },
  'morrisons': { category: 'grocery', searchTerms: ['morrisons'], type: 'grocery store' },
  'aldi': { category: 'grocery', searchTerms: ['aldi'], type: 'grocery store' },
  'waitrose': { category: 'grocery', searchTerms: ['waitrose'], type: 'grocery store' },
  'iceland': { category: 'grocery', searchTerms: ['iceland'], type: 'grocery store' },
  'co-op': { category: 'grocery', searchTerms: ['co-op', 'coop'], type: 'grocery store' },
  
  // Pharmacies
  'apteka': { category: 'pharmacy', searchTerms: ['apteka'], type: 'pharmacy' },
  'boots': { category: 'pharmacy', searchTerms: ['boots'], type: 'pharmacy' },
  'superdrug': { category: 'pharmacy', searchTerms: ['superdrug'], type: 'pharmacy' },
  
  // Banks
  'pko': { category: 'finance', searchTerms: ['pko', 'pko bp'], type: 'bank' },
  'millennium': { category: 'finance', searchTerms: ['millennium'], type: 'bank' },
  'ing': { category: 'finance', searchTerms: ['ing'], type: 'bank' },
  'santander': { category: 'finance', searchTerms: ['santander'], type: 'bank' },
  'barclays': { category: 'finance', searchTerms: ['barclays'], type: 'bank' },
  'lloyds': { category: 'finance', searchTerms: ['lloyds'], type: 'bank' },
  'hsbc': { category: 'finance', searchTerms: ['hsbc'], type: 'bank' },
  
  // Fuel stations
  'orlen': { category: 'fuel', searchTerms: ['orlen', 'pko orlen'], type: 'petrol station' },
  'bp': { category: 'fuel', searchTerms: ['bp'], type: 'petrol station' },
  'shell': { category: 'fuel', searchTerms: ['shell'], type: 'petrol station' },
  'esso': { category: 'fuel', searchTerms: ['esso'], type: 'petrol station' },
  
  // Restaurants/Food
  'mcdonalds': { category: 'food', searchTerms: ['mcdonald', 'mcdonalds', 'mac'], type: 'restaurant' },
  'kfc': { category: 'food', searchTerms: ['kfc'], type: 'restaurant' },
  'burger king': { category: 'food', searchTerms: ['burger king'], type: 'restaurant' },
  'subway': { category: 'food', searchTerms: ['subway'], type: 'restaurant' },
  'pizza hut': { category: 'food', searchTerms: ['pizza hut'], type: 'restaurant' },
  'starbucks': { category: 'food', searchTerms: ['starbucks'], type: 'cafe' },
  'costa': { category: 'food', searchTerms: ['costa'], type: 'cafe' },
};

// Product-to-store mapping for intelligent suggestions
const PRODUCT_STORE_MAPPING = {
  // Hardware/DIY items
  'lamp': ['mrówka', 'psb', 'castorama', 'leroy merlin', 'obi'],
  'lamps': ['mrówka', 'psb', 'castorama', 'leroy merlin', 'obi'],
  'light': ['mrówka', 'psb', 'castorama', 'leroy merlin', 'obi'],
  'bulb': ['mrówka', 'psb', 'castorama', 'leroy merlin', 'obi'],
  'tools': ['mrówka', 'psb', 'castorama', 'leroy merlin', 'obi'],
  'paint': ['mrówka', 'psb', 'castorama', 'leroy merlin', 'obi'],
  'screws': ['mrówka', 'psb', 'castorama', 'leroy merlin', 'obi'],
  'drill': ['mrówka', 'psb', 'castorama', 'leroy merlin', 'obi'],
  
  // Grocery items
  'milk': ['biedronka', 'lidl', 'tesco', 'carrefour', 'auchan'],
  'bread': ['biedronka', 'lidl', 'tesco', 'carrefour', 'żabka'],
  'eggs': ['biedronka', 'lidl', 'tesco', 'carrefour', 'auchan'],
  'meat': ['biedronka', 'lidl', 'tesco', 'carrefour', 'auchan'],
  'vegetables': ['biedronka', 'lidl', 'tesco', 'carrefour', 'auchan'],
  'fruit': ['biedronka', 'lidl', 'tesco', 'carrefour', 'auchan'],
  
  // Medicine/Health
  'medicine': ['apteka', 'boots', 'superdrug'],
  'pills': ['apteka', 'boots', 'superdrug'],
  'vitamins': ['apteka', 'boots', 'superdrug'],
  'prescription': ['apteka', 'boots', 'superdrug'],
};

class IntelligentLocationService {
  
  /**
   * Intelligently parse task text to extract location and product information
   */
  parseTaskText(taskText: string): LocationMatch[] {
    const text = taskText.toLowerCase();
    const matches: LocationMatch[] = [];
    
    // 1. Direct store name detection
    for (const [storeName, storeData] of Object.entries(LOCATION_DATABASE)) {
      for (const searchTerm of storeData.searchTerms) {
        if (text.includes(searchTerm.toLowerCase())) {
          matches.push({
            placeName: storeName,
            searchQuery: searchTerm,
            category: storeData.category,
            confidence: 0.9
          });
        }
      }
    }
    
    // 2. Product-based store suggestions
    for (const [product, suggestedStores] of Object.entries(PRODUCT_STORE_MAPPING)) {
      if (text.includes(product)) {
        for (const storeName of suggestedStores) {
          const storeData = LOCATION_DATABASE[storeName];
          if (storeData && !matches.find(m => m.placeName === storeName)) {
            matches.push({
              placeName: storeName,
              searchQuery: storeData.searchTerms[0],
              category: storeData.category,
              confidence: 0.7
            });
          }
        }
      }
    }
    
    // 3. Category-based detection
    if (matches.length === 0) {
      if (text.includes('shop') || text.includes('buy') || text.includes('get')) {
        // Default to grocery stores for general shopping
        const groceryStores = ['biedronka', 'lidl', 'tesco'];
        for (const storeName of groceryStores) {
          const storeData = LOCATION_DATABASE[storeName];
          matches.push({
            placeName: storeName,
            searchQuery: storeData.searchTerms[0],
            category: storeData.category,
            confidence: 0.5
          });
        }
      }
    }
    
    // Sort by confidence
    return matches.sort((a, b) => b.confidence - a.confidence);
  }
  
  /**
   * Search for actual places using Google Maps API
   */
  async findNearbyPlaces(locationMatches: LocationMatch[], userLocation: { latitude: number; longitude: number }, radius: number = 500): Promise<PlaceResult[]> {
    const results: PlaceResult[] = [];
    let lastError: Error | null = null;

    for (const match of locationMatches.slice(0, 2)) { // Limit to top 2 matches for performance
      try {
        console.log(`🔍 Searching Google Maps for: ${match.searchQuery}`);

        // Use real Google Maps search
        const googleResults = await mobileGoogleMapsService.searchNearbyPlaces(
          match.searchQuery,
          userLocation,
          radius
        );

        // Convert Google Maps results to our format
        const convertedResults = googleResults.slice(0, 3).map(place => ({
          name: place.name,
          coordinates: place.coordinates,
          address: place.address,
          category: match.category,
          distance: place.distance
        }));

        results.push(...convertedResults);

        console.log(`✅ Found ${convertedResults.length} ${match.placeName} locations`);

      } catch (error) {
        console.error(`Error searching for ${match.placeName}:`, error);
        lastError = error instanceof Error ? error : new Error(`Failed to search for ${match.placeName}`);
      }
    }

    // Only throw error if it's a real API failure, not just no results
    if (results.length === 0 && lastError && !lastError.message.includes('ZERO_RESULTS')) {
      throw lastError;
    }

    return results.sort((a, b) => (a.distance || 0) - (b.distance || 0));
  }
  

  
  /**
   * Main function to get intelligent location suggestions with custom radius
   */
  async getLocationSuggestions(taskText: string, radius: number = 500): Promise<PlaceResult[]> {
    // Get current user location
    const userLocation = await mobileLocationService.getCurrentLocation();
    if (!userLocation) {
      throw new Error('Could not get user location. Please enable location services and try again.');
    }

    // Parse task text for location clues
    const locationMatches = this.parseTaskText(taskText);
    console.log('🧠 Intelligent location matches:', locationMatches);

    if (locationMatches.length === 0) {
      console.log('No location matches found for:', taskText);
      return [];
    }

    // Find actual places nearby using Google Maps - let errors propagate
    const places = await this.findNearbyPlaces(locationMatches, userLocation, radius);
    console.log('📍 Found nearby places:', places);

    return places;
  }

  /**
   * Find a specific place by name (e.g., "Lidl") within radius
   */
  async findSpecificPlace(placeName: string, radius: number = 500): Promise<PlaceResult | null> {
    // Get current user location
    const userLocation = await mobileLocationService.getCurrentLocation();
    if (!userLocation) {
      throw new Error('Could not get user location. Please enable location services and try again.');
    }

    console.log(`🎯 Finding specific place: ${placeName} within ${radius}m`);

    // Use Google Maps to find the specific place - let errors propagate
    const result = await mobileGoogleMapsService.findSpecificPlace(placeName, userLocation, radius);

    if (result) {
      console.log(`✅ Found ${placeName}: ${result.name} at ${result.address} (${result.distance}m)`);
      return {
        name: result.name,
        coordinates: result.coordinates,
        address: result.address,
        category: 'store',
        distance: result.distance
      };
    }

    console.log(`❌ No ${placeName} found within ${radius}m`);
    return null;
  }
}

export const intelligentLocationService = new IntelligentLocationService();
