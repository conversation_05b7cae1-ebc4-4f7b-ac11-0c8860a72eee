import * as Location from 'expo-location';
import { Task, Coordinates } from '../types/task';

export class MobileLocationService {
  private coordinatesCache = new Map<string, Coordinates>();

  // Request location permissions
  async requestLocationPermission(): Promise<'granted' | 'denied' | 'undetermined'> {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      return status as 'granted' | 'denied' | 'undetermined';
    } catch (error) {
      console.error('Error requesting location permission:', error);
      return 'denied';
    }
  }

  // Request background location permissions
  async requestBackgroundLocationPermission(): Promise<'granted' | 'denied' | 'undetermined'> {
    try {
      // First ensure we have foreground permission
      const foregroundStatus = await this.requestLocationPermission();
      if (foregroundStatus !== 'granted') {
        return foregroundStatus;
      }

      // Request background permission
      const { status } = await Location.requestBackgroundPermissionsAsync();
      return status as 'granted' | 'denied' | 'undetermined';
    } catch (error) {
      console.error('Error requesting background location permission:', error);
      return 'denied';
    }
  }

  // Check if location permissions are granted
  async hasLocationPermission(): Promise<boolean> {
    try {
      const { status } = await Location.getForegroundPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Error checking location permission:', error);
      return false;
    }
  }

  // Check if background location permissions are granted
  async hasBackgroundLocationPermission(): Promise<boolean> {
    try {
      const { status } = await Location.getBackgroundPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Error checking background location permission:', error);
      return false;
    }
  }

  // Get current location
  async getCurrentLocation(): Promise<{ latitude: number; longitude: number } | null> {
    try {
      const { status } = await Location.getForegroundPermissionsAsync();
      if (status !== 'granted') {
        console.log('Location permission not granted');
        return null;
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });

      return {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      };
    } catch (error) {
      console.error('Error getting current location:', error);
      return null;
    }
  }

  // Watch location changes
  async watchLocation(callback: (location: { latitude: number; longitude: number }) => void): Promise<Location.LocationSubscription | null> {
    try {
      const { status } = await Location.getForegroundPermissionsAsync();
      if (status !== 'granted') {
        console.log('Location permission not granted');
        return null;
      }

      return await Location.watchPositionAsync(
        {
          accuracy: Location.Accuracy.High,
          timeInterval: 10000, // Update every 10 seconds
          distanceInterval: 10, // Update every 10 meters
        },
        (location) => {
          callback({
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
          });
        }
      );
    } catch (error) {
      console.error('Error watching location:', error);
      return null;
    }
  }

  // Geocode location using Google Maps API (if available) or fallback
  async resolveTaskLocation(
    task: Task, 
    userLocation?: { latitude: number; longitude: number }
  ): Promise<{ coordinates: Coordinates; placeDetails?: any } | null> {
    if (!task.location) return null;

    // Check cache first
    const cacheKey = `${task.location}_${userLocation?.latitude}_${userLocation?.longitude}`;
    if (this.coordinatesCache.has(cacheKey)) {
      return { coordinates: this.coordinatesCache.get(cacheKey)! };
    }

    let coordinates: Coordinates | null = null;
    let placeDetails: any = null;

    // Try Google Places API if available
    if (userLocation) {
      try {
        coordinates = await this.searchNearbyPlaces(task.location, userLocation);
        if (coordinates) {
          placeDetails = {
            name: task.location,
            address: 'Near your location'
          };
        }
      } catch (error) {
        console.error('Error with location search:', error);
      }
    }

    // Fallback to simulated coordinates
    if (!coordinates && userLocation) {
      coordinates = this.simulateNearbyLocation(task.location, userLocation);
    }

    // Final fallback to default coordinates
    if (!coordinates) {
      coordinates = this.getDefaultCoordinates(task.location);
    }

    // Cache the result
    if (coordinates) {
      this.coordinatesCache.set(cacheKey, coordinates);
    }

    return coordinates ? { coordinates, placeDetails } : null;
  }

  // Search for nearby places (simplified version)
  private async searchNearbyPlaces(
    query: string,
    userLocation: { latitude: number; longitude: number }
  ): Promise<Coordinates | null> {
    // For now, simulate nearby locations
    // In a full implementation, you would use Google Places API
    return this.simulateNearbyLocation(query, userLocation);
  }

  // Simulate nearby locations based on query
  private simulateNearbyLocation(
    query: string,
    userLocation: { latitude: number; longitude: number }
  ): Coordinates | null {
    const queryLower = query.toLowerCase();
    
    // Generate a random nearby location within 1km
    const latOffset = (Math.random() - 0.5) * 0.01; // ~1km
    const lngOffset = (Math.random() - 0.5) * 0.01; // ~1km
    
    return {
      lat: userLocation.latitude + latOffset,
      lng: userLocation.longitude + lngOffset
    };
  }

  // Get default coordinates for common locations
  private getDefaultCoordinates(location: string): Coordinates | null {
    const locationLower = location.toLowerCase();
    
    // Default to London area coordinates
    const defaultCoords = {
      lat: 51.5074,
      lng: -0.1278
    };

    if (locationLower.includes('lidl')) {
      return { lat: 51.5074 + Math.random() * 0.01, lng: -0.1278 + Math.random() * 0.01 };
    }
    
    if (locationLower.includes('tesco')) {
      return { lat: 51.5074 + Math.random() * 0.01, lng: -0.1278 + Math.random() * 0.01 };
    }
    
    if (locationLower.includes('bank') || locationLower.includes('atm')) {
      return { lat: 51.5074 + Math.random() * 0.01, lng: -0.1278 + Math.random() * 0.01 };
    }

    return defaultCoords;
  }

  // Calculate distance between two points
  calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = lat1 * Math.PI / 180;
    const φ2 = lat2 * Math.PI / 180;
    const Δφ = (lat2 - lat1) * Math.PI / 180;
    const Δλ = (lon2 - lon1) * Math.PI / 180;

    const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c; // Distance in meters
  }
}

export const mobileLocationService = new MobileLocationService();
