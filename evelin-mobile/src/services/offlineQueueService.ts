import AsyncStorage from '@react-native-async-storage/async-storage';
import { EventEmitter } from '../utils/EventEmitter';
import { ConflictResolutionStrategy } from './conflictResolutionService';
import { networkService } from './networkService';

export interface QueuedOperation {
  id: string;
  type: 'CREATE_TASK' | 'UPDATE_TASK' | 'DELETE_TASK' | 'BULK_UPDATE' | 'SYNC_SETTINGS' | 'UPLOAD_FILE';
  payload: any;
  timestamp: string;
  retryCount: number;
  maxRetries: number;
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  error?: string;
  dependencies?: string[]; // IDs of operations this depends on
  version: number; // Operation version for conflict resolution
  checksum: string; // Data integrity verification
  atomicGroup?: string; // Group ID for atomic operations
  rollbackData?: any; // Data needed for rollback
  metadata?: {
    userId?: string;
    deviceId?: string;
    appVersion?: string;
    conflictResolution?: ConflictResolutionStrategy;
    originalTimestamp?: string; // Original creation time before retries
    networkCondition?: 'online' | 'offline' | 'poor';
    dataSize?: number; // Size of payload for optimization
    tags?: string[]; // Tags for categorization and filtering
  };
}

export interface QueueStats {
  totalOperations: number;
  pendingOperations: number;
  failedOperations: number;
  completedOperations: number;
  processingOperations: number;
  cancelledOperations: number;
  oldestPendingOperation?: string;
  averageProcessingTime: number;
  totalDataSize: number;
  atomicGroups: number;
  conflictedOperations: number;
  lastProcessedOperation?: string;
  queueHealth: 'healthy' | 'degraded' | 'critical';
}

class OfflineQueueService extends EventEmitter {
  private queue: QueuedOperation[] = [];
  private isProcessing = false;
  private processingInterval: NodeJS.Timeout | null = null;
  private readonly STORAGE_KEY = 'offline_queue';
  private readonly STATS_KEY = 'offline_queue_stats';
  private readonly ATOMIC_GROUPS_KEY = 'atomic_groups';
  private readonly ROLLBACK_LOG_KEY = 'rollback_log';
  private atomicGroups: Map<string, string[]> = new Map(); // groupId -> operationIds
  private rollbackLog: Array<{id: string, timestamp: string, data: any}> = [];
  private stats: QueueStats = {
    totalOperations: 0,
    pendingOperations: 0,
    failedOperations: 0,
    completedOperations: 0,
    processingOperations: 0,
    cancelledOperations: 0,
    averageProcessingTime: 0,
    totalDataSize: 0,
    atomicGroups: 0,
    conflictedOperations: 0,
    queueHealth: 'healthy',
  };

  async initialize(): Promise<void> {
    try {
      // Load queue from storage
      await this.loadQueue();
      await this.loadStats();
      await this.loadAtomicGroups();
      await this.loadRollbackLog();

      // Validate queue integrity
      await this.validateQueueIntegrity();

      // Listen for network changes
      networkService.on('connectionRestored', () => {
        console.log('🔄 Network restored, processing offline queue...');
        this.processQueue();
      });

      // Start periodic processing
      this.startPeriodicProcessing();

      // Start periodic health check
      this.startHealthMonitoring();

      console.log('✅ Offline queue service initialized');
      console.log(`📊 Queue stats: ${this.stats.pendingOperations} pending, ${this.stats.failedOperations} failed, ${this.stats.atomicGroups} atomic groups`);
    } catch (error) {
      console.error('❌ Failed to initialize offline queue service:', error);
    }
  }

  // Add operation to queue
  async enqueue(operation: Omit<QueuedOperation, 'id' | 'timestamp' | 'retryCount' | 'status' | 'version' | 'checksum'>): Promise<string> {
    const operationId = this.generateId();
    const timestamp = new Date().toISOString();
    const checksum = this.generateChecksum(operation.payload);
    const dataSize = this.calculateDataSize(operation.payload);

    const queuedOperation: QueuedOperation = {
      ...operation,
      id: operationId,
      timestamp,
      retryCount: 0,
      status: 'pending',
      version: 1,
      checksum,
      metadata: {
        ...operation.metadata,
        originalTimestamp: timestamp,
        networkCondition: networkService.isOnline() ? 'online' : 'offline',
        dataSize,
      },
    };

    // Handle atomic operations
    if (operation.atomicGroup) {
      await this.addToAtomicGroup(operation.atomicGroup, operationId);
    }

    this.queue.push(queuedOperation);
    this.stats.totalOperations++;
    this.stats.pendingOperations++;
    this.stats.totalDataSize += dataSize;

    await this.saveQueue();
    await this.saveStats();
    if (operation.atomicGroup) {
      await this.saveAtomicGroups();
    }

    console.log(`📝 Queued operation: ${operation.type} (${operationId}) ${operation.atomicGroup ? `[Atomic: ${operation.atomicGroup}]` : ''}`);
    this.emit('operationQueued', queuedOperation);

    // Try to process immediately if online
    if (networkService.isOnline()) {
      this.processQueue();
    }

    return operationId;
  }

  // Remove operation from queue
  async dequeue(operationId: string): Promise<boolean> {
    const index = this.queue.findIndex(op => op.id === operationId);
    if (index === -1) return false;

    const operation = this.queue[index];
    this.queue.splice(index, 1);

    // Update stats
    if (operation.status === 'pending') this.stats.pendingOperations--;
    else if (operation.status === 'failed') this.stats.failedOperations--;
    else if (operation.status === 'processing') this.stats.processingOperations--;

    await this.saveQueue();
    await this.saveStats();

    this.emit('operationDequeued', operation);
    return true;
  }

  // Process the queue
  async processQueue(): Promise<void> {
    if (this.isProcessing || networkService.isOffline()) {
      return;
    }

    this.isProcessing = true;
    console.log('🔄 Processing offline queue...');

    try {
      // Sort by priority and timestamp
      const sortedQueue = this.getSortedQueue();
      
      for (const operation of sortedQueue) {
        if (operation.status !== 'pending') continue;

        // Check dependencies
        if (operation.dependencies && !this.areDependenciesMet(operation.dependencies)) {
          continue;
        }

        await this.processOperation(operation);
        
        // Small delay between operations to avoid overwhelming the server
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    } catch (error) {
      console.error('Error processing queue:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  private async processOperation(operation: QueuedOperation): Promise<void> {
    const startTime = Date.now();
    
    try {
      operation.status = 'processing';
      this.stats.processingOperations++;
      this.stats.pendingOperations--;
      
      await this.saveQueue();
      this.emit('operationProcessing', operation);

      // Process based on operation type
      let success = false;
      switch (operation.type) {
        case 'CREATE_TASK':
          success = await this.processCreateTask(operation);
          break;
        case 'UPDATE_TASK':
          success = await this.processUpdateTask(operation);
          break;
        case 'DELETE_TASK':
          success = await this.processDeleteTask(operation);
          break;
        case 'BULK_UPDATE':
          success = await this.processBulkUpdate(operation);
          break;
        case 'SYNC_SETTINGS':
          success = await this.processSyncSettings(operation);
          break;
        default:
          console.warn(`Unknown operation type: ${operation.type}`);
          success = false;
      }

      const processingTime = Date.now() - startTime;
      this.updateAverageProcessingTime(processingTime);

      if (success) {
        operation.status = 'completed';
        this.stats.processingOperations--;
        this.stats.completedOperations++;
        
        console.log(`✅ Operation completed: ${operation.type} (${operation.id})`);
        this.emit('operationCompleted', operation);
        
        // Remove completed operation after a delay
        setTimeout(() => this.dequeue(operation.id), 5000);
      } else {
        throw new Error('Operation failed');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      operation.retryCount++;
      operation.error = errorMessage;
      this.stats.processingOperations--;

      if (operation.retryCount >= operation.maxRetries) {
        operation.status = 'failed';
        this.stats.failedOperations++;
        console.error(`❌ Operation failed permanently: ${operation.type} (${operation.id}) - ${errorMessage}`);
        this.emit('operationFailed', operation);
      } else {
        operation.status = 'pending';
        this.stats.pendingOperations++;
        console.warn(`⚠️ Operation failed, will retry: ${operation.type} (${operation.id}) - ${errorMessage}`);
        this.emit('operationRetry', operation);
      }
    }

    await this.saveQueue();
    await this.saveStats();
  }

  // Operation processors
  private async processCreateTask(operation: QueuedOperation): Promise<boolean> {
    try {
      const { mobileTaskService } = await import('./taskService');
      const result = await mobileTaskService.createTask(operation.payload);
      return !!result;
    } catch (error) {
      console.error('Failed to create task:', error);
      return false;
    }
  }

  private async processUpdateTask(operation: QueuedOperation): Promise<boolean> {
    try {
      const { mobileTaskService } = await import('./taskService');
      const { id, updates } = operation.payload;
      const result = await mobileTaskService.updateTask(id, updates);
      return !!result;
    } catch (error) {
      console.error('Failed to update task:', error);
      return false;
    }
  }

  private async processDeleteTask(operation: QueuedOperation): Promise<boolean> {
    try {
      const { mobileTaskService } = await import('./taskService');
      const result = await mobileTaskService.deleteTask(operation.payload.id);
      return result;
    } catch (error) {
      console.error('Failed to delete task:', error);
      return false;
    }
  }

  private async processBulkUpdate(operation: QueuedOperation): Promise<boolean> {
    try {
      const { mobileTaskService } = await import('./taskService');
      const { updates } = operation.payload;
      
      // Process each update individually
      for (const update of updates) {
        const result = await mobileTaskService.updateTask(update.id, update.updates);
        if (!result) return false;
      }
      
      return true;
    } catch (error) {
      console.error('Failed to process bulk update:', error);
      return false;
    }
  }

  private async processSyncSettings(operation: QueuedOperation): Promise<boolean> {
    try {
      // Implement settings sync logic here
      console.log('Syncing settings:', operation.payload);
      return true;
    } catch (error) {
      console.error('Failed to sync settings:', error);
      return false;
    }
  }

  // Helper methods
  private getSortedQueue(): QueuedOperation[] {
    const priorityOrder = { critical: 0, high: 1, medium: 2, low: 3 };
    
    return [...this.queue].sort((a, b) => {
      // First sort by priority
      const priorityDiff = priorityOrder[a.priority] - priorityOrder[b.priority];
      if (priorityDiff !== 0) return priorityDiff;
      
      // Then by timestamp (older first)
      return new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime();
    });
  }

  private areDependenciesMet(dependencies: string[]): boolean {
    return dependencies.every(depId => {
      const dep = this.queue.find(op => op.id === depId);
      return !dep || dep.status === 'completed';
    });
  }

  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private generateChecksum(data: any): string {
    const str = JSON.stringify(data);
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(36);
  }

  private calculateDataSize(data: any): number {
    return new Blob([JSON.stringify(data)]).size;
  }

  private async addToAtomicGroup(groupId: string, operationId: string): Promise<void> {
    if (!this.atomicGroups.has(groupId)) {
      this.atomicGroups.set(groupId, []);
      this.stats.atomicGroups++;
    }
    this.atomicGroups.get(groupId)!.push(operationId);
  }

  private async validateQueueIntegrity(): Promise<void> {
    let corruptedOperations = 0;

    for (const operation of this.queue) {
      // Validate checksum
      const expectedChecksum = this.generateChecksum(operation.payload);
      if (operation.checksum !== expectedChecksum) {
        console.warn(`⚠️ Checksum mismatch for operation ${operation.id}`);
        corruptedOperations++;
      }

      // Validate dependencies
      if (operation.dependencies) {
        for (const depId of operation.dependencies) {
          const dependency = this.queue.find(op => op.id === depId);
          if (!dependency) {
            console.warn(`⚠️ Missing dependency ${depId} for operation ${operation.id}`);
          }
        }
      }
    }

    if (corruptedOperations > 0) {
      console.warn(`⚠️ Found ${corruptedOperations} corrupted operations in queue`);
      this.stats.queueHealth = corruptedOperations > this.queue.length * 0.1 ? 'critical' : 'degraded';
    }
  }

  private startHealthMonitoring(): void {
    setInterval(() => {
      this.updateQueueHealth();
    }, 60000); // Check every minute
  }

  private updateQueueHealth(): void {
    const totalOps = this.stats.totalOperations;
    const failedRatio = totalOps > 0 ? this.stats.failedOperations / totalOps : 0;
    const oldestPending = this.getOldestPendingAge();

    if (failedRatio > 0.3 || oldestPending > 24 * 60 * 60 * 1000) { // 24 hours
      this.stats.queueHealth = 'critical';
    } else if (failedRatio > 0.1 || oldestPending > 6 * 60 * 60 * 1000) { // 6 hours
      this.stats.queueHealth = 'degraded';
    } else {
      this.stats.queueHealth = 'healthy';
    }
  }

  private getOldestPendingAge(): number {
    const pendingOps = this.queue.filter(op => op.status === 'pending');
    if (pendingOps.length === 0) return 0;

    const oldest = pendingOps.reduce((oldest, current) =>
      new Date(current.timestamp) < new Date(oldest.timestamp) ? current : oldest
    );

    return Date.now() - new Date(oldest.timestamp).getTime();
  }

  private updateAverageProcessingTime(newTime: number): void {
    const totalCompleted = this.stats.completedOperations;
    if (totalCompleted === 0) {
      this.stats.averageProcessingTime = newTime;
    } else {
      this.stats.averageProcessingTime = 
        (this.stats.averageProcessingTime * (totalCompleted - 1) + newTime) / totalCompleted;
    }
  }

  private startPeriodicProcessing(): void {
    // Process queue every 30 seconds
    this.processingInterval = setInterval(() => {
      if (networkService.isOnline() && this.stats.pendingOperations > 0) {
        this.processQueue();
      }
    }, 30000);
  }

  // Storage methods
  private async saveQueue(): Promise<void> {
    try {
      await AsyncStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.queue));
    } catch (error) {
      console.error('Failed to save queue:', error);
    }
  }

  private async loadQueue(): Promise<void> {
    try {
      const data = await AsyncStorage.getItem(this.STORAGE_KEY);
      if (data) {
        this.queue = JSON.parse(data);
      }
    } catch (error) {
      console.error('Failed to load queue:', error);
      this.queue = [];
    }
  }

  private async saveStats(): Promise<void> {
    try {
      await AsyncStorage.setItem(this.STATS_KEY, JSON.stringify(this.stats));
    } catch (error) {
      console.error('Failed to save stats:', error);
    }
  }

  private async loadStats(): Promise<void> {
    try {
      const data = await AsyncStorage.getItem(this.STATS_KEY);
      if (data) {
        this.stats = { ...this.stats, ...JSON.parse(data) };
      }
    } catch (error) {
      console.error('Failed to load stats:', error);
    }
  }

  private async saveAtomicGroups(): Promise<void> {
    try {
      const groupsData = Array.from(this.atomicGroups.entries());
      await AsyncStorage.setItem(this.ATOMIC_GROUPS_KEY, JSON.stringify(groupsData));
    } catch (error) {
      console.error('Failed to save atomic groups:', error);
    }
  }

  private async loadAtomicGroups(): Promise<void> {
    try {
      const data = await AsyncStorage.getItem(this.ATOMIC_GROUPS_KEY);
      if (data) {
        const groupsData = JSON.parse(data);
        this.atomicGroups = new Map(groupsData);
        this.stats.atomicGroups = this.atomicGroups.size;
      }
    } catch (error) {
      console.error('Failed to load atomic groups:', error);
      this.atomicGroups = new Map();
    }
  }

  private async saveRollbackLog(): Promise<void> {
    try {
      // Keep only last 100 rollback entries
      const trimmedLog = this.rollbackLog.slice(-100);
      await AsyncStorage.setItem(this.ROLLBACK_LOG_KEY, JSON.stringify(trimmedLog));
    } catch (error) {
      console.error('Failed to save rollback log:', error);
    }
  }

  private async loadRollbackLog(): Promise<void> {
    try {
      const data = await AsyncStorage.getItem(this.ROLLBACK_LOG_KEY);
      if (data) {
        this.rollbackLog = JSON.parse(data);
      }
    } catch (error) {
      console.error('Failed to load rollback log:', error);
      this.rollbackLog = [];
    }
  }

  // Public methods
  getQueue(): QueuedOperation[] {
    return [...this.queue];
  }

  getStats(): QueueStats {
    return { ...this.stats };
  }

  getPendingOperations(): QueuedOperation[] {
    return this.queue.filter(op => op.status === 'pending');
  }

  getFailedOperations(): QueuedOperation[] {
    return this.queue.filter(op => op.status === 'failed');
  }

  async clearCompleted(): Promise<void> {
    const completedIds = this.queue
      .filter(op => op.status === 'completed')
      .map(op => op.id);
    
    for (const id of completedIds) {
      await this.dequeue(id);
    }
  }

  async clearFailed(): Promise<void> {
    const failedIds = this.queue
      .filter(op => op.status === 'failed')
      .map(op => op.id);
    
    for (const id of failedIds) {
      await this.dequeue(id);
    }
  }

  async retryFailed(): Promise<void> {
    const failedOperations = this.queue.filter(op => op.status === 'failed');

    for (const operation of failedOperations) {
      operation.status = 'pending';
      operation.retryCount = 0;
      operation.error = undefined;

      this.stats.failedOperations--;
      this.stats.pendingOperations++;
    }

    await this.saveQueue();
    await this.saveStats();

    if (networkService.isOnline()) {
      this.processQueue();
    }
  }

  async clearAll(): Promise<void> {
    this.queue = [];
    this.atomicGroups.clear();
    this.rollbackLog = [];
    this.stats = {
      totalOperations: 0,
      pendingOperations: 0,
      failedOperations: 0,
      completedOperations: 0,
      processingOperations: 0,
      cancelledOperations: 0,
      averageProcessingTime: 0,
      totalDataSize: 0,
      atomicGroups: 0,
      conflictedOperations: 0,
      queueHealth: 'healthy',
    };

    await this.saveQueue();
    await this.saveStats();
    await this.saveAtomicGroups();
    await this.saveRollbackLog();
  }

  // Atomic operation methods
  async enqueueAtomicGroup(operations: Array<Omit<QueuedOperation, 'id' | 'timestamp' | 'retryCount' | 'status' | 'version' | 'checksum' | 'atomicGroup'>>): Promise<string[]> {
    const groupId = this.generateId();
    const operationIds: string[] = [];

    try {
      // Add all operations to the same atomic group
      for (const operation of operations) {
        const operationId = await this.enqueue({
          ...operation,
          atomicGroup: groupId,
        });
        operationIds.push(operationId);
      }

      console.log(`📦 Created atomic group ${groupId} with ${operations.length} operations`);
      return operationIds;
    } catch (error) {
      // Rollback any operations that were added
      for (const operationId of operationIds) {
        await this.cancelOperation(operationId);
      }
      throw error;
    }
  }

  async cancelOperation(operationId: string): Promise<boolean> {
    const operation = this.queue.find(op => op.id === operationId);
    if (!operation || operation.status === 'completed') {
      return false;
    }

    // Add to rollback log if it was processing
    if (operation.status === 'processing' && operation.rollbackData) {
      this.rollbackLog.push({
        id: operationId,
        timestamp: new Date().toISOString(),
        data: operation.rollbackData,
      });
    }

    // Update stats based on previous status
    const previousStatus = operation.status;
    if (previousStatus === 'pending') this.stats.pendingOperations--;
    else if (previousStatus === 'processing') this.stats.processingOperations--;
    else if (previousStatus === 'failed') this.stats.failedOperations--;

    operation.status = 'cancelled';
    this.stats.cancelledOperations++;

    await this.saveQueue();
    await this.saveStats();
    await this.saveRollbackLog();

    this.emit('operationCancelled', operation);
    return true;
  }

  async rollbackOperation(operationId: string): Promise<boolean> {
    const rollbackEntry = this.rollbackLog.find(entry => entry.id === operationId);
    if (!rollbackEntry) {
      console.warn(`No rollback data found for operation ${operationId}`);
      return false;
    }

    try {
      // Execute rollback logic based on operation type
      const operation = this.queue.find(op => op.id === operationId);
      if (operation) {
        await this.executeRollback(operation, rollbackEntry.data);
        console.log(`✅ Rolled back operation ${operationId}`);
        return true;
      }
      return false;
    } catch (error) {
      console.error(`Failed to rollback operation ${operationId}:`, error);
      return false;
    }
  }

  private async executeRollback(operation: QueuedOperation, rollbackData: any): Promise<void> {
    // Implementation depends on operation type
    switch (operation.type) {
      case 'CREATE_TASK':
        // Delete the created task
        if (rollbackData.createdId) {
          const { mobileTaskService } = await import('./taskService');
          await mobileTaskService.deleteTask(rollbackData.createdId);
        }
        break;
      case 'UPDATE_TASK':
        // Restore previous values
        if (rollbackData.previousData) {
          const { mobileTaskService } = await import('./taskService');
          await mobileTaskService.updateTask(rollbackData.taskId, rollbackData.previousData);
        }
        break;
      case 'DELETE_TASK':
        // Recreate the deleted task
        if (rollbackData.deletedTask) {
          const { mobileTaskService } = await import('./taskService');
          await mobileTaskService.createTask(rollbackData.deletedTask);
        }
        break;
    }
  }

  destroy(): void {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
    }
    
    this.removeAllListeners();
  }
}

export const offlineQueueService = new OfflineQueueService();
