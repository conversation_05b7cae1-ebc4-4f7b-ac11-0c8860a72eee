import { SecureStorageService, SecureStorageKeys, SecureStorageUtils } from './secureStorageService';
import { getSupabaseClient, refreshSupabaseToken } from './supabase';

/**
 * JWT Token Rotation and Refresh Service
 * Handles automatic token refresh and rotation for enhanced security
 */
export class TokenRotationService {
  private static refreshInterval: NodeJS.Timeout | null = null;
  private static isRefreshing = false;
  private static refreshPromise: Promise<void> | null = null;
  
  // Configuration
  private static readonly REFRESH_INTERVAL = 15 * 60 * 1000; // 15 minutes
  private static readonly TOKEN_EXPIRY_BUFFER = 5 * 60 * 1000; // 5 minutes before expiry
  private static readonly MAX_RETRY_ATTEMPTS = 3;
  private static readonly RETRY_DELAY = 2000; // 2 seconds

  /**
   * Initialize the token rotation service
   */
  static async initialize(): Promise<void> {
    try {
      console.log('🔄 Initializing token rotation service');
      
      // Check if we have existing tokens
      const tokens = await SecureStorageUtils.getAuthTokens();
      
      if (tokens.jwtToken) {
        // Validate token and refresh if needed
        await this.validateAndRefreshToken();
      }
      
      // Start automatic refresh interval
      this.startAutoRefresh();
      
      console.log('✅ Token rotation service initialized');
    } catch (error) {
      console.error('❌ Failed to initialize token rotation service:', error);
    }
  }

  /**
   * Start automatic token refresh
   */
  private static startAutoRefresh(): void {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
    }

    this.refreshInterval = setInterval(async () => {
      try {
        await this.validateAndRefreshToken();
      } catch (error) {
        console.error('❌ Auto refresh failed:', error);
      }
    }, this.REFRESH_INTERVAL);

    console.log('🔄 Auto refresh started');
  }

  /**
   * Stop automatic token refresh
   */
  static stopAutoRefresh(): void {
    if (this.refreshInterval) {
      clearInterval(this.refreshInterval);
      this.refreshInterval = null;
      console.log('⏹️ Auto refresh stopped');
    }
  }

  /**
   * Validate token and refresh if needed
   */
  static async validateAndRefreshToken(): Promise<void> {
    // Prevent concurrent refresh attempts
    if (this.isRefreshing) {
      if (this.refreshPromise) {
        await this.refreshPromise;
      }
      return;
    }

    this.isRefreshing = true;
    this.refreshPromise = this.performTokenRefresh();

    try {
      await this.refreshPromise;
    } finally {
      this.isRefreshing = false;
      this.refreshPromise = null;
    }
  }

  /**
   * Perform the actual token refresh
   */
  private static async performTokenRefresh(): Promise<void> {
    try {
      const tokens = await SecureStorageUtils.getAuthTokens();
      
      if (!tokens.jwtToken) {
        console.log('ℹ️ No JWT token found, skipping refresh');
        return;
      }

      // Check if token needs refresh
      if (!this.shouldRefreshToken(tokens.jwtToken)) {
        console.log('ℹ️ Token is still valid, no refresh needed');
        return;
      }

      console.log('🔄 Refreshing JWT token');
      
      // Attempt refresh with retry logic
      await this.refreshTokenWithRetry();
      
      console.log('✅ JWT token refreshed successfully');
    } catch (error) {
      console.error('❌ Token refresh failed:', error);
      
      // If refresh fails, we might need to re-authenticate
      await this.handleRefreshFailure(error);
    }
  }

  /**
   * Check if token should be refreshed
   */
  private static shouldRefreshToken(token: string): boolean {
    try {
      // Decode JWT token (without verification for expiry check)
      const payload = this.decodeJWTPayload(token);
      
      if (!payload.exp) {
        console.warn('⚠️ Token has no expiration, refreshing as precaution');
        return true;
      }

      const expiryTime = payload.exp * 1000; // Convert to milliseconds
      const currentTime = Date.now();
      const timeUntilExpiry = expiryTime - currentTime;

      // Refresh if token expires within the buffer time
      return timeUntilExpiry <= this.TOKEN_EXPIRY_BUFFER;
    } catch (error) {
      console.error('❌ Failed to decode token:', error);
      return true; // Refresh on decode error
    }
  }

  /**
   * Decode JWT payload (without verification)
   */
  private static decodeJWTPayload(token: string): any {
    try {
      const parts = token.split('.');
      if (parts.length !== 3) {
        throw new Error('Invalid JWT format');
      }

      const payload = parts[1];
      const decoded = atob(payload.replace(/-/g, '+').replace(/_/g, '/'));
      return JSON.parse(decoded);
    } catch (error) {
      throw new Error('Failed to decode JWT payload');
    }
  }

  /**
   * Refresh token with retry logic
   */
  private static async refreshTokenWithRetry(): Promise<void> {
    let lastError: any;

    for (let attempt = 1; attempt <= this.MAX_RETRY_ATTEMPTS; attempt++) {
      try {
        await refreshSupabaseToken();
        return; // Success
      } catch (error) {
        lastError = error;
        console.warn(`⚠️ Token refresh attempt ${attempt} failed:`, error);

        if (attempt < this.MAX_RETRY_ATTEMPTS) {
          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, this.RETRY_DELAY * attempt));
        }
      }
    }

    throw lastError;
  }

  /**
   * Handle refresh failure
   */
  private static async handleRefreshFailure(error: any): Promise<void> {
    console.error('❌ All token refresh attempts failed:', error);

    // Clear invalid tokens
    await SecureStorageUtils.clearAuthData();

    // Emit event for app to handle re-authentication
    this.emitTokenExpiredEvent();
  }

  /**
   * Emit token expired event
   */
  private static emitTokenExpiredEvent(): void {
    // In a real app, you might use an event emitter or Redux action
    console.log('🚨 Token expired - user needs to re-authenticate');
    
    // You could dispatch a Redux action here
    // store.dispatch(authActions.tokenExpired());
  }

  /**
   * Force token refresh
   */
  static async forceRefresh(): Promise<void> {
    console.log('🔄 Force refreshing token');
    await this.performTokenRefresh();
  }

  /**
   * Get token expiry information
   */
  static async getTokenInfo(): Promise<{
    hasToken: boolean;
    expiresAt?: Date;
    expiresIn?: number;
    needsRefresh: boolean;
  }> {
    try {
      const tokens = await SecureStorageUtils.getAuthTokens();
      
      if (!tokens.jwtToken) {
        return {
          hasToken: false,
          needsRefresh: false,
        };
      }

      const payload = this.decodeJWTPayload(tokens.jwtToken);
      const expiresAt = payload.exp ? new Date(payload.exp * 1000) : undefined;
      const expiresIn = expiresAt ? expiresAt.getTime() - Date.now() : undefined;
      const needsRefresh = this.shouldRefreshToken(tokens.jwtToken);

      return {
        hasToken: true,
        expiresAt,
        expiresIn,
        needsRefresh,
      };
    } catch (error) {
      console.error('❌ Failed to get token info:', error);
      return {
        hasToken: false,
        needsRefresh: true,
      };
    }
  }

  /**
   * Clean up the service
   */
  static cleanup(): void {
    this.stopAutoRefresh();
    this.isRefreshing = false;
    this.refreshPromise = null;
    console.log('🧹 Token rotation service cleaned up');
  }
}

/**
 * Token refresh middleware for API calls
 * Automatically refreshes tokens before API requests if needed
 */
export const createTokenRefreshMiddleware = () => {
  return async (request: any, next: any) => {
    try {
      // Check if token needs refresh before making the request
      const tokenInfo = await TokenRotationService.getTokenInfo();
      
      if (tokenInfo.hasToken && tokenInfo.needsRefresh) {
        console.log('🔄 Refreshing token before API request');
        await TokenRotationService.validateAndRefreshToken();
      }
      
      return next(request);
    } catch (error) {
      console.error('❌ Token refresh middleware error:', error);
      return next(request); // Continue with request even if refresh fails
    }
  };
};

/**
 * Utility functions for token management
 */
export const TokenUtils = {
  /**
   * Check if user is authenticated
   */
  isAuthenticated: async (): Promise<boolean> => {
    const tokenInfo = await TokenRotationService.getTokenInfo();
    return tokenInfo.hasToken && !tokenInfo.needsRefresh;
  },

  /**
   * Get time until token expires
   */
  getTimeUntilExpiry: async (): Promise<number | null> => {
    const tokenInfo = await TokenRotationService.getTokenInfo();
    return tokenInfo.expiresIn || null;
  },

  /**
   * Format token expiry time
   */
  formatExpiryTime: async (): Promise<string> => {
    const tokenInfo = await TokenRotationService.getTokenInfo();
    
    if (!tokenInfo.expiresAt) {
      return 'Unknown';
    }

    const now = new Date();
    const expiry = tokenInfo.expiresAt;
    const diff = expiry.getTime() - now.getTime();

    if (diff <= 0) {
      return 'Expired';
    }

    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days} day${days > 1 ? 's' : ''}`;
    } else if (hours > 0) {
      return `${hours} hour${hours > 1 ? 's' : ''}`;
    } else {
      return `${minutes} minute${minutes > 1 ? 's' : ''}`;
    }
  },
};
