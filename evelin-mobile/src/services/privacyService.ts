/**
 * Privacy Service
 * Manages user privacy settings, data retention, and automatic purging
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { 
  LocationPrivacySettings, 
  DataPurgeLog, 
  LocationDataSummary, 
  PrivacyAuditLog,
  DataExportRequest,
  LocationDataType,
  DataPurgeReason,
  DataRetentionPeriod,
  DEFAULT_PRIVACY_SETTINGS
} from '../types/privacy';
import { EncryptionService } from './encryptionService';
import { SecureStorageService } from './secureStorageService';

class PrivacyService {
  private static readonly PRIVACY_SETTINGS_KEY = 'privacy_settings';
  private static readonly PURGE_LOG_KEY = 'data_purge_log';
  private static readonly AUDIT_LOG_KEY = 'privacy_audit_log';
  private static readonly DATA_SUMMARY_KEY = 'location_data_summary';

  private settings: LocationPrivacySettings = DEFAULT_PRIVACY_SETTINGS;
  private purgeTimer: NodeJS.Timeout | null = null;

  /**
   * Initialize the privacy service
   */
  async initialize(): Promise<void> {
    try {
      await this.loadPrivacySettings();
      await this.scheduleAutomaticPurging();
      await this.logAuditEvent('service_initialized', 'Privacy service initialized');
      console.log('🔒 Privacy service initialized');
    } catch (error) {
      console.error('❌ Failed to initialize privacy service:', error);
      throw error;
    }
  }

  /**
   * Load privacy settings from secure storage
   */
  private async loadPrivacySettings(): Promise<void> {
    try {
      const encryptedSettings = await SecureStorageService.getItem(PrivacyService.PRIVACY_SETTINGS_KEY);
      if (encryptedSettings) {
        const decrypted = EncryptionService.decrypt(encryptedSettings);
        this.settings = { ...DEFAULT_PRIVACY_SETTINGS, ...JSON.parse(decrypted) };
      } else {
        // First time setup - use defaults
        await this.savePrivacySettings();
      }
    } catch (error) {
      console.error('Error loading privacy settings:', error);
      this.settings = DEFAULT_PRIVACY_SETTINGS;
    }
  }

  /**
   * Save privacy settings to secure storage
   */
  private async savePrivacySettings(): Promise<void> {
    try {
      const serialized = JSON.stringify(this.settings);
      const encrypted = EncryptionService.encrypt(serialized);
      await SecureStorageService.setItem(PrivacyService.PRIVACY_SETTINGS_KEY, encrypted);
      await this.logAuditEvent('settings_change', 'Privacy settings updated');
    } catch (error) {
      console.error('Error saving privacy settings:', error);
      throw error;
    }
  }

  /**
   * Get current privacy settings
   */
  getPrivacySettings(): LocationPrivacySettings {
    return { ...this.settings };
  }

  /**
   * Update privacy settings
   */
  async updatePrivacySettings(updates: Partial<LocationPrivacySettings>): Promise<void> {
    const oldSettings = { ...this.settings };
    this.settings = { ...this.settings, ...updates };
    
    await this.savePrivacySettings();
    
    // Check if data retention policies changed and trigger cleanup if needed
    if (this.hasRetentionPolicyChanged(oldSettings, this.settings)) {
      await this.enforceRetentionPolicies();
    }
    
    // Reschedule automatic purging if schedule changed
    if (oldSettings.autoPurge.schedule !== this.settings.autoPurge.schedule ||
        oldSettings.autoPurge.enabled !== this.settings.autoPurge.enabled) {
      await this.scheduleAutomaticPurging();
    }
  }

  /**
   * Check if retention policies have changed
   */
  private hasRetentionPolicyChanged(
    oldSettings: LocationPrivacySettings, 
    newSettings: LocationPrivacySettings
  ): boolean {
    const oldRetention = oldSettings.dataRetention;
    const newRetention = newSettings.dataRetention;
    
    return Object.keys(newRetention).some(key => 
      oldRetention[key as keyof typeof oldRetention] !== 
      newRetention[key as keyof typeof newRetention]
    );
  }

  /**
   * Get data retention period in milliseconds
   */
  private getRetentionPeriodMs(period: DataRetentionPeriod): number {
    const periods = {
      '1_hour': 60 * 60 * 1000,
      '24_hours': 24 * 60 * 60 * 1000,
      '7_days': 7 * 24 * 60 * 60 * 1000,
      '30_days': 30 * 24 * 60 * 60 * 1000,
      '90_days': 90 * 24 * 60 * 60 * 1000,
      '1_year': 365 * 24 * 60 * 60 * 1000,
      'forever': Infinity,
    };
    return periods[period];
  }

  /**
   * Enforce data retention policies
   */
  async enforceRetentionPolicies(): Promise<DataPurgeLog> {
    const now = Date.now();
    const purgeLog: DataPurgeLog = {
      id: `purge_${now}`,
      timestamp: new Date().toISOString(),
      dataTypes: [],
      reason: 'retention_policy',
      itemsDeleted: 0,
      sizeFreed: 0,
      userInitiated: false,
      details: 'Automatic retention policy enforcement',
    };

    try {
      // Purge location history
      const locationHistoryPurged = await this.purgeLocationHistory(
        this.getRetentionPeriodMs(this.settings.dataRetention.locationHistory)
      );
      if (locationHistoryPurged.itemsDeleted > 0) {
        purgeLog.dataTypes.push('location_history');
        purgeLog.itemsDeleted += locationHistoryPurged.itemsDeleted;
        purgeLog.sizeFreed += locationHistoryPurged.sizeFreed;
      }

      // Purge visited places
      const visitedPlacesPurged = await this.purgeVisitedPlaces(
        this.getRetentionPeriodMs(this.settings.dataRetention.visitedPlaces)
      );
      if (visitedPlacesPurged.itemsDeleted > 0) {
        purgeLog.dataTypes.push('visited_places');
        purgeLog.itemsDeleted += visitedPlacesPurged.itemsDeleted;
        purgeLog.sizeFreed += visitedPlacesPurged.sizeFreed;
      }

      // Purge search history
      const searchHistoryPurged = await this.purgeSearchHistory(
        this.getRetentionPeriodMs(this.settings.dataRetention.searchHistory)
      );
      if (searchHistoryPurged.itemsDeleted > 0) {
        purgeLog.dataTypes.push('search_history');
        purgeLog.itemsDeleted += searchHistoryPurged.itemsDeleted;
        purgeLog.sizeFreed += searchHistoryPurged.sizeFreed;
      }

      // Purge task locations
      const taskLocationsPurged = await this.purgeTaskLocations(
        this.getRetentionPeriodMs(this.settings.dataRetention.taskLocations)
      );
      if (taskLocationsPurged.itemsDeleted > 0) {
        purgeLog.dataTypes.push('task_locations');
        purgeLog.itemsDeleted += taskLocationsPurged.itemsDeleted;
        purgeLog.sizeFreed += taskLocationsPurged.sizeFreed;
      }

      // Save purge log
      await this.savePurgeLog(purgeLog);
      await this.logAuditEvent('data_deletion', `Purged ${purgeLog.itemsDeleted} items, freed ${purgeLog.sizeFreed} bytes`);

      console.log(`🧹 Data purge completed: ${purgeLog.itemsDeleted} items deleted, ${purgeLog.sizeFreed} bytes freed`);
      return purgeLog;
    } catch (error) {
      console.error('Error enforcing retention policies:', error);
      throw error;
    }
  }

  /**
   * Purge location history based on retention period
   */
  private async purgeLocationHistory(maxAgeMs: number): Promise<{ itemsDeleted: number; sizeFreed: number }> {
    if (maxAgeMs === Infinity) return { itemsDeleted: 0, sizeFreed: 0 };

    try {
      const locationHistory = await AsyncStorage.getItem('location_history');
      if (!locationHistory) return { itemsDeleted: 0, sizeFreed: 0 };

      const history = JSON.parse(locationHistory);
      const now = Date.now();
      const originalCount = history.length;
      const originalSize = new Blob([locationHistory]).size;

      // Filter out old entries
      const filteredHistory = history.filter((entry: any) => 
        (now - entry.timestamp) <= maxAgeMs
      );

      // Apply storage limits
      const maxEntries = this.settings.storageLimits.maxLocationHistoryEntries;
      const finalHistory = filteredHistory.slice(0, maxEntries);

      await AsyncStorage.setItem('location_history', JSON.stringify(finalHistory));
      
      const newSize = new Blob([JSON.stringify(finalHistory)]).size;
      const itemsDeleted = originalCount - finalHistory.length;
      const sizeFreed = originalSize - newSize;

      return { itemsDeleted, sizeFreed };
    } catch (error) {
      console.error('Error purging location history:', error);
      return { itemsDeleted: 0, sizeFreed: 0 };
    }
  }

  /**
   * Purge visited places based on retention period
   */
  private async purgeVisitedPlaces(maxAgeMs: number): Promise<{ itemsDeleted: number; sizeFreed: number }> {
    if (maxAgeMs === Infinity) return { itemsDeleted: 0, sizeFreed: 0 };

    try {
      const visitedPlaces = await AsyncStorage.getItem('visited_places');
      if (!visitedPlaces) return { itemsDeleted: 0, sizeFreed: 0 };

      const places = JSON.parse(visitedPlaces);
      const now = Date.now();
      const originalCount = places.length;
      const originalSize = new Blob([visitedPlaces]).size;

      // Filter out old places
      const filteredPlaces = places.filter((place: any) => {
        const lastVisit = new Date(place.lastVisit).getTime();
        return (now - lastVisit) <= maxAgeMs;
      });

      // Apply storage limits (keep most visited places)
      const maxPlaces = this.settings.storageLimits.maxVisitedPlaces;
      const finalPlaces = filteredPlaces
        .sort((a: any, b: any) => b.visits - a.visits)
        .slice(0, maxPlaces);

      await AsyncStorage.setItem('visited_places', JSON.stringify(finalPlaces));
      
      const newSize = new Blob([JSON.stringify(finalPlaces)]).size;
      const itemsDeleted = originalCount - finalPlaces.length;
      const sizeFreed = originalSize - newSize;

      return { itemsDeleted, sizeFreed };
    } catch (error) {
      console.error('Error purging visited places:', error);
      return { itemsDeleted: 0, sizeFreed: 0 };
    }
  }

  /**
   * Purge search history based on retention period
   */
  private async purgeSearchHistory(maxAgeMs: number): Promise<{ itemsDeleted: number; sizeFreed: number }> {
    if (maxAgeMs === Infinity) return { itemsDeleted: 0, sizeFreed: 0 };

    try {
      const searchHistory = await AsyncStorage.getItem('search_history');
      if (!searchHistory) return { itemsDeleted: 0, sizeFreed: 0 };

      const history = JSON.parse(searchHistory);
      const now = Date.now();
      const originalCount = history.length;
      const originalSize = new Blob([searchHistory]).size;

      // Filter out old searches
      const filteredHistory = history.filter((search: any) => 
        (now - search.timestamp) <= maxAgeMs
      );

      // Apply storage limits
      const maxEntries = this.settings.storageLimits.maxSearchHistoryEntries;
      const finalHistory = filteredHistory.slice(0, maxEntries);

      await AsyncStorage.setItem('search_history', JSON.stringify(finalHistory));
      
      const newSize = new Blob([JSON.stringify(finalHistory)]).size;
      const itemsDeleted = originalCount - finalHistory.length;
      const sizeFreed = originalSize - newSize;

      return { itemsDeleted, sizeFreed };
    } catch (error) {
      console.error('Error purging search history:', error);
      return { itemsDeleted: 0, sizeFreed: 0 };
    }
  }

  /**
   * Purge task locations based on retention period
   */
  private async purgeTaskLocations(maxAgeMs: number): Promise<{ itemsDeleted: number; sizeFreed: number }> {
    if (maxAgeMs === Infinity) return { itemsDeleted: 0, sizeFreed: 0 };

    try {
      // This would integrate with the task storage system
      // For now, return placeholder values
      return { itemsDeleted: 0, sizeFreed: 0 };
    } catch (error) {
      console.error('Error purging task locations:', error);
      return { itemsDeleted: 0, sizeFreed: 0 };
    }
  }

  /**
   * Save purge log
   */
  private async savePurgeLog(purgeLog: DataPurgeLog): Promise<void> {
    try {
      const existingLogs = await AsyncStorage.getItem(PrivacyService.PURGE_LOG_KEY);
      const logs = existingLogs ? JSON.parse(existingLogs) : [];
      
      logs.unshift(purgeLog);
      
      // Keep only last 50 purge logs
      const trimmedLogs = logs.slice(0, 50);
      
      await AsyncStorage.setItem(PrivacyService.PURGE_LOG_KEY, JSON.stringify(trimmedLogs));
    } catch (error) {
      console.error('Error saving purge log:', error);
    }
  }

  /**
   * Log audit event
   */
  private async logAuditEvent(action: string, details: string): Promise<void> {
    try {
      const auditLog: PrivacyAuditLog = {
        id: `audit_${Date.now()}`,
        timestamp: new Date().toISOString(),
        action: action as any,
        details,
        success: true,
      };

      const existingLogs = await AsyncStorage.getItem(PrivacyService.AUDIT_LOG_KEY);
      const logs = existingLogs ? JSON.parse(existingLogs) : [];
      
      logs.unshift(auditLog);
      
      // Keep only last 100 audit logs
      const trimmedLogs = logs.slice(0, 100);
      
      await AsyncStorage.setItem(PrivacyService.AUDIT_LOG_KEY, JSON.stringify(trimmedLogs));
    } catch (error) {
      console.error('Error logging audit event:', error);
    }
  }

  /**
   * Schedule automatic purging
   */
  private async scheduleAutomaticPurging(): Promise<void> {
    // Clear existing timer
    if (this.purgeTimer) {
      clearInterval(this.purgeTimer);
      this.purgeTimer = null;
    }

    if (!this.settings.autoPurge.enabled) {
      return;
    }

    const intervals = {
      daily: 24 * 60 * 60 * 1000,
      weekly: 7 * 24 * 60 * 60 * 1000,
      monthly: 30 * 24 * 60 * 60 * 1000,
    };

    const interval = intervals[this.settings.autoPurge.schedule];
    
    this.purgeTimer = setInterval(async () => {
      try {
        console.log('🕒 Running scheduled data purge...');
        await this.enforceRetentionPolicies();
        
        // Update last purge date
        this.settings.autoPurge.lastPurgeDate = new Date().toISOString();
        await this.savePrivacySettings();
      } catch (error) {
        console.error('Error in scheduled purge:', error);
      }
    }, interval);

    console.log(`⏰ Scheduled automatic purging: ${this.settings.autoPurge.schedule}`);
  }

  /**
   * Get data summary for privacy dashboard
   */
  async getLocationDataSummary(): Promise<LocationDataSummary> {
    try {
      const summary: LocationDataSummary = {
        totalEntries: 0,
        oldestEntry: null,
        newestEntry: null,
        storageSize: 0,
        dataTypes: {
          current_location: { count: 0, size: 0, oldestEntry: null },
          location_history: { count: 0, size: 0, oldestEntry: null },
          visited_places: { count: 0, size: 0, oldestEntry: null },
          search_history: { count: 0, size: 0, oldestEntry: null },
          geofences: { count: 0, size: 0, oldestEntry: null },
          route_data: { count: 0, size: 0, oldestEntry: null },
          task_locations: { count: 0, size: 0, oldestEntry: null },
        },
      };

      // Analyze location history
      const locationHistory = await AsyncStorage.getItem('location_history');
      if (locationHistory) {
        const history = JSON.parse(locationHistory);
        summary.dataTypes.location_history.count = history.length;
        summary.dataTypes.location_history.size = new Blob([locationHistory]).size;
        if (history.length > 0) {
          const timestamps = history.map((h: any) => h.timestamp).sort();
          summary.dataTypes.location_history.oldestEntry = new Date(timestamps[0]).toISOString();
        }
      }

      // Analyze visited places
      const visitedPlaces = await AsyncStorage.getItem('visited_places');
      if (visitedPlaces) {
        const places = JSON.parse(visitedPlaces);
        summary.dataTypes.visited_places.count = places.length;
        summary.dataTypes.visited_places.size = new Blob([visitedPlaces]).size;
        if (places.length > 0) {
          const visits = places.map((p: any) => new Date(p.lastVisit).getTime()).sort();
          summary.dataTypes.visited_places.oldestEntry = new Date(visits[0]).toISOString();
        }
      }

      // Calculate totals
      Object.values(summary.dataTypes).forEach(dataType => {
        summary.totalEntries += dataType.count;
        summary.storageSize += dataType.size;
        if (dataType.oldestEntry) {
          if (!summary.oldestEntry || dataType.oldestEntry < summary.oldestEntry) {
            summary.oldestEntry = dataType.oldestEntry;
          }
          if (!summary.newestEntry || dataType.oldestEntry > summary.newestEntry) {
            summary.newestEntry = dataType.oldestEntry;
          }
        }
      });

      return summary;
    } catch (error) {
      console.error('Error getting location data summary:', error);
      throw error;
    }
  }

  /**
   * Get purge logs
   */
  async getPurgeLogs(): Promise<DataPurgeLog[]> {
    try {
      const logs = await AsyncStorage.getItem(PrivacyService.PURGE_LOG_KEY);
      return logs ? JSON.parse(logs) : [];
    } catch (error) {
      console.error('Error getting purge logs:', error);
      return [];
    }
  }

  /**
   * Get audit logs
   */
  async getAuditLogs(): Promise<PrivacyAuditLog[]> {
    try {
      const logs = await AsyncStorage.getItem(PrivacyService.AUDIT_LOG_KEY);
      return logs ? JSON.parse(logs) : [];
    } catch (error) {
      console.error('Error getting audit logs:', error);
      return [];
    }
  }

  /**
   * Manually purge specific data types
   */
  async purgeDataTypes(dataTypes: LocationDataType[], reason: DataPurgeReason = 'user_request'): Promise<DataPurgeLog> {
    const purgeLog: DataPurgeLog = {
      id: `manual_purge_${Date.now()}`,
      timestamp: new Date().toISOString(),
      dataTypes,
      reason,
      itemsDeleted: 0,
      sizeFreed: 0,
      userInitiated: true,
      details: `Manual purge of: ${dataTypes.join(', ')}`,
    };

    try {
      for (const dataType of dataTypes) {
        let result = { itemsDeleted: 0, sizeFreed: 0 };

        switch (dataType) {
          case 'location_history':
            result = await this.purgeLocationHistory(0); // Purge all
            break;
          case 'visited_places':
            result = await this.purgeVisitedPlaces(0); // Purge all
            break;
          case 'search_history':
            result = await this.purgeSearchHistory(0); // Purge all
            break;
          case 'task_locations':
            result = await this.purgeTaskLocations(0); // Purge all
            break;
          case 'current_location':
            await AsyncStorage.removeItem('current_location');
            result = { itemsDeleted: 1, sizeFreed: 0 };
            break;
          case 'geofences':
            await AsyncStorage.removeItem('geofences');
            result = { itemsDeleted: 1, sizeFreed: 0 };
            break;
          case 'route_data':
            await AsyncStorage.removeItem('route_data');
            result = { itemsDeleted: 1, sizeFreed: 0 };
            break;
        }

        purgeLog.itemsDeleted += result.itemsDeleted;
        purgeLog.sizeFreed += result.sizeFreed;
      }

      await this.savePurgeLog(purgeLog);
      await this.logAuditEvent('data_deletion', `Manual purge: ${purgeLog.itemsDeleted} items deleted`);

      console.log(`🗑️ Manual data purge completed: ${purgeLog.itemsDeleted} items deleted`);
      return purgeLog;
    } catch (error) {
      console.error('Error in manual data purge:', error);
      throw error;
    }
  }

  /**
   * Export user data for GDPR compliance
   */
  async exportUserData(dataTypes: LocationDataType[], format: 'json' | 'csv' = 'json'): Promise<DataExportRequest> {
    const exportRequest: DataExportRequest = {
      id: `export_${Date.now()}`,
      requestDate: new Date().toISOString(),
      status: 'processing',
      dataTypes,
      format,
    };

    try {
      const exportData: any = {};

      for (const dataType of dataTypes) {
        switch (dataType) {
          case 'location_history':
            const locationHistory = await AsyncStorage.getItem('location_history');
            exportData.locationHistory = locationHistory ? JSON.parse(locationHistory) : [];
            break;
          case 'visited_places':
            const visitedPlaces = await AsyncStorage.getItem('visited_places');
            exportData.visitedPlaces = visitedPlaces ? JSON.parse(visitedPlaces) : [];
            break;
          case 'search_history':
            const searchHistory = await AsyncStorage.getItem('search_history');
            exportData.searchHistory = searchHistory ? JSON.parse(searchHistory) : [];
            break;
          // Add other data types as needed
        }
      }

      // Add metadata
      exportData.metadata = {
        exportDate: new Date().toISOString(),
        dataTypes,
        privacySettings: this.settings,
      };

      // For now, just log the export (in a real app, you'd upload to secure storage)
      console.log('📤 Data export prepared:', exportData);

      exportRequest.status = 'completed';
      exportRequest.fileSize = new Blob([JSON.stringify(exportData)]).size;

      await this.logAuditEvent('data_export', `Data export completed: ${dataTypes.join(', ')}`);

      return exportRequest;
    } catch (error) {
      console.error('Error exporting user data:', error);
      exportRequest.status = 'failed';
      return exportRequest;
    }
  }

  /**
   * Check if data collection is allowed for specific type
   */
  isDataCollectionAllowed(dataType: LocationDataType): boolean {
    if (!this.settings.locationDataCollection.enabled) {
      return false;
    }

    // Privacy mode restricts all data collection
    if (this.settings.advancedControls.enablePrivacyMode) {
      return false;
    }

    // Check specific data type permissions
    switch (dataType) {
      case 'location_history':
        return this.settings.locationDataCollection.backgroundTracking ||
               !this.settings.locationDataCollection.onlyWhenAppActive;
      case 'current_location':
        return true; // Always allowed if location is enabled
      default:
        return true;
    }
  }

  /**
   * Cleanup and shutdown
   */
  async cleanup(): Promise<void> {
    if (this.purgeTimer) {
      clearInterval(this.purgeTimer);
      this.purgeTimer = null;
    }
    console.log('🔒 Privacy service cleaned up');
  }
}

export const privacyService = new PrivacyService();
