import CryptoJ<PERSON> from 'crypto-js';
import * as SecureStore from 'expo-secure-store';
import { Platform } from 'react-native';

/**
 * Encryption Service for securing sensitive data
 * Uses AES encryption with secure key management
 */
export class EncryptionService {
  private static readonly ENCRYPTION_KEY_NAME = 'evelin_encryption_key';
  private static readonly KEY_SIZE = 256; // AES-256
  private static readonly IV_SIZE = 16; // 128-bit IV
  private static encryptionKey: string | null = null;

  /**
   * Initialize the encryption service
   * Generates or retrieves the encryption key
   */
  static async initialize(): Promise<void> {
    try {
      // Try to get existing key from secure storage
      let key = await SecureStore.getItemAsync(this.ENCRYPTION_KEY_NAME);
      
      if (!key) {
        // Generate new encryption key
        key = this.generateEncryptionKey();
        
        // Store the key securely
        await SecureStore.setItemAsync(this.ENCRYPTION_KEY_NAME, key, {
          requireAuthentication: false, // Don't require biometric auth for app functionality
          keychainService: 'evelin-app-keychain',
          sharedPreferencesName: 'evelin-secure-prefs',
        });
        
        console.log('🔐 Generated new encryption key');
      } else {
        console.log('🔐 Retrieved existing encryption key');
      }
      
      this.encryptionKey = key;
    } catch (error) {
      console.error('❌ Failed to initialize encryption service:', error);
      // Fallback to a deterministic key (less secure but functional)
      this.encryptionKey = this.generateFallbackKey();
      console.warn('⚠️ Using fallback encryption key');
    }
  }

  /**
   * Generate a secure random encryption key
   */
  private static generateEncryptionKey(): string {
    try {
      return CryptoJS.lib.WordArray.random(this.KEY_SIZE / 8).toString();
    } catch (error) {
      console.log('ℹ️ Crypto random not available (Expo Go limitation), using fallback key generation');
      // Fallback for Expo Go - use timestamp and math random
      const timestamp = Date.now().toString();
      const random = Math.random().toString();
      return CryptoJS.SHA256(timestamp + random + 'evelin-fallback-key').toString();
    }
  }

  /**
   * Generate a fallback key for cases where SecureStore is unavailable
   */
  private static generateFallbackKey(): string {
    // Use a combination of device-specific and app-specific data
    const deviceInfo = Platform.OS + Platform.Version;
    const appInfo = 'evelin-voice-location-helper-v1.0';
    return CryptoJS.SHA256(deviceInfo + appInfo).toString();
  }

  /**
   * Check if encryption is available
   */
  static isEncryptionAvailable(): boolean {
    try {
      // Test if crypto functions work
      CryptoJS.lib.WordArray.random(16);
      return true;
    } catch (error) {
      return false;
    }
  }

  /**
   * Encrypt sensitive data
   */
  static encrypt(data: string): string {
    if (!this.encryptionKey) {
      throw new Error('Encryption service not initialized');
    }

    // Check if encryption is available
    if (!this.isEncryptionAvailable()) {
      console.log('ℹ️ Encryption not available (Expo Go limitation), returning unencrypted data');
      return data; // Return unencrypted data in Expo Go
    }

    try {
      // Generate random IV for each encryption
      let iv: CryptoJS.lib.WordArray;
      try {
        iv = CryptoJS.lib.WordArray.random(this.IV_SIZE);
      } catch (error) {
        // Fallback IV generation for Expo Go
        const timestamp = Date.now().toString();
        const random = Math.random().toString();
        iv = CryptoJS.SHA256(timestamp + random).clone();
        iv.sigBytes = this.IV_SIZE; // Truncate to IV size
      }

      // Encrypt the data
      const encrypted = CryptoJS.AES.encrypt(data, this.encryptionKey, {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      });

      // Combine IV and encrypted data
      const combined = iv.concat(encrypted.ciphertext);
      return combined.toString(CryptoJS.enc.Base64);
    } catch (error) {
      console.log('ℹ️ Encryption failed (Expo Go limitation), returning unencrypted data');
      return data; // Return unencrypted data as fallback
    }
  }

  /**
   * Decrypt sensitive data
   */
  static decrypt(encryptedData: string): string {
    if (!this.encryptionKey) {
      throw new Error('Encryption service not initialized');
    }

    // Check if encryption is available
    if (!this.isEncryptionAvailable()) {
      console.log('ℹ️ Decryption not available (Expo Go limitation), returning data as-is');
      return encryptedData; // Return data as-is in Expo Go
    }

    try {
      // Try to parse as Base64 - if it fails, it might be unencrypted data
      let combined;
      try {
        combined = CryptoJS.enc.Base64.parse(encryptedData);
      } catch (parseError) {
        // If parsing fails, assume it's unencrypted data
        console.log('ℹ️ Data appears to be unencrypted, returning as-is');
        return encryptedData;
      }

      // Extract IV and ciphertext
      const iv = CryptoJS.lib.WordArray.create(combined.words.slice(0, this.IV_SIZE / 4));
      const ciphertext = CryptoJS.lib.WordArray.create(combined.words.slice(this.IV_SIZE / 4));

      // Decrypt the data
      const decrypted = CryptoJS.AES.decrypt(
        { ciphertext: ciphertext } as any,
        this.encryptionKey,
        {
          iv: iv,
          mode: CryptoJS.mode.CBC,
          padding: CryptoJS.pad.Pkcs7
        }
      );

      return decrypted.toString(CryptoJS.enc.Utf8);
    } catch (error) {
      console.log('ℹ️ Decryption failed (Expo Go limitation), returning data as-is');
      return encryptedData; // Return data as-is as fallback
    }
  }

  /**
   * Check if encryption is available
   */
  static isAvailable(): boolean {
    return this.encryptionKey !== null;
  }

  /**
   * Rotate the encryption key (for enhanced security)
   */
  static async rotateKey(): Promise<void> {
    try {
      const newKey = this.generateEncryptionKey();
      
      await SecureStore.setItemAsync(this.ENCRYPTION_KEY_NAME, newKey, {
        requireAuthentication: false,
        keychainService: 'evelin-app-keychain',
        sharedPreferencesName: 'evelin-secure-prefs',
      });
      
      this.encryptionKey = newKey;
      console.log('🔄 Encryption key rotated successfully');
    } catch (error) {
      console.error('❌ Failed to rotate encryption key:', error);
      throw new Error('Failed to rotate encryption key');
    }
  }

  /**
   * Clear encryption key (for logout/reset)
   */
  static async clearKey(): Promise<void> {
    try {
      await SecureStore.deleteItemAsync(this.ENCRYPTION_KEY_NAME);
      this.encryptionKey = null;
      console.log('🗑️ Encryption key cleared');
    } catch (error) {
      console.error('❌ Failed to clear encryption key:', error);
    }
  }
}

/**
 * Redux Persist Transform for encrypting sensitive data
 */
export const createEncryptTransform = (whitelist?: string[], blacklist?: string[]) => {
  return {
    in: (inboundState: any, key: string) => {
      // Check if this key should be encrypted
      if (whitelist && !whitelist.includes(key)) return inboundState;
      if (blacklist && blacklist.includes(key)) return inboundState;

      try {
        // Check if encryption service is available
        if (!EncryptionService.isEncryptionAvailable()) {
          console.log(`ℹ️ Encryption not available for key ${key} (Expo Go limitation), storing unencrypted`);
          return inboundState;
        }

        const serialized = JSON.stringify(inboundState);
        return EncryptionService.encrypt(serialized);
      } catch (error) {
        console.log(`ℹ️ Failed to encrypt state for key ${key} (Expo Go limitation), storing unencrypted`);
        return inboundState; // Return unencrypted as fallback
      }
    },
    out: (outboundState: any, key: string) => {
      // Check if this key should be decrypted
      if (whitelist && !whitelist.includes(key)) return outboundState;
      if (blacklist && blacklist.includes(key)) return outboundState;

      try {
        if (typeof outboundState === 'string') {
          const decrypted = EncryptionService.decrypt(outboundState);
          return JSON.parse(decrypted);
        }
        return outboundState;
      } catch (error) {
        console.log(`ℹ️ Failed to decrypt state for key ${key} (Expo Go limitation), returning as-is`);
        return outboundState; // Return as-is if decryption fails
      }
    }
  };
};

/**
 * Utility functions for encrypting specific data types
 */
export const EncryptionUtils = {
  /**
   * Encrypt task data
   */
  encryptTask: (task: any) => {
    const sensitiveFields = ['text', 'location', 'coordinates'];
    const encrypted = { ...task };
    
    sensitiveFields.forEach(field => {
      if (encrypted[field]) {
        encrypted[field] = EncryptionService.encrypt(JSON.stringify(encrypted[field]));
      }
    });
    
    return encrypted;
  },

  /**
   * Decrypt task data
   */
  decryptTask: (encryptedTask: any) => {
    const sensitiveFields = ['text', 'location', 'coordinates'];
    const decrypted = { ...encryptedTask };
    
    sensitiveFields.forEach(field => {
      if (decrypted[field] && typeof decrypted[field] === 'string') {
        try {
          decrypted[field] = JSON.parse(EncryptionService.decrypt(decrypted[field]));
        } catch (error) {
          console.error(`Failed to decrypt task field ${field}:`, error);
        }
      }
    });
    
    return decrypted;
  },

  /**
   * Encrypt location data
   */
  encryptLocation: (location: any) => {
    const sensitiveFields = ['coordinates', 'address', 'placeName'];
    const encrypted = { ...location };
    
    sensitiveFields.forEach(field => {
      if (encrypted[field]) {
        encrypted[field] = EncryptionService.encrypt(JSON.stringify(encrypted[field]));
      }
    });
    
    return encrypted;
  },

  /**
   * Decrypt location data
   */
  decryptLocation: (encryptedLocation: any) => {
    const sensitiveFields = ['coordinates', 'address', 'placeName'];
    const decrypted = { ...encryptedLocation };
    
    sensitiveFields.forEach(field => {
      if (decrypted[field] && typeof decrypted[field] === 'string') {
        try {
          decrypted[field] = JSON.parse(EncryptionService.decrypt(decrypted[field]));
        } catch (error) {
          console.error(`Failed to decrypt location field ${field}:`, error);
        }
      }
    });
    
    return decrypted;
  }
};
