import { EventEmitter } from '../utils/EventEmitter';
import { networkService } from './networkService';
import { offlineQueueService } from './offlineQueueService';
import { offlineStorageService } from './offlineStorageService';
import { store } from '../store';
import { dataVersioningService } from './dataVersioningService';
import { conflictResolutionService } from './conflictResolutionService';
import { 
  updateNetworkState, 
  addMetrics, 
  setQueueStats,
  initializeNetworkService,
  updateQueueStats
} from '../store/slices/networkSlice';
import { 
  setOfflineStatus, 
  updateOfflineStats,
  loadOfflineTasksAsync,
  syncTasksAsync
} from '../store/slices/tasksSlice';

export interface OfflineManagerConfig {
  autoSync: boolean;
  syncInterval: number; // in milliseconds
  maxRetries: number;
  retryDelay: number;
  enableBackgroundSync: boolean;
  conflictResolution: 'client-wins' | 'server-wins' | 'merge';
}

class OfflineManager extends EventEmitter {
  private isInitialized = false;
  private syncInterval: NodeJS.Timeout | null = null;
  private config: OfflineManagerConfig = {
    autoSync: true,
    syncInterval: 5 * 60 * 1000, // 5 minutes
    maxRetries: 3,
    retryDelay: 2000,
    enableBackgroundSync: true,
    conflictResolution: 'server-wins',
  };

  async initialize(config?: Partial<OfflineManagerConfig>): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Update configuration
      this.config = { ...this.config, ...config };

      // Initialize data versioning first
      await dataVersioningService.initialize();

      // Check for and apply any pending migrations
      const needsMigration = await dataVersioningService.checkForMigrations();
      if (needsMigration) {
        console.log('🔄 Applying data migrations...');
        const migrationResult = await dataVersioningService.migrate();
        if (!migrationResult.success) {
          console.error('❌ Data migration failed:', migrationResult.errors);
          throw new Error('Data migration failed');
        }
        console.log(`✅ Data migrated to version ${migrationResult.version}`);
      }

      // Initialize conflict resolution service
      await conflictResolutionService.initialize();

      // Initialize network service
      await store.dispatch(initializeNetworkService());

      // Initialize offline services
      await offlineQueueService.initialize();
      await offlineStorageService.initialize();

      // Set up event listeners
      this.setupEventListeners();

      // Load offline data on startup
      await this.loadOfflineData();

      // Start periodic sync if enabled
      if (this.config.autoSync) {
        this.startPeriodicSync();
      }

      // Start periodic maintenance
      this.startPeriodicMaintenance();

      this.isInitialized = true;
      console.log('✅ Offline manager initialized');
      this.emit('initialized');
    } catch (error) {
      console.error('❌ Failed to initialize offline manager:', error);
      throw error;
    }
  }

  private setupEventListeners(): void {
    // Network state changes
    networkService.on('networkStateChanged', (networkState) => {
      store.dispatch(updateNetworkState(networkState));
      store.dispatch(setOfflineStatus(!networkState.isConnected));
      
      this.emit('networkStateChanged', networkState);
    });

    // Connection restored
    networkService.on('connectionRestored', async (networkState) => {
      console.log('🌐 Connection restored, triggering sync...');
      
      // Update Redux state
      store.dispatch(updateNetworkState(networkState));
      store.dispatch(setOfflineStatus(false));
      
      // Trigger sync
      if (this.config.autoSync) {
        await this.syncNow();
      }
      
      this.emit('connectionRestored', networkState);
    });

    // Connection lost
    networkService.on('connectionLost', (networkState) => {
      console.log('📵 Connection lost, switching to offline mode...');
      
      // Update Redux state
      store.dispatch(updateNetworkState(networkState));
      store.dispatch(setOfflineStatus(true));
      
      this.emit('connectionLost', networkState);
    });

    // Network metrics updated
    networkService.on('metricsUpdated', (metrics) => {
      store.dispatch(addMetrics(metrics));
    });

    // Queue events
    offlineQueueService.on('operationQueued', (operation) => {
      this.updateQueueStats();
      this.emit('operationQueued', operation);
    });

    offlineQueueService.on('operationCompleted', (operation) => {
      this.updateQueueStats();
      this.emit('operationCompleted', operation);
    });

    offlineQueueService.on('operationFailed', (operation) => {
      this.updateQueueStats();
      this.emit('operationFailed', operation);
    });
  }

  private async loadOfflineData(): Promise<void> {
    try {
      // Load tasks from offline storage
      await store.dispatch(loadOfflineTasksAsync());
      
      // Update offline stats
      await this.updateOfflineStats();
      
      console.log('📱 Offline data loaded successfully');
    } catch (error) {
      console.error('Failed to load offline data:', error);
    }
  }

  private async updateQueueStats(): Promise<void> {
    try {
      await store.dispatch(updateQueueStats());
    } catch (error) {
      console.error('Failed to update queue stats:', error);
    }
  }

  private async updateOfflineStats(): Promise<void> {
    try {
      const storageInfo = await offlineStorageService.getStorageInfo();
      const queueStats = offlineQueueService.getStats();
      
      store.dispatch(updateOfflineStats({
        pendingOperations: queueStats.pendingOperations,
        conflicts: storageInfo.conflicts,
        storageSize: storageInfo.storageSize,
        lastOfflineSync: storageInfo.lastSync,
      }));
    } catch (error) {
      console.error('Failed to update offline stats:', error);
    }
  }

  private startPeriodicSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    this.syncInterval = setInterval(async () => {
      try {
        // Check if we're still initialized and online
        if (!this.isInitialized || !networkService.isOnline()) {
          return;
        }

        // Check if Redux store is available
        const state = store.getState();
        if (!state || !state.tasks) {
          console.warn('⚠️ Redux store not properly initialized, skipping sync');
          return;
        }

        await this.syncNow();
      } catch (error) {
        console.error('❌ Error in periodic sync:', error);
        // Don't throw the error to prevent crashing the interval
      }
    }, this.config.syncInterval);

    console.log(`🔄 Periodic sync started (interval: ${this.config.syncInterval}ms)`);
  }

  private startPeriodicMaintenance(): void {
    // Run maintenance tasks every hour
    setInterval(async () => {
      await this.performMaintenance();
    }, 60 * 60 * 1000); // 1 hour

    console.log('🧹 Periodic maintenance started');
  }

  private async performMaintenance(): Promise<void> {
    try {
      console.log('🧹 Running periodic maintenance...');

      // Clean up old backups
      await dataVersioningService.cleanupOldBackups(5);

      // Validate data integrity
      const integrityCheck = await dataVersioningService.validateDataIntegrity();
      if (!integrityCheck.valid) {
        console.warn('⚠️ Data integrity issues found:', integrityCheck.errors);
        this.emit('dataIntegrityIssue', integrityCheck.errors);
      }

      // Clean up resolved conflicts
      await conflictResolutionService.clearResolvedConflicts();

      // Get queue stats and emit health status
      const queueStats = await offlineQueueService.getStats();
      if (queueStats.queueHealth !== 'healthy') {
        console.warn(`⚠️ Queue health: ${queueStats.queueHealth}`);
        this.emit('queueHealthIssue', queueStats);
      }

      console.log('✅ Maintenance completed');
    } catch (error) {
      console.error('❌ Maintenance failed:', error);
    }
  }

  private stopPeriodicSync(): void {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
      console.log('⏹️ Periodic sync stopped');
    }
  }

  // Public methods
  async syncNow(): Promise<boolean> {
    if (networkService.isOffline()) {
      console.warn('⚠️ Cannot sync while offline');
      return false;
    }

    // Check if manager is properly initialized
    if (!this.isInitialized) {
      console.warn('⚠️ Offline manager not initialized, cannot sync');
      return false;
    }

    try {
      console.log('🔄 Starting manual sync...');

      // Verify Redux store state before syncing
      const state = store.getState();
      if (!state || !state.tasks) {
        console.error('❌ Redux store not properly initialized');
        this.emit('syncError', new Error('Redux store not initialized'));
        return false;
      }

      // Process offline queue
      await offlineQueueService.processQueue();

      // Sync with server
      const result = await store.dispatch(syncTasksAsync());

      // Update stats
      await this.updateOfflineStats();

      if (result.meta.requestStatus === 'fulfilled') {
        console.log('✅ Sync completed successfully');
        this.emit('syncCompleted', result.payload);
        return true;
      } else {
        console.error('❌ Sync failed:', result.payload);
        this.emit('syncFailed', result.payload);
        return false;
      }
    } catch (error) {
      console.error('❌ Sync error:', error);
      this.emit('syncError', error);
      return false;
    }
  }

  async retryFailedOperations(): Promise<void> {
    try {
      await offlineQueueService.retryFailed();
      await this.updateQueueStats();
      console.log('🔄 Retrying failed operations...');
    } catch (error) {
      console.error('Failed to retry operations:', error);
    }
  }

  async clearOfflineData(): Promise<void> {
    try {
      await offlineStorageService.clearAllOfflineData();
      await offlineQueueService.clearCompleted();
      await this.updateOfflineStats();
      console.log('🗑️ Offline data cleared');
      this.emit('offlineDataCleared');
    } catch (error) {
      console.error('Failed to clear offline data:', error);
      throw error;
    }
  }

  // Configuration methods
  updateConfig(newConfig: Partial<OfflineManagerConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    // Restart periodic sync if interval changed
    if (newConfig.syncInterval && this.config.autoSync) {
      this.startPeriodicSync();
    }
    
    // Stop/start sync based on autoSync setting
    if (newConfig.autoSync !== undefined) {
      if (newConfig.autoSync) {
        this.startPeriodicSync();
      } else {
        this.stopPeriodicSync();
      }
    }
    
    console.log('⚙️ Offline manager configuration updated:', this.config);
  }

  getConfig(): OfflineManagerConfig {
    return { ...this.config };
  }

  // Status methods
  isOnline(): boolean {
    return networkService.isOnline();
  }

  isOffline(): boolean {
    return networkService.isOffline();
  }

  getNetworkState() {
    return networkService.getNetworkState();
  }

  getQueueStats() {
    return offlineQueueService.getStats();
  }

  async getStorageInfo() {
    return await offlineStorageService.getStorageInfo();
  }

  // Cleanup
  destroy(): void {
    this.stopPeriodicSync();
    networkService.destroy();
    offlineQueueService.destroy();
    this.removeAllListeners();
    this.isInitialized = false;
    console.log('🧹 Offline manager destroyed');
  }
}

export const offlineManager = new OfflineManager();
