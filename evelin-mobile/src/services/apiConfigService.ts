/**
 * API Configuration Service
 * Securely manages API tokens and configurations
 */

import { EncryptionService } from './encryptionService';
import { SecureStorageService } from './secureStorageService';

interface APIConfig {
  huggingFaceToken?: string;
  googleMapsApiKey?: string;
  openAIApiKey?: string;
}

class APIConfigService {
  private static readonly CONFIG_KEY = 'api_config_encrypted';
  private config: APIConfig = {};
  private initialized = false;

  /**
   * Initialize the API configuration service
   */
  async initialize(): Promise<void> {
    try {
      await this.loadConfig();
      this.initialized = true;
      console.log('🔐 API Configuration service initialized');

      // Test token retrieval immediately after initialization
      const testToken = this.getHuggingFaceToken();
      console.log(`🔐 Test token retrieval: ${!!testToken} (length: ${testToken?.length || 0})`);
    } catch (error) {
      console.error('❌ Failed to initialize API configuration service:', error);
      // Use environment variables as fallback
      this.loadFromEnvironment();
      this.initialized = true;
    }
  }

  /**
   * Load configuration from secure storage
   */
  private async loadConfig(): Promise<void> {
    try {
      const encryptedConfig = await SecureStorageService.getItem(APIConfigService.CONFIG_KEY);

      if (encryptedConfig) {
        try {
          // Try to decrypt the configuration
          const decrypted = EncryptionService.decrypt(encryptedConfig);
          this.config = JSON.parse(decrypted);
          console.log('🔐 Loaded encrypted API configuration');
          console.log(`🔐 Stored config contains: HF token: ${!!this.config.huggingFaceToken}, GMaps: ${!!this.config.googleMapsApiKey}`);

          // Check if Hugging Face token is missing and refresh from environment
          if (!this.config.huggingFaceToken && process.env.EXPO_PUBLIC_HUGGINGFACE_API_TOKEN) {
            console.log('🔐 Hugging Face token missing from stored config, refreshing from environment');
            this.config.huggingFaceToken = process.env.EXPO_PUBLIC_HUGGINGFACE_API_TOKEN;
            await this.saveConfig();
          }
        } catch (decryptError) {
          // If decryption fails (e.g., in Expo Go), try to parse as plain JSON
          console.log('🔐 Decryption failed, trying plain JSON (Expo Go mode)');
          try {
            this.config = JSON.parse(encryptedConfig);
            console.log('🔐 Loaded plain API configuration');
            console.log(`🔐 Stored config contains: HF token: ${!!this.config.huggingFaceToken}, GMaps: ${!!this.config.googleMapsApiKey}`);

            // Check if Hugging Face token is missing and refresh from environment
            if (!this.config.huggingFaceToken && process.env.EXPO_PUBLIC_HUGGINGFACE_API_TOKEN) {
              console.log('🔐 Hugging Face token missing from stored config, refreshing from environment');
              this.config.huggingFaceToken = process.env.EXPO_PUBLIC_HUGGINGFACE_API_TOKEN;
              await this.saveConfig();
            }
          } catch (parseError) {
            console.log('🔐 Failed to parse stored config, loading from environment');
            this.loadFromEnvironment();
          }
        }
      } else {
        // First time setup - load from environment and save
        console.log('🔐 No stored config found, loading from environment');
        this.loadFromEnvironment();
        await this.saveConfig();
      }
    } catch (error) {
      console.error('Error loading API configuration:', error);
      this.loadFromEnvironment();
    }
  }

  /**
   * Load configuration from environment variables
   */
  private loadFromEnvironment(): void {
    const hfToken = process.env.EXPO_PUBLIC_HUGGINGFACE_API_TOKEN;
    const gmapsKey = process.env.EXPO_PUBLIC_GOOGLE_MAPS_API_KEY;

    this.config = {
      huggingFaceToken: hfToken,
      googleMapsApiKey: gmapsKey,
    };

    console.log(`🔐 Loaded API configuration from environment:`);
    console.log(`🔐 - Hugging Face token: ${!!hfToken} (length: ${hfToken?.length || 0})`);
    console.log(`🔐 - Google Maps key: ${!!gmapsKey} (length: ${gmapsKey?.length || 0})`);
  }

  /**
   * Save configuration to secure storage
   */
  private async saveConfig(): Promise<void> {
    try {
      const serialized = JSON.stringify(this.config);

      try {
        // Try to encrypt the configuration
        const encrypted = EncryptionService.encrypt(serialized);
        await SecureStorageService.setItem(APIConfigService.CONFIG_KEY, encrypted);
        console.log('🔐 Saved encrypted API configuration');
      } catch (encryptError) {
        // If encryption fails (e.g., in Expo Go), save as plain JSON
        console.log('🔐 Encryption failed, saving as plain JSON (Expo Go mode)');
        await SecureStorageService.setItem(APIConfigService.CONFIG_KEY, serialized);
        console.log('🔐 Saved plain API configuration');
      }
    } catch (error) {
      console.error('Error saving API configuration:', error);
    }
  }

  /**
   * Get Hugging Face API token
   */
  getHuggingFaceToken(): string | null {
    if (!this.initialized) {
      console.warn('🔐 API Configuration service not initialized, using environment token');
      const envToken = process.env.EXPO_PUBLIC_HUGGINGFACE_API_TOKEN || null;
      console.log(`🔐 Environment token available: ${!!envToken}`);
      return envToken;
    }

    const token = this.config.huggingFaceToken || null;
    console.log(`🔐 Retrieved Hugging Face token from config: ${!!token} (length: ${token?.length || 0})`);

    if (!token) {
      console.log('🔐 No token in config, trying environment fallback');
      const envToken = process.env.EXPO_PUBLIC_HUGGINGFACE_API_TOKEN || null;
      console.log(`🔐 Environment fallback token available: ${!!envToken}`);
      return envToken;
    }

    return token;
  }

  /**
   * Get Google Maps API key
   */
  getGoogleMapsApiKey(): string | null {
    if (!this.initialized) {
      console.warn('API Configuration service not initialized');
      return process.env.EXPO_PUBLIC_GOOGLE_MAPS_API_KEY || null;
    }
    return this.config.googleMapsApiKey || null;
  }

  /**
   * Update Hugging Face API token
   */
  async updateHuggingFaceToken(token: string): Promise<void> {
    this.config.huggingFaceToken = token;
    await this.saveConfig();
    console.log('🔐 Updated Hugging Face API token');
  }

  /**
   * Update Google Maps API key
   */
  async updateGoogleMapsApiKey(apiKey: string): Promise<void> {
    this.config.googleMapsApiKey = apiKey;
    await this.saveConfig();
    console.log('🔐 Updated Google Maps API key');
  }

  /**
   * Test API connectivity
   */
  async testAPIConnectivity(): Promise<{ huggingFace: boolean; googleMaps: boolean }> {
    const results = {
      huggingFace: false,
      googleMaps: false
    };

    // Test Hugging Face API
    const hfToken = this.getHuggingFaceToken();
    if (hfToken) {
      try {
        const response = await fetch('https://router.huggingface.co/hf-inference/models/sentence-transformers/all-MiniLM-L6-v2/pipeline/sentence-similarity', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${hfToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            inputs: {
              source_sentence: "test",
              sentences: ["test"]
            }
          })
        });
        
        results.huggingFace = response.ok;
        console.log(`🤖 Hugging Face API test: ${response.ok ? 'SUCCESS' : 'FAILED'}`);
      } catch (error) {
        console.log('🤖 Hugging Face API test failed:', error);
      }
    }

    // Test Google Maps API
    const gmapsKey = this.getGoogleMapsApiKey();
    if (gmapsKey) {
      try {
        const response = await fetch(`https://maps.googleapis.com/maps/api/geocode/json?address=test&key=${gmapsKey}`);
        results.googleMaps = response.ok;
        console.log(`🗺️ Google Maps API test: ${response.ok ? 'SUCCESS' : 'FAILED'}`);
      } catch (error) {
        console.log('🗺️ Google Maps API test failed:', error);
      }
    }

    return results;
  }

  /**
   * Get API usage statistics
   */
  getAPIUsageStats(): { huggingFace: boolean; googleMaps: boolean } {
    return {
      huggingFace: !!this.getHuggingFaceToken(),
      googleMaps: !!this.getGoogleMapsApiKey()
    };
  }

  /**
   * Clear all API configurations (for privacy/security)
   */
  async clearAllConfigurations(): Promise<void> {
    this.config = {};
    await SecureStorageService.removeItem(APIConfigService.CONFIG_KEY);
    console.log('🔐 Cleared all API configurations');
  }

  /**
   * Export configuration for backup (encrypted)
   */
  async exportConfiguration(): Promise<string | null> {
    try {
      const serialized = JSON.stringify(this.config);
      return EncryptionService.encrypt(serialized);
    } catch (error) {
      console.error('Error exporting configuration:', error);
      return null;
    }
  }

  /**
   * Import configuration from backup (encrypted)
   */
  async importConfiguration(encryptedConfig: string): Promise<boolean> {
    try {
      const decrypted = EncryptionService.decrypt(encryptedConfig);
      const importedConfig = JSON.parse(decrypted);
      
      // Validate the imported configuration
      if (this.validateConfig(importedConfig)) {
        this.config = importedConfig;
        await this.saveConfig();
        console.log('🔐 Successfully imported API configuration');
        return true;
      } else {
        console.error('Invalid configuration format');
        return false;
      }
    } catch (error) {
      console.error('Error importing configuration:', error);
      return false;
    }
  }

  /**
   * Validate configuration structure
   */
  private validateConfig(config: any): boolean {
    if (typeof config !== 'object' || config === null) {
      return false;
    }

    // Check if it has expected properties (optional)
    const validKeys = ['huggingFaceToken', 'googleMapsApiKey', 'openAIApiKey'];
    const configKeys = Object.keys(config);
    
    return configKeys.every(key => validKeys.includes(key));
  }

  /**
   * Get masked token for display purposes
   */
  getMaskedToken(tokenType: 'huggingFace' | 'googleMaps'): string {
    let token: string | null = null;
    
    switch (tokenType) {
      case 'huggingFace':
        token = this.getHuggingFaceToken();
        break;
      case 'googleMaps':
        token = this.getGoogleMapsApiKey();
        break;
    }

    if (!token) return 'Not configured';
    
    // Show first 4 and last 4 characters
    if (token.length > 8) {
      return `${token.substring(0, 4)}...${token.substring(token.length - 4)}`;
    }
    
    return '****';
  }

  /**
   * Check if all required APIs are configured
   */
  areRequiredAPIsConfigured(): boolean {
    return !!(this.getHuggingFaceToken() && this.getGoogleMapsApiKey());
  }
}

export const apiConfigService = new APIConfigService();
