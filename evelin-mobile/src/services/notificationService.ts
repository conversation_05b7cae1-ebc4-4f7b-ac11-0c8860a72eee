import { Task } from '../types/task';
import { Platform, Alert } from 'react-native';

// Check if we're running in Expo Go
const isExpoGo = __DEV__ && Platform.OS !== 'web';

// Conditionally import notifications
let Notifications: any = null;

if (!isExpoGo) {
  try {
    Notifications = require('expo-notifications');

    // Configure notification behavior only if not in Expo Go
    Notifications.setNotificationHandler({
      handleNotification: async () => ({
        shouldShowAlert: true,
        shouldPlaySound: true,
        shouldSetBadge: false,
        shouldShowBanner: true,
        shouldShowList: true,
      }),
    });
  } catch (error) {
    console.warn('⚠️ expo-notifications not available:', error);
  }
}

export class MobileNotificationService {
  private isNotificationSupported = true;

  // Request notification permissions
  async requestPermissions(): Promise<boolean> {
    try {
      // Check if notifications are supported (not in Expo Go for SDK 53+)
      if (isExpoGo || !Notifications) {
        console.warn('⚠️ Notifications not supported in Expo Go (SDK 53+). Use development build for full notification support.');
        this.isNotificationSupported = false;
        return false;
      }

      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.log('Notification permission not granted');
        return false;
      }

      // Configure notification channel for Android
      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('location-reminders', {
          name: 'Location Reminders',
          importance: Notifications.AndroidImportance.HIGH,
          vibrationPattern: [0, 250, 250, 250],
          lightColor: '#FF231F7C',
        });
      }

      this.isNotificationSupported = true;
      return true;
    } catch (error) {
      console.error('Error requesting notification permissions:', error);
      this.isNotificationSupported = false;

      // Show user-friendly message for Expo Go limitation
      if ((error as any)?.message?.includes('expo-notifications') || (error as any)?.message?.includes('remote notifications')) {
        console.warn('⚠️ Notifications disabled: Running in Expo Go. Use development build for notifications.');
      }

      return false;
    }
  }

  // Show location-based notification
  async showLocationNotification(task: Task, distance: number): Promise<void> {
    try {
      // Fallback for when notifications aren't supported
      if (!this.isNotificationSupported || !Notifications) {
        console.log('📍 Location notification (fallback):', {
          task: task.text,
          location: task.location,
          distance: `${Math.round(distance)}m`,
          message: 'Notifications not available in Expo Go'
        });
        return;
      }

      const distanceText = distance < 1000
        ? `${Math.round(distance)}m away`
        : `${(distance / 1000).toFixed(1)}km away`;

      const placeName = task.location || 'the location';
      const action = task.text.toLowerCase().includes('buy') ? 'buy' : 'complete your task';

      let body = `${placeName} is ${distanceText}`;

      if (task.text.toLowerCase().includes('buy')) {
        const item = task.text.replace(/buy\s+/i, '').replace(/\s+from.*/i, '');
        body += ` - you can go and buy ${item}`;
      } else {
        body += ` - you can go and ${action}`;
      }

      await Notifications.scheduleNotificationAsync({
        content: {
          title: `📍 ${placeName} Nearby!`,
          body: body,
          sound: true,
          priority: Notifications.AndroidNotificationPriority.HIGH,
          categoryIdentifier: 'location-reminder',
          data: {
            taskId: task.id,
            coordinates: task.coordinates,
            placeName: placeName,
          },
        },
        trigger: null, // Show immediately
      });

      console.log('✅ Mobile notification sent:', { placeName, distance, task: task.text });
    } catch (error) {
      console.error('Error showing notification:', error);

      // Fallback logging when notification fails
      console.log('📍 Location notification (error fallback):', {
        task: task.text,
        location: task.location,
        distance: `${Math.round(distance)}m`,
        error: (error as any)?.message || 'Unknown error'
      });
    }
  }

  // Show test notification
  async showTestNotification(): Promise<void> {
    try {
      if (!this.isNotificationSupported || !Notifications) {
        console.log('🧪 Test notification (fallback): Notifications not available in Expo Go');
        return;
      }

      await Notifications.scheduleNotificationAsync({
        content: {
          title: '🧪 Test Notification',
          body: 'This is a test notification from Evelin mobile app!',
          sound: true,
        },
        trigger: null,
      });
    } catch (error) {
      console.error('Error showing test notification:', error);
    }
  }

  // Set up notification response handler
  setupNotificationResponseHandler(onNotificationPress: (data: any) => void): void {
    try {
      if (!this.isNotificationSupported || !Notifications) {
        console.log('📱 Notification response handler not available in Expo Go');
        return;
      }

      Notifications.addNotificationResponseReceivedListener((response: any) => {
        const data = response.notification.request.content.data;
        onNotificationPress(data);
      });
    } catch (error) {
      console.error('Error setting up notification response handler:', error);
    }
  }

  // Cancel all notifications
  async cancelAllNotifications(): Promise<void> {
    try {
      if (!this.isNotificationSupported || !Notifications) {
        console.log('📱 Cancel notifications not available in Expo Go');
        return;
      }

      await Notifications.cancelAllScheduledNotificationsAsync();
    } catch (error) {
      console.error('Error canceling notifications:', error);
    }
  }

  // Send location notification (for background service compatibility)
  async sendLocationNotification(taskText: string, location: string, distance: number): Promise<void> {
    try {
      if (!this.isNotificationSupported || !Notifications) {
        console.log('📍 Background notification (fallback):', {
          task: taskText,
          location,
          distance: `${distance}m`,
          message: 'Notifications not available in Expo Go'
        });
        return;
      }

      await Notifications.scheduleNotificationAsync({
        content: {
          title: `📍 ${location} Nearby!`,
          body: `${location} is ${distance}m away - you can complete your task: ${taskText}`,
          sound: true,
          priority: Notifications.AndroidNotificationPriority.HIGH,
          categoryIdentifier: 'location-reminder',
        },
        trigger: null,
      });

      console.log('✅ Background notification sent:', { location, distance, task: taskText });
    } catch (error) {
      console.error('Error sending background notification:', error);

      // Fallback logging
      console.log('📍 Background notification (error fallback):', {
        task: taskText,
        location,
        distance: `${distance}m`,
        error: (error as any)?.message || 'Unknown error'
      });
    }
  }
}

export const mobileNotificationService = new MobileNotificationService();
