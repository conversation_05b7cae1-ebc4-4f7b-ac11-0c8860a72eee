import * as SecureStore from 'expo-secure-store';
import { Platform } from 'react-native';

/**
 * Secure Storage Service for managing sensitive data
 * Uses Expo SecureStore with proper error handling and fallbacks
 */
export class SecureStorageService {
  private static readonly KEYCHAIN_SERVICE = 'evelin-app-keychain';
  private static readonly SHARED_PREFS_NAME = 'evelin-secure-prefs';

  /**
   * Store a value securely
   */
  static async setItem(
    key: string,
    value: string,
    options?: {
      requireAuthentication?: boolean;
      authenticationPrompt?: string;
    }
  ): Promise<void> {
    try {
      // Check value size and warn if it's large
      const valueSize = new Blob([value]).size;
      if (valueSize > 2048) {
        console.warn(`⚠️ Large value being stored in SecureStore (${valueSize} bytes) for key: ${key}. Consider using alternative storage for large data.`);
      }

      const secureOptions: SecureStore.SecureStoreOptions = {
        keychainService: this.KEYCHAIN_SERVICE,
        sharedPreferencesName: this.SHARED_PREFS_NAME,
        requireAuthentication: options?.requireAuthentication || false,
        authenticationPrompt: options?.authenticationPrompt || 'Authenticate to access secure data',
      };

      await SecureStore.setItemAsync(key, value, secureOptions);
      console.log(`🔐 Securely stored item: ${key} (${valueSize} bytes)`);
    } catch (error) {
      console.error(`❌ Failed to store secure item ${key}:`, error);
      throw new SecureStorageError(`Failed to store ${key}`, error);
    }
  }

  /**
   * Retrieve a value securely
   */
  static async getItem(
    key: string,
    options?: {
      requireAuthentication?: boolean;
      authenticationPrompt?: string;
    }
  ): Promise<string | null> {
    try {
      const secureOptions: SecureStore.SecureStoreOptions = {
        keychainService: this.KEYCHAIN_SERVICE,
        sharedPreferencesName: this.SHARED_PREFS_NAME,
        requireAuthentication: options?.requireAuthentication || false,
        authenticationPrompt: options?.authenticationPrompt || 'Authenticate to access secure data',
      };

      const value = await SecureStore.getItemAsync(key, secureOptions);
      console.log(`🔐 Retrieved secure item: ${key} (${value ? 'found' : 'not found'})`);
      return value;
    } catch (error) {
      console.error(`❌ Failed to retrieve secure item ${key}:`, error);
      throw new SecureStorageError(`Failed to retrieve ${key}`, error);
    }
  }

  /**
   * Remove a value securely
   */
  static async removeItem(key: string): Promise<void> {
    try {
      await SecureStore.deleteItemAsync(key, {
        keychainService: this.KEYCHAIN_SERVICE,
        sharedPreferencesName: this.SHARED_PREFS_NAME,
      });
      console.log(`🗑️ Removed secure item: ${key}`);
    } catch (error) {
      console.error(`❌ Failed to remove secure item ${key}:`, error);
      throw new SecureStorageError(`Failed to remove ${key}`, error);
    }
  }

  /**
   * Check if SecureStore is available on the current platform
   */
  static async isAvailable(): Promise<boolean> {
    try {
      return await SecureStore.isAvailableAsync();
    } catch (error) {
      console.error('❌ Failed to check SecureStore availability:', error);
      return false;
    }
  }

  /**
   * Store JSON data securely
   */
  static async setJSON(
    key: string, 
    data: any, 
    options?: {
      requireAuthentication?: boolean;
      authenticationPrompt?: string;
    }
  ): Promise<void> {
    try {
      const jsonString = JSON.stringify(data);
      await this.setItem(key, jsonString, options);
    } catch (error) {
      console.error(`❌ Failed to store JSON data for ${key}:`, error);
      throw new SecureStorageError(`Failed to store JSON data for ${key}`, error);
    }
  }

  /**
   * Retrieve JSON data securely
   */
  static async getJSON<T = any>(
    key: string,
    options?: {
      requireAuthentication?: boolean;
      authenticationPrompt?: string;
    }
  ): Promise<T | null> {
    try {
      const jsonString = await this.getItem(key, options);
      if (!jsonString) return null;
      
      return JSON.parse(jsonString) as T;
    } catch (error) {
      console.error(`❌ Failed to retrieve JSON data for ${key}:`, error);
      throw new SecureStorageError(`Failed to retrieve JSON data for ${key}`, error);
    }
  }

  /**
   * Store multiple items securely
   */
  static async setMultiple(
    items: Array<{ key: string; value: string }>,
    options?: {
      requireAuthentication?: boolean;
      authenticationPrompt?: string;
    }
  ): Promise<void> {
    try {
      const promises = items.map(item => 
        this.setItem(item.key, item.value, options)
      );
      await Promise.all(promises);
      console.log(`🔐 Stored ${items.length} secure items`);
    } catch (error) {
      console.error('❌ Failed to store multiple secure items:', error);
      throw new SecureStorageError('Failed to store multiple items', error);
    }
  }

  /**
   * Retrieve multiple items securely
   */
  static async getMultiple(
    keys: string[],
    options?: {
      requireAuthentication?: boolean;
      authenticationPrompt?: string;
    }
  ): Promise<Array<{ key: string; value: string | null }>> {
    try {
      const promises = keys.map(async key => ({
        key,
        value: await this.getItem(key, options)
      }));
      
      const results = await Promise.all(promises);
      console.log(`🔐 Retrieved ${results.length} secure items`);
      return results;
    } catch (error) {
      console.error('❌ Failed to retrieve multiple secure items:', error);
      throw new SecureStorageError('Failed to retrieve multiple items', error);
    }
  }

  /**
   * Clear all secure storage (use with caution)
   */
  static async clearAll(): Promise<void> {
    try {
      // Note: SecureStore doesn't provide a clearAll method
      // This would need to be implemented by tracking keys
      console.warn('⚠️ clearAll() not implemented - SecureStore requires individual key deletion');
      throw new Error('clearAll() not supported by SecureStore');
    } catch (error) {
      console.error('❌ Failed to clear all secure items:', error);
      throw new SecureStorageError('Failed to clear all items', error);
    }
  }
}

/**
 * Custom error class for secure storage operations
 */
export class SecureStorageError extends Error {
  public readonly originalError?: any;

  constructor(message: string, originalError?: any) {
    super(message);
    this.name = 'SecureStorageError';
    this.originalError = originalError;
  }
}

/**
 * Predefined keys for common secure storage items
 */
export const SecureStorageKeys = {
  // API Keys and Tokens
  SUPABASE_URL: 'supabase_url',
  SUPABASE_ANON_KEY: 'supabase_anon_key',
  GOOGLE_MAPS_API_KEY: 'google_maps_api_key',
  
  // Authentication
  JWT_TOKEN: 'jwt_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_SESSION: 'user_session',
  
  // Encryption
  ENCRYPTION_KEY: 'encryption_key',
  
  // User Preferences (sensitive)
  USER_BIOMETRIC_SETTINGS: 'user_biometric_settings',
  USER_PRIVACY_SETTINGS: 'user_privacy_settings',
  
  // Device-specific
  DEVICE_ID: 'device_id',
  PUSH_TOKEN: 'push_token',
} as const;

/**
 * Utility functions for common secure storage operations
 */
export const SecureStorageUtils = {
  /**
   * Store API configuration securely
   */
  storeAPIConfig: async (config: {
    supabaseUrl?: string;
    supabaseAnonKey?: string;
    googleMapsApiKey?: string;
  }) => {
    const items = [];
    
    if (config.supabaseUrl) {
      items.push({ key: SecureStorageKeys.SUPABASE_URL, value: config.supabaseUrl });
    }
    if (config.supabaseAnonKey) {
      items.push({ key: SecureStorageKeys.SUPABASE_ANON_KEY, value: config.supabaseAnonKey });
    }
    if (config.googleMapsApiKey) {
      items.push({ key: SecureStorageKeys.GOOGLE_MAPS_API_KEY, value: config.googleMapsApiKey });
    }
    
    if (items.length > 0) {
      await SecureStorageService.setMultiple(items);
    }
  },

  /**
   * Retrieve API configuration securely
   */
  getAPIConfig: async (): Promise<{
    supabaseUrl?: string;
    supabaseAnonKey?: string;
    googleMapsApiKey?: string;
  }> => {
    const keys = [
      SecureStorageKeys.SUPABASE_URL,
      SecureStorageKeys.SUPABASE_ANON_KEY,
      SecureStorageKeys.GOOGLE_MAPS_API_KEY,
    ];
    
    const results = await SecureStorageService.getMultiple(keys);
    
    return {
      supabaseUrl: results.find(r => r.key === SecureStorageKeys.SUPABASE_URL)?.value || undefined,
      supabaseAnonKey: results.find(r => r.key === SecureStorageKeys.SUPABASE_ANON_KEY)?.value || undefined,
      googleMapsApiKey: results.find(r => r.key === SecureStorageKeys.GOOGLE_MAPS_API_KEY)?.value || undefined,
    };
  },

  /**
   * Store authentication tokens securely
   */
  storeAuthTokens: async (tokens: {
    jwtToken?: string;
    refreshToken?: string;
    userSession?: any;
  }) => {
    const items = [];
    
    if (tokens.jwtToken) {
      items.push({ key: SecureStorageKeys.JWT_TOKEN, value: tokens.jwtToken });
    }
    if (tokens.refreshToken) {
      items.push({ key: SecureStorageKeys.REFRESH_TOKEN, value: tokens.refreshToken });
    }
    if (tokens.userSession) {
      items.push({ key: SecureStorageKeys.USER_SESSION, value: JSON.stringify(tokens.userSession) });
    }
    
    if (items.length > 0) {
      await SecureStorageService.setMultiple(items, {
        requireAuthentication: false, // Don't require auth for app functionality
        authenticationPrompt: 'Authenticate to access your account'
      });
    }
  },

  /**
   * Retrieve authentication tokens securely
   */
  getAuthTokens: async (): Promise<{
    jwtToken?: string;
    refreshToken?: string;
    userSession?: any;
  }> => {
    const keys = [
      SecureStorageKeys.JWT_TOKEN,
      SecureStorageKeys.REFRESH_TOKEN,
      SecureStorageKeys.USER_SESSION,
    ];
    
    const results = await SecureStorageService.getMultiple(keys, {
      requireAuthentication: false,
      authenticationPrompt: 'Authenticate to access your account'
    });
    
    const userSessionString = results.find(r => r.key === SecureStorageKeys.USER_SESSION)?.value;
    let userSession;
    if (userSessionString) {
      try {
        userSession = JSON.parse(userSessionString);
      } catch (error) {
        console.error('Failed to parse user session:', error);
      }
    }
    
    return {
      jwtToken: results.find(r => r.key === SecureStorageKeys.JWT_TOKEN)?.value || undefined,
      refreshToken: results.find(r => r.key === SecureStorageKeys.REFRESH_TOKEN)?.value || undefined,
      userSession,
    };
  },

  /**
   * Clear authentication data
   */
  clearAuthData: async () => {
    const keys = [
      SecureStorageKeys.JWT_TOKEN,
      SecureStorageKeys.REFRESH_TOKEN,
      SecureStorageKeys.USER_SESSION,
    ];
    
    const promises = keys.map(key => SecureStorageService.removeItem(key));
    await Promise.all(promises);
  }
};
