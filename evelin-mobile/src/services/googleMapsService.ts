// Google Maps API integration for mobile app
import { SecureStorageService, SecureStorageKeys } from './secureStorageService';
import { apiConfigService } from './apiConfigService';

interface GooglePlacesResult {
  place_id: string;
  name: string;
  geometry: {
    location: {
      lat: number;
      lng: number;
    };
  };
  vicinity?: string;
  formatted_address?: string;
  types: string[];
  rating?: number;
  opening_hours?: {
    open_now: boolean;
  };
  business_status?: string;
}

interface GooglePlacesResponse {
  results: GooglePlacesResult[];
  status: string;
  next_page_token?: string;
}

interface PlaceSearchResult {
  name: string;
  address: string;
  coordinates: { lat: number; lng: number };
  placeId: string;
  rating?: number;
  isOpen?: boolean;
  distance: number;
}

export class MobileGoogleMapsService {
  private apiKey: string | null = null;
  private baseUrl = 'https://maps.googleapis.com/maps/api/place';

  constructor(apiKey?: string) {
    this.apiKey = apiKey || null;
  }

  /**
   * Initialize the service with secure API key
   */
  async initialize(): Promise<void> {
    if (!this.apiKey) {
      try {
        // First try environment variable (most reliable in Expo Go)
        const envApiKey = process.env.EXPO_PUBLIC_GOOGLE_MAPS_API_KEY;

        if (envApiKey) {
          this.apiKey = envApiKey;
          console.log('🗺️ Using Google Maps API key from environment');

          // Try to store it securely for future use (may fail in Expo Go)
          try {
            await SecureStorageService.setItem(SecureStorageKeys.GOOGLE_MAPS_API_KEY, envApiKey);
            console.log('🔐 Stored Google Maps API key securely');
          } catch (storageError) {
            console.warn('⚠️ Could not store API key securely (Expo Go limitation):', storageError);
          }
        } else {
          // Try to get API key from secure storage
          const storedApiKey = await SecureStorageService.getItem(SecureStorageKeys.GOOGLE_MAPS_API_KEY);

          if (storedApiKey) {
            this.apiKey = storedApiKey;
            console.log('🗺️ Using Google Maps API key from secure storage');
          } else {
            throw new Error('Google Maps API key not found in environment or secure storage');
          }
        }

        console.log('🗺️ Google Maps service initialized with secure API key');
      } catch (error) {
        console.error('❌ Failed to initialize Google Maps service:', error);
        throw error;
      }
    }
  }

  /**
   * Get the API key (ensures initialization)
   */
  private async getApiKey(): Promise<string> {
    if (!this.apiKey) {
      await this.initialize();
    }

    if (!this.apiKey) {
      throw new Error('Google Maps API key not available');
    }

    return this.apiKey;
  }

  /**
   * Search for nearby places using Google Places API
   */
  async searchNearbyPlaces(
    query: string,
    userLocation: { latitude: number; longitude: number },
    radius: number = 500
  ): Promise<PlaceSearchResult[]> {
    try {
      console.log(`🗺️ Searching for "${query}" within ${radius}m of`, userLocation);

      // Ensure API key is available
      const apiKey = await this.getApiKey();

      // Use Google Places Nearby Search API
      const url = `${this.baseUrl}/nearbysearch/json?` +
        `location=${userLocation.latitude},${userLocation.longitude}&` +
        `radius=${radius}&` +
        `keyword=${encodeURIComponent(query)}&` +
        `type=establishment&` +
        `key=${apiKey}`;

      console.log('🔗 API Request URL:', url.replace(apiKey, 'API_KEY_HIDDEN'));

      const response = await fetch(url);
      const data: GooglePlacesResponse = await response.json();

      console.log('📡 API Response status:', data.status);
      if (data.status === 'REQUEST_DENIED') {
        console.error('❌ REQUEST_DENIED - Possible issues:');
        console.error('   1. API key not enabled for Places API');
        console.error('   2. Billing not set up in Google Cloud');
        console.error('   3. API key has restrictions');
        console.error('   4. Quota exceeded');
      }

      if (data.status !== 'OK') {
        // Handle ZERO_RESULTS as normal case, not an error
        if (data.status === 'ZERO_RESULTS') {
          console.log(`📍 No results found for "${query}" within ${radius}m - this is normal`);
          return []; // Return empty array instead of throwing error
        }

        // Log actual API errors
        console.error('Google Places API error:', data.status);

        // Create detailed error message for actual API failures
        let errorMessage = 'Location search failed';
        switch (data.status) {
          case 'REQUEST_DENIED':
            errorMessage = 'Location search is currently unavailable. Please check your internet connection and try again.';
            break;
          case 'OVER_QUERY_LIMIT':
            errorMessage = 'Location search limit reached. Please try again later.';
            break;
          case 'INVALID_REQUEST':
            errorMessage = 'Invalid location search request. Please check the location name and try again.';
            break;
          default:
            errorMessage = `Location search failed (${data.status}). Please try again.`;
        }

        throw new Error(errorMessage);
      }

      console.log(`✅ Found ${data.results.length} places for "${query}"`);

      // Process and sort results by distance
      const results: PlaceSearchResult[] = data.results.map(place => {
        const distance = this.calculateDistance(
          userLocation.latitude,
          userLocation.longitude,
          place.geometry.location.lat,
          place.geometry.location.lng
        );

        return {
          name: place.name,
          address: place.vicinity || place.formatted_address || 'Address not available',
          coordinates: {
            lat: place.geometry.location.lat,
            lng: place.geometry.location.lng
          },
          placeId: place.place_id,
          rating: place.rating,
          isOpen: place.opening_hours?.open_now,
          distance: Math.round(distance)
        };
      });

      // Sort by distance (closest first)
      results.sort((a, b) => a.distance - b.distance);

      console.log('📍 Nearest places:', results.slice(0, 3).map(r => 
        `${r.name} (${r.distance}m) - ${r.address}`
      ));

      return results;

    } catch (error) {
      console.error('Error searching nearby places:', error);
      return [];
    }
  }

  /**
   * Search for a specific place by name and location
   */
  async findSpecificPlace(
    placeName: string,
    userLocation: { latitude: number; longitude: number },
    radius: number = 500
  ): Promise<PlaceSearchResult | null> {
    try {
      console.log(`🎯 Finding specific place: "${placeName}" within ${radius}m`);

      // AI-powered query correction for typos
      const correctedPlaceName = await this.correctQueryWithAI(placeName);
      const searchQuery = correctedPlaceName !== placeName ? correctedPlaceName : placeName;

      if (correctedPlaceName !== placeName) {
        console.log(`🤖 Using AI-corrected query: "${placeName}" → "${correctedPlaceName}"`);
      }

      // Try text search first (often works better than nearby search)
      console.log(`🔍 Trying text search first for: ${searchQuery}`);
      const textResults = await this.textSearch(`${searchQuery} near me`, userLocation, radius);

      if (textResults.length > 0) {
        // Validate that text search results match either the original or corrected query
        const matchingTextResults = [];
        for (const place of textResults) {
          // Try matching against original query first
          let isMatch = await this.isPlaceNameMatch(place.name, placeName);

          // If no match and we used a corrected query, try matching against corrected query
          if (!isMatch && correctedPlaceName !== placeName) {
            isMatch = await this.isPlaceNameMatch(place.name, correctedPlaceName);
            if (isMatch) {
              console.log(`🎯 Match found using corrected query: "${place.name}" matches "${correctedPlaceName}"`);
            }
          }

          if (isMatch) {
            matchingTextResults.push(place);
          }
        }

        if (matchingTextResults.length > 0) {
          const closest = matchingTextResults[0];
          console.log(`✅ Found via text search: ${closest.name} at ${closest.address} (${closest.distance}m)`);
          return closest;
        } else {
          console.log(`❌ Text search results don't match ${searchQuery}. Found: ${textResults.map(r => r.name).join(', ')}`);
        }
      }

      // Fallback to nearby search
      console.log(`🔍 Text search failed, trying nearby search...`);
      const nearbyResults = await this.searchNearbyPlaces(searchQuery, userLocation, radius);

      if (nearbyResults.length > 0) {
        // Validate that the found place matches either the original or corrected query
        const matchingResults = [];
        for (const place of nearbyResults) {
          // Try matching against original query first
          let isMatch = await this.isPlaceNameMatch(place.name, placeName);

          // If no match and we used a corrected query, try matching against corrected query
          if (!isMatch && correctedPlaceName !== placeName) {
            isMatch = await this.isPlaceNameMatch(place.name, correctedPlaceName);
            if (isMatch) {
              console.log(`🎯 Match found using corrected query: "${place.name}" matches "${correctedPlaceName}"`);
            }
          }

          if (isMatch) {
            matchingResults.push(place);
          }
        }

        if (matchingResults.length > 0) {
          const closest = matchingResults[0];
          console.log(`✅ Found closest ${placeName}: ${closest.name} at ${closest.address} (${closest.distance}m)`);
          return closest;
        } else {
          console.log(`❌ No matching ${placeName} found in nearby results. Found: ${nearbyResults.map(r => r.name).join(', ')}`);
        }
      }

      // No results found - return null instead of throwing error
      console.log(`❌ No real results found for: ${placeName}`);
      return null;

    } catch (error) {
      console.error(`Error finding ${placeName}:`, error);
      // Re-throw the error so it can be handled by the calling code
      if (error instanceof Error) {
        throw error;
      }
      throw new Error(`Failed to search for "${placeName}". Please check your internet connection and try again.`);
    }
  }

  /**
   * Text search for places
   */
  async textSearch(
    query: string,
    userLocation: { latitude: number; longitude: number },
    radius: number = 1000
  ): Promise<PlaceSearchResult[]> {
    try {
      // Ensure API key is available
      const apiKey = await this.getApiKey();

      const url = `${this.baseUrl}/textsearch/json?` +
        `query=${encodeURIComponent(query)}&` +
        `location=${userLocation.latitude},${userLocation.longitude}&` +
        `radius=${radius}&` +
        `key=${apiKey}`;

      console.log('🔗 Text Search URL:', url.replace(apiKey, 'API_KEY_HIDDEN'));

      const response = await fetch(url);
      const data: GooglePlacesResponse = await response.json();

      console.log('📡 Text Search Response status:', data.status);
      if (data.status === 'REQUEST_DENIED') {
        console.error('❌ TEXT SEARCH REQUEST_DENIED - API configuration issue');
      }

      if (data.status !== 'OK') {
        // Handle ZERO_RESULTS as normal case, not an error
        if (data.status === 'ZERO_RESULTS') {
          console.log(`📍 No text search results found for "${query}" - this is normal`);
          return []; // Return empty array instead of throwing error
        }

        // Log actual API errors
        console.error('Google Places Text Search error:', data.status);

        // Create detailed error message for actual API failures
        let errorMessage = 'Search failed';
        switch (data.status) {
          case 'REQUEST_DENIED':
            errorMessage = 'Location search is currently unavailable. Please check your internet connection and try again.';
            break;
          case 'OVER_QUERY_LIMIT':
            errorMessage = 'Search limit reached. Please try again later.';
            break;
          case 'INVALID_REQUEST':
            errorMessage = 'Invalid search request. Please check your search term and try again.';
            break;
          default:
            errorMessage = `Search failed (${data.status}). Please try again.`;
        }

        throw new Error(errorMessage);
      }

      // Process results
      const results: PlaceSearchResult[] = data.results.map(place => {
        const distance = this.calculateDistance(
          userLocation.latitude,
          userLocation.longitude,
          place.geometry.location.lat,
          place.geometry.location.lng
        );

        return {
          name: place.name,
          address: place.formatted_address || place.vicinity || 'Address not available',
          coordinates: {
            lat: place.geometry.location.lat,
            lng: place.geometry.location.lng
          },
          placeId: place.place_id,
          rating: place.rating,
          isOpen: place.opening_hours?.open_now,
          distance: Math.round(distance)
        };
      });

      // Sort by distance and filter by radius
      return results
        .filter(result => result.distance <= radius)
        .sort((a, b) => a.distance - b.distance);

    } catch (error) {
      console.error('Error in text search:', error);
      return [];
    }
  }

  /**
   * Calculate distance between two points in meters
   */
  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = lat1 * Math.PI / 180;
    const φ2 = lat2 * Math.PI / 180;
    const Δφ = (lat2 - lat1) * Math.PI / 180;
    const Δλ = (lon2 - lon1) * Math.PI / 180;

    const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c;
  }



  /**
   * Reverse geocode coordinates to get human-readable address
   */
  async reverseGeocode(
    latitude: number,
    longitude: number
  ): Promise<string | null> {
    try {
      console.log(`🔄 Reverse geocoding coordinates: ${latitude}, ${longitude}`);

      // Ensure API key is available
      const apiKey = await this.getApiKey();
      console.log('🔑 API Key length:', apiKey ? apiKey.length : 'null');
      console.log('🔑 API Key starts with:', apiKey ? apiKey.substring(0, 10) + '...' : 'null');

      const url = `https://maps.googleapis.com/maps/api/geocode/json?` +
        `latlng=${latitude},${longitude}&` +
        `key=${apiKey}`;

      console.log('🔗 Reverse Geocoding URL:', url.replace(apiKey, 'API_KEY_HIDDEN'));

      const response = await fetch(url);
      const data = await response.json();

      console.log('📡 Reverse Geocoding Response status:', data.status);
  

      if (data.status !== 'OK') {
        console.error('Google Reverse Geocoding error:', data.status);
        if (data.error_message) {
          console.error('Error message:', data.error_message);
        }

        // Provide specific error guidance
        switch (data.status) {
          case 'REQUEST_DENIED':
            console.error('❌ REQUEST_DENIED: Google Maps API key is invalid or Geocoding API is not enabled');
            console.error('📋 To fix this:');
            console.error('   1. Go to Google Cloud Console (https://console.cloud.google.com/)');
            console.error('   2. Enable the Geocoding API for your project');
            console.error('   3. Create a valid API key or check if your current key is correct');
            console.error('   4. Update EXPO_PUBLIC_GOOGLE_MAPS_API_KEY in your .env file');
            console.error('   5. Make sure billing is enabled for your Google Cloud project');
            break;
          case 'INVALID_REQUEST':
            console.error('❌ INVALID_REQUEST: Check request parameters');
            break;
          case 'OVER_QUERY_LIMIT':
            console.error('❌ OVER_QUERY_LIMIT: API quota exceeded');
            break;
          case 'ZERO_RESULTS':
            console.warn('⚠️ ZERO_RESULTS: No results found for coordinates');
            break;
          default:
            console.error('❌ Unknown error:', data.status);
        }
        return null;
      }

      if (data.results && data.results.length > 0) {
        // Get the most relevant address (usually the first result)
        const result = data.results[0];

        // Try to get a nice formatted address
        let locationName = result.formatted_address;

        // If we have address components, try to create a shorter, more user-friendly name
        if (result.address_components && result.address_components.length > 0) {
          const components = result.address_components;

          // Look for neighborhood, locality, or administrative area
          const neighborhood = components.find((c: any) => c.types.includes('neighborhood'))?.long_name;
          const locality = components.find((c: any) => c.types.includes('locality'))?.long_name;
          const adminArea = components.find((c: any) => c.types.includes('administrative_area_level_1'))?.long_name;
          const country = components.find((c: any) => c.types.includes('country'))?.long_name;

          // Create a concise location name
          if (neighborhood && locality) {
            locationName = `${neighborhood}, ${locality}`;
          } else if (locality && adminArea) {
            locationName = `${locality}, ${adminArea}`;
          } else if (locality && country) {
            locationName = `${locality}, ${country}`;
          }
        }

        console.log(`✅ Reverse geocoded to: ${locationName}`);
        return locationName;
      }

      console.log('❌ No results found for reverse geocoding');
      return null;

    } catch (error) {
      console.error('Error in reverse geocoding:', error);
      return null;
    }
  }

  /**
   * Check if a found place name matches the requested place name using AI-powered intelligent matching
   */
  private async isPlaceNameMatch(foundName: string, requestedName: string): Promise<boolean> {
    const found = foundName.toLowerCase().trim();
    const requested = requestedName.toLowerCase().trim();

    // Direct exact match
    if (found === requested) {
      return true;
    }

    // Direct substring match
    if (found.includes(requested) || requested.includes(found)) {
      return true;
    }

    // Use AI-powered intelligent matching
    return await this.aiPoweredLocationMatch(found, requested);
  }

  /**
   * AI-powered location matching using multiple intelligent approaches
   */
  private async aiPoweredLocationMatch(foundName: string, requestedName: string): Promise<boolean> {
    try {
      // 1. Semantic similarity using text embeddings
      const semanticMatch = await this.semanticSimilarityMatch(foundName, requestedName);
      if (semanticMatch) {
        console.log(`🤖 Semantic match found: "${requestedName}" ≈ "${foundName}"`);
        return true;
      }

      // 2. Multi-language brand recognition
      const brandMatch = await this.multilanguageBrandMatch(foundName, requestedName);
      if (brandMatch) {
        console.log(`🌍 Multi-language brand match: "${requestedName}" ≈ "${foundName}"`);
        return true;
      }

      // 3. Contextual fuzzy matching with business type awareness
      const contextualMatch = await this.contextualFuzzyMatch(foundName, requestedName);
      if (contextualMatch) {
        console.log(`🎯 Contextual match found: "${requestedName}" ≈ "${foundName}"`);
        return true;
      }

      // 4. Phonetic similarity (for voice input errors)
      const phoneticMatch = this.phoneticSimilarityMatch(foundName, requestedName);
      if (phoneticMatch) {
        console.log(`🔊 Phonetic match found: "${requestedName}" ≈ "${foundName}"`);
        return true;
      }

      return false;
    } catch (error) {
      console.error('Error in AI-powered location matching:', error);
      // Fallback to basic fuzzy matching
      return this.fuzzyMatch(foundName, requestedName, 0.7);
    }
  }

  /**
   * Semantic similarity matching using Hugging Face API
   */
  private async semanticSimilarityMatch(foundName: string, requestedName: string): Promise<boolean> {
    try {
      // Get secure Hugging Face API token
      const hfToken = apiConfigService.getHuggingFaceToken();
      if (!hfToken) {
        console.log('Hugging Face API token not available, using fallback');
        return false;
      }

      // Use Hugging Face sentence similarity API
      const response = await fetch('https://router.huggingface.co/hf-inference/models/sentence-transformers/all-MiniLM-L6-v2/pipeline/sentence-similarity', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${hfToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          inputs: {
            source_sentence: requestedName,
            sentences: [foundName]
          }
        })
      });

      if (response.ok) {
        const result = await response.json();
        const similarity = result[0];

        // Multi-tier similarity matching for better typo detection
        if (similarity > 0.75) {
          console.log(`🤖 High semantic similarity: ${similarity.toFixed(3)} between "${requestedName}" and "${foundName}"`);
          return true;
        } else if (similarity > 0.4) {
          // Medium similarity - check if it's likely a typo or abbreviation
          const isLikelyTypo = await this.isLikelyTypoMatch(requestedName, foundName, similarity);
          if (isLikelyTypo) {
            console.log(`🤖 Typo match detected: ${similarity.toFixed(3)} between "${requestedName}" and "${foundName}"`);
            return true;
          } else {
            console.log(`🤖 Medium semantic similarity: ${similarity.toFixed(3)} between "${requestedName}" and "${foundName}" - not a typo match`);
          }
        } else {
          console.log(`🤖 Low semantic similarity: ${similarity.toFixed(3)} between "${requestedName}" and "${foundName}"`);
        }
      } else {
        console.log('Hugging Face API request failed:', response.status);
      }
    } catch (error) {
      console.log('Semantic matching unavailable, using fallback:', error);
    }

    return false;
  }

  /**
   * AI-powered spell correction for location queries
   */
  private async correctQueryWithAI(query: string): Promise<string> {
    try {
      const hfToken = apiConfigService.getHuggingFaceToken();
      if (!hfToken) {
        console.log('🤖 Hugging Face API token not available, using fallback correction');
        return this.fallbackCorrection(query);
      }

      // Use T5 model for spell correction
      const response = await fetch('https://api-inference.huggingface.co/models/vennify/t5-base-grammar-correction', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${hfToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          inputs: `correct: ${query}`,
        }),
      });

      if (response.ok) {
        const result = await response.json();
        const corrected = result[0]?.generated_text?.replace('correct: ', '').trim() || query;

        if (corrected !== query) {
          console.log(`🤖 AI-corrected query: "${query}" → "${corrected}"`);
          return corrected;
        }
      } else {
        console.log('🤖 Hugging Face spell correction failed:', response.status);
      }
    } catch (error) {
      console.log('🤖 AI correction failed:', error);
    }

    // Fallback to rule-based correction
    return this.fallbackCorrection(query);
  }

  /**
   * Fallback correction using common patterns and phonetic matching
   */
  private fallbackCorrection(query: string): string {
    let corrected = query.toLowerCase();

    // Common phonetic corrections for store names
    const phoneticCorrections: { [key: string]: string } = {
      // Seven Eleven variations
      'sven': 'seven',
      'sevn': 'seven',
      'sevan': 'seven',
      'slben': 'eleven',
      'elben': 'eleven',
      'elevan': 'eleven',
      'elevn': 'eleven',

      // Walmart variations
      'walmat': 'walmart',
      'walmrt': 'walmart',
      'wallmart': 'walmart',

      // McDonald's variations
      'mcdonalds': 'mcdonalds',
      'mcdonald': 'mcdonalds',
      'macdonalds': 'mcdonalds',

      // Target variations
      'targt': 'target',
      'targit': 'target',

      // Costco variations
      'costko': 'costco',
      'cosco': 'costco',
    };

    // Apply phonetic corrections
    for (const [typo, correction] of Object.entries(phoneticCorrections)) {
      corrected = corrected.replace(new RegExp(typo, 'gi'), correction);
    }

    // Number to word conversions
    corrected = corrected.replace(/\b7\b/g, 'seven');
    corrected = corrected.replace(/\b11\b/g, 'eleven');
    corrected = corrected.replace(/\b24\b/g, 'twenty four');

    if (corrected !== query.toLowerCase()) {
      console.log(`🔧 Fallback corrected: "${query}" → "${corrected}"`);
    }

    return corrected;
  }

  /**
   * Enhanced typo detection with AI correction and phonetic matching
   */
  private async isLikelyTypoMatch(requested: string, found: string, similarity: number): Promise<boolean> {
    const foundClean = found.toLowerCase().replace(/[^\w\s]/g, '');

    // Extract core brand name from found location
    const foundCore = foundClean
      .replace(/\b(supercenter|supermarket|pharmacy|store|shop|market|express|plus|neighborhood|center)\b/g, '')
      .trim();

    // AI-based correction for requested query
    const correctedRequested = await this.correctQueryWithAI(requested);
    const correctedClean = correctedRequested.toLowerCase().replace(/[^\w\s]/g, '');

    // Calculate edit distance between corrected query and found location
    const editDistance = this.calculateEditDistance(correctedClean, foundCore);
    const maxLength = Math.max(correctedClean.length, foundCore.length);
    const editSimilarity = maxLength > 0 ? 1 - (editDistance / maxLength) : 0;

    // Phonetic matching using Soundex
    const phoneticMatch = this.calculateSoundexSimilarity(correctedClean, foundCore);

    // Length difference
    const lengthDiff = Math.abs(correctedClean.length - foundCore.length);

    // Relaxed thresholds for short strings (store names)
    const isShortString = maxLength <= 15;
    const editThreshold = isShortString ? 0.5 : 0.7;
    const semanticThreshold = isShortString ? 0.2 : 0.4;
    const lengthThreshold = isShortString ? 4 : 2;

    // Multi-criteria typo detection
    const isTypo = (
      editSimilarity > editThreshold ||
      phoneticMatch > 0.7 ||
      (similarity > semanticThreshold && lengthDiff <= lengthThreshold)
    );

    if (isTypo) {
      console.log(`🔍 Enhanced typo analysis: "${correctedClean}" vs "${foundCore}"`);
      console.log(`   Edit: ${editSimilarity.toFixed(3)}, Phonetic: ${phoneticMatch.toFixed(3)}, Semantic: ${similarity.toFixed(3)}, Length diff: ${lengthDiff}`);
    }

    return isTypo;
  }

  /**
   * Calculate phonetic similarity using Soundex algorithm
   */
  private calculateSoundexSimilarity(str1: string, str2: string): number {
    const soundex1 = this.soundex(str1);
    const soundex2 = this.soundex(str2);

    if (soundex1 === soundex2) return 1.0;

    // Calculate similarity between soundex codes
    const editDistance = this.calculateEditDistance(soundex1, soundex2);
    return 1 - (editDistance / Math.max(soundex1.length, soundex2.length));
  }

  /**
   * Generate Soundex code for phonetic matching
   */
  private soundex(str: string): string {
    const map: { [key: string]: string } = {
      b: '1', f: '1', p: '1', v: '1',
      c: '2', g: '2', j: '2', k: '2', q: '2', s: '2', x: '2', z: '2',
      d: '3', t: '3',
      l: '4',
      m: '5', n: '5',
      r: '6'
    };

    const clean = str.toLowerCase().replace(/[^a-z]/g, '');
    if (!clean) return '0000';

    let code = clean[0].toUpperCase();
    let prev = '';

    for (let i = 1; i < clean.length && code.length < 4; i++) {
      const char = clean[i];
      const mapped = map[char] || '';
      if (mapped && mapped !== prev) {
        code += mapped;
        prev = mapped;
      }
    }

    return code.padEnd(4, '0');
  }

  /**
   * Calculate Levenshtein distance between two strings
   */
  private calculateEditDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i;
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j;

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,     // deletion
          matrix[j - 1][i] + 1,     // insertion
          matrix[j - 1][i - 1] + indicator // substitution
        );
      }
    }

    return matrix[str2.length][str1.length];
  }

  /**
   * Multi-language brand recognition using intelligent text processing
   */
  private async multilanguageBrandMatch(foundName: string, requestedName: string): Promise<boolean> {
    // Normalize text for multi-language comparison
    const normalizeText = (text: string): string => {
      return text
        .toLowerCase()
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '') // Remove diacritics
        .replace(/[^\w\s]/g, ' ') // Replace special chars with spaces
        .replace(/\s+/g, ' ') // Normalize whitespace
        .trim();
    };

    const normalizedFound = normalizeText(foundName);
    const normalizedRequested = normalizeText(requestedName);

    // Check for common brand transformations
    const brandTransformations = [
      // Number to word transformations
      { pattern: /\b7\b/g, replacement: 'seven' },
      { pattern: /\b11\b/g, replacement: 'eleven' },
      { pattern: /\b24\b/g, replacement: 'twenty four' },
      { pattern: /\b&\b/g, replacement: 'and' },
      { pattern: /\bst\b/g, replacement: 'saint' },
      { pattern: /\bmt\b/g, replacement: 'mount' },

      // Common abbreviations
      { pattern: /\bco\b/g, replacement: 'company' },
      { pattern: /\binc\b/g, replacement: 'incorporated' },
      { pattern: /\bltd\b/g, replacement: 'limited' },
      { pattern: /\bcorp\b/g, replacement: 'corporation' },

      // Store type variations
      { pattern: /\bstore\b/g, replacement: '' },
      { pattern: /\bshop\b/g, replacement: '' },
      { pattern: /\bmarket\b/g, replacement: '' },
      { pattern: /\bsupermarket\b/g, replacement: '' },
      { pattern: /\bpharmacy\b/g, replacement: '' },
    ];

    // Apply transformations to both strings
    let transformedFound = normalizedFound;
    let transformedRequested = normalizedRequested;

    for (const { pattern, replacement } of brandTransformations) {
      transformedFound = transformedFound.replace(pattern, replacement);
      transformedRequested = transformedRequested.replace(pattern, replacement);
    }

    // Clean up extra spaces
    transformedFound = transformedFound.replace(/\s+/g, ' ').trim();
    transformedRequested = transformedRequested.replace(/\s+/g, ' ').trim();

    // Check if transformed versions match
    if (transformedFound === transformedRequested) {
      return true;
    }

    // Check if one contains the other after transformation
    if (transformedFound.includes(transformedRequested) || transformedRequested.includes(transformedFound)) {
      return true;
    }

    // Use fuzzy matching on transformed text
    return this.fuzzyMatch(transformedFound, transformedRequested, 0.85);
  }

  /**
   * Contextual fuzzy matching with business type awareness
   */
  private async contextualFuzzyMatch(foundName: string, requestedName: string): Promise<boolean> {
    // Extract business type indicators
    const businessTypes = ['store', 'shop', 'market', 'pharmacy', 'restaurant', 'cafe', 'gas', 'station', 'center', 'mall'];

    const extractBusinessName = (text: string): string => {
      let cleanText = text.toLowerCase();

      // Remove common business suffixes/prefixes
      for (const type of businessTypes) {
        cleanText = cleanText.replace(new RegExp(`\\b${type}\\b`, 'g'), '');
      }

      return cleanText.replace(/\s+/g, ' ').trim();
    };

    const cleanFound = extractBusinessName(foundName);
    const cleanRequested = extractBusinessName(requestedName);

    // Use more lenient fuzzy matching for business names
    return this.fuzzyMatch(cleanFound, cleanRequested, 0.7);
  }

  /**
   * Phonetic similarity matching for voice input errors
   */
  private phoneticSimilarityMatch(foundName: string, requestedName: string): boolean {
    // Simple phonetic matching using Soundex-like algorithm
    const phoneticCode = (str: string): string => {
      const text = str.toLowerCase().replace(/[^a-z]/g, '');
      if (text.length === 0) return '';

      let code = text[0];
      const consonantMap: { [key: string]: string } = {
        'b': '1', 'f': '1', 'p': '1', 'v': '1',
        'c': '2', 'g': '2', 'j': '2', 'k': '2', 'q': '2', 's': '2', 'x': '2', 'z': '2',
        'd': '3', 't': '3',
        'l': '4',
        'm': '5', 'n': '5',
        'r': '6'
      };

      for (let i = 1; i < text.length && code.length < 4; i++) {
        const char = text[i];
        const phoneticChar = consonantMap[char];
        if (phoneticChar && phoneticChar !== code[code.length - 1]) {
          code += phoneticChar;
        }
      }

      return code.padEnd(4, '0').substring(0, 4);
    };

    const foundPhonetic = phoneticCode(foundName);
    const requestedPhonetic = phoneticCode(requestedName);

    return foundPhonetic === requestedPhonetic && foundPhonetic.length > 1;
  }

  /**
   * Fuzzy string matching using Levenshtein distance
   */
  private fuzzyMatch(str1: string, str2: string, threshold: number = 0.8): boolean {
    if (str1 === str2) return true;

    const distance = this.levenshteinDistance(str1, str2);
    const maxLength = Math.max(str1.length, str2.length);
    const similarity = 1 - (distance / maxLength);

    return similarity >= threshold;
  }

  /**
   * Calculate Levenshtein distance between two strings
   */
  private levenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null));

    for (let i = 0; i <= str1.length; i++) {
      matrix[0][i] = i;
    }

    for (let j = 0; j <= str2.length; j++) {
      matrix[j][0] = j;
    }

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1;
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1, // deletion
          matrix[j - 1][i] + 1, // insertion
          matrix[j - 1][i - 1] + indicator // substitution
        );
      }
    }

    return matrix[str2.length][str1.length];
  }



  /**
   * Get navigation URL for Google Maps
   */
  getNavigationUrl(destination: { lat: number; lng: number }): string {
    const destinationParam = `${destination.lat},${destination.lng}`;
    return `https://www.google.com/maps/dir/?api=1&destination=${destinationParam}&travelmode=walking`;
  }
}

// Create singleton instance with secure API key management
export const mobileGoogleMapsService = new MobileGoogleMapsService();

// Initialize the service (should be called during app startup)
export const initializeGoogleMapsService = async (): Promise<void> => {
  await mobileGoogleMapsService.initialize();
};
