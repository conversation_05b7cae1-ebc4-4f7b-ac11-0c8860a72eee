import NetInfo, { NetInfoState } from '@react-native-community/netinfo';
import { EventEmitter } from '../utils/EventEmitter';

export interface NetworkState {
  isConnected: boolean;
  isInternetReachable: boolean;
  type: string;
  isWifiEnabled?: boolean;
  strength?: number;
  lastConnectedAt?: string;
  connectionQuality: 'excellent' | 'good' | 'fair' | 'poor' | 'offline';
}

export interface NetworkMetrics {
  latency: number;
  downloadSpeed: number;
  uploadSpeed: number;
  packetLoss: number;
  timestamp: string;
}

class NetworkService extends EventEmitter {
  private currentState: NetworkState = {
    isConnected: false,
    isInternetReachable: false,
    type: 'unknown',
    connectionQuality: 'offline',
  };

  private metrics: NetworkMetrics[] = [];
  private unsubscribe: (() => void) | null = null;
  private connectionTestInterval: NodeJS.Timeout | null = null;
  private isInitialized = false;

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Get initial network state
      const state = await NetInfo.fetch();
      this.updateNetworkState(state);

      // Subscribe to network state changes
      this.unsubscribe = NetInfo.addEventListener((state) => {
        this.updateNetworkState(state);
      });

      // Start periodic connection quality tests
      this.startConnectionQualityMonitoring();

      this.isInitialized = true;
      console.log('✅ Network service initialized');
    } catch (error) {
      console.error('❌ Failed to initialize network service:', error);
    }
  }

  private updateNetworkState(netInfoState: NetInfoState): void {
    const wasConnected = this.currentState.isConnected;
    
    this.currentState = {
      isConnected: netInfoState.isConnected ?? false,
      isInternetReachable: netInfoState.isInternetReachable ?? false,
      type: netInfoState.type,
      isWifiEnabled: netInfoState.isWifiEnabled,
      connectionQuality: this.determineConnectionQuality(netInfoState),
      lastConnectedAt: netInfoState.isConnected ? new Date().toISOString() : this.currentState.lastConnectedAt,
    };

    // Emit events for state changes
    this.emit('networkStateChanged', this.currentState);

    if (!wasConnected && this.currentState.isConnected) {
      console.log('🌐 Network connection restored');
      this.emit('connectionRestored', this.currentState);
    } else if (wasConnected && !this.currentState.isConnected) {
      console.log('📵 Network connection lost');
      this.emit('connectionLost', this.currentState);
    }
  }

  private determineConnectionQuality(state: NetInfoState): NetworkState['connectionQuality'] {
    if (!state.isConnected) return 'offline';

    // For cellular connections, use signal strength
    if (state.type === 'cellular' && state.details) {
      const details = state.details as any;
      if (details.strength !== undefined) {
        if (details.strength >= 4) return 'excellent';
        if (details.strength >= 3) return 'good';
        if (details.strength >= 2) return 'fair';
        return 'poor';
      }
    }

    // For WiFi, assume good quality if connected
    if (state.type === 'wifi') return 'good';

    // Default to fair for other connection types
    return 'fair';
  }

  private startConnectionQualityMonitoring(): void {
    // Test connection quality every 30 seconds when connected
    this.connectionTestInterval = setInterval(async () => {
      if (this.currentState.isConnected) {
        await this.measureConnectionQuality();
      }
    }, 30000);
  }

  private async measureConnectionQuality(): Promise<void> {
    try {
      const startTime = Date.now();

      // Simple ping test to measure latency - use a more reliable endpoint
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      const response = await fetch('https://httpbin.org/status/200', {
        method: 'HEAD',
        cache: 'no-cache',
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      const latency = Date.now() - startTime;

      const metrics: NetworkMetrics = {
        latency,
        downloadSpeed: 0, // Would need more complex test for actual speed
        uploadSpeed: 0,
        packetLoss: response.ok ? 0 : 1,
        timestamp: new Date().toISOString(),
      };

      this.metrics.push(metrics);

      // Keep only last 10 measurements
      if (this.metrics.length > 10) {
        this.metrics = this.metrics.slice(-10);
      }

      this.emit('metricsUpdated', metrics);
    } catch (error) {
      // Only log in development, reduce noise in production
      if (__DEV__) {
        console.warn('Connection quality test failed:', error);
      }
    }
  }

  // Public methods
  getNetworkState(): NetworkState {
    return { ...this.currentState };
  }

  isOnline(): boolean {
    return this.currentState.isConnected && this.currentState.isInternetReachable;
  }

  isOffline(): boolean {
    return !this.isOnline();
  }

  getConnectionQuality(): NetworkState['connectionQuality'] {
    return this.currentState.connectionQuality;
  }

  getLatestMetrics(): NetworkMetrics | null {
    return this.metrics.length > 0 ? this.metrics[this.metrics.length - 1] : null;
  }

  getAverageLatency(): number {
    if (this.metrics.length === 0) return 0;
    const sum = this.metrics.reduce((acc, metric) => acc + metric.latency, 0);
    return sum / this.metrics.length;
  }

  // Test if a specific endpoint is reachable
  async testEndpoint(url: string, timeout: number = 5000): Promise<boolean> {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);

      const response = await fetch(url, {
        method: 'HEAD',
        signal: controller.signal,
        cache: 'no-cache',
      });

      clearTimeout(timeoutId);
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  // Wait for network connection
  async waitForConnection(timeout: number = 30000): Promise<boolean> {
    if (this.isOnline()) return true;

    return new Promise((resolve) => {
      const timeoutId = setTimeout(() => {
        this.off('connectionRestored', onConnectionRestored);
        resolve(false);
      }, timeout);

      const onConnectionRestored = () => {
        clearTimeout(timeoutId);
        this.off('connectionRestored', onConnectionRestored);
        resolve(true);
      };

      this.on('connectionRestored', onConnectionRestored);
    });
  }

  destroy(): void {
    if (this.unsubscribe) {
      this.unsubscribe();
      this.unsubscribe = null;
    }

    if (this.connectionTestInterval) {
      clearInterval(this.connectionTestInterval);
      this.connectionTestInterval = null;
    }

    this.removeAllListeners();
    this.isInitialized = false;
  }
}

export const networkService = new NetworkService();
