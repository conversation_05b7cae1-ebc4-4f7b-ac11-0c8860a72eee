import AsyncStorage from '@react-native-async-storage/async-storage';
import { Task } from '../types/task';
import { EventEmitter } from '../utils/EventEmitter';

export type ConflictResolutionStrategy = 
  | 'client-wins' 
  | 'server-wins' 
  | 'merge' 
  | 'timestamp-based' 
  | 'user-choice'
  | 'field-level-merge'
  | 'priority-based';

export interface ConflictData {
  id: string;
  type: 'task' | 'settings' | 'user-data';
  entityId: string;
  localData: any;
  serverData: any;
  conflictFields: string[];
  timestamp: string;
  strategy?: ConflictResolutionStrategy;
  resolution?: any;
  status: 'pending' | 'resolved' | 'failed' | 'user-required';
  metadata?: {
    localTimestamp?: string;
    serverTimestamp?: string;
    priority?: 'low' | 'medium' | 'high' | 'critical';
    userContext?: string;
    autoResolvable?: boolean;
    confidence?: number; // 0-1 confidence in auto-resolution
  };
}

export interface ConflictResolutionResult {
  success: boolean;
  resolvedData?: any;
  strategy: ConflictResolutionStrategy;
  confidence: number;
  requiresUserInput: boolean;
  error?: string;
  metadata?: {
    fieldsChanged: string[];
    mergeStrategy: string;
    fallbackUsed: boolean;
  };
}

export interface MergeRule {
  field: string;
  strategy: 'latest' | 'longest' | 'truthy' | 'numeric-max' | 'numeric-min' | 'array-union' | 'custom';
  customResolver?: (local: any, server: any) => any;
  priority?: 'local' | 'server';
}

class ConflictResolutionService extends EventEmitter {
  private readonly CONFLICTS_KEY = 'pending_conflicts';
  private readonly RESOLUTION_HISTORY_KEY = 'resolution_history';
  private readonly USER_PREFERENCES_KEY = 'conflict_preferences';
  
  private pendingConflicts: ConflictData[] = [];
  private resolutionHistory: Array<{id: string, strategy: ConflictResolutionStrategy, timestamp: string}> = [];
  private userPreferences: Map<string, ConflictResolutionStrategy> = new Map();

  // Default merge rules for different entity types
  private taskMergeRules: MergeRule[] = [
    { field: 'text', strategy: 'longest' },
    { field: 'completed', strategy: 'truthy', priority: 'local' },
    { field: 'location', strategy: 'latest' },
    { field: 'coordinates', strategy: 'latest' },
    { field: 'notificationDistance', strategy: 'numeric-max' },
    { field: 'notificationTriggered', strategy: 'truthy', priority: 'local' },
    { field: 'category', strategy: 'latest' },
  ];

  async initialize(): Promise<void> {
    try {
      await this.loadPendingConflicts();
      await this.loadResolutionHistory();
      await this.loadUserPreferences();
      console.log('✅ Conflict resolution service initialized');
    } catch (error) {
      console.error('❌ Failed to initialize conflict resolution service:', error);
    }
  }

  async detectConflicts(localData: any[], serverData: any[], entityType: string): Promise<ConflictData[]> {
    const conflicts: ConflictData[] = [];
    
    for (const localItem of localData) {
      const serverItem = serverData.find(item => item.id === localItem.id);
      
      if (serverItem) {
        const conflictFields = this.findConflictingFields(localItem, serverItem, entityType);
        
        if (conflictFields.length > 0) {
          const conflict: ConflictData = {
            id: this.generateConflictId(),
            type: entityType as any,
            entityId: localItem.id,
            localData: localItem,
            serverData: serverItem,
            conflictFields,
            timestamp: new Date().toISOString(),
            status: 'pending',
            metadata: {
              localTimestamp: localItem.updatedAt || localItem.createdAt,
              serverTimestamp: serverItem.updatedAt || serverItem.createdAt,
              autoResolvable: this.isAutoResolvable(conflictFields, entityType),
              confidence: this.calculateResolutionConfidence(localItem, serverItem, conflictFields),
            },
          };
          
          conflicts.push(conflict);
        }
      }
    }
    
    return conflicts;
  }

  async resolveConflict(conflict: ConflictData, strategy?: ConflictResolutionStrategy): Promise<ConflictResolutionResult> {
    const resolveStrategy = strategy || this.determineOptimalStrategy(conflict);
    
    try {
      let resolvedData: any;
      let confidence = 0.8;
      let requiresUserInput = false;
      
      switch (resolveStrategy) {
        case 'client-wins':
          resolvedData = conflict.localData;
          confidence = 0.7;
          break;
          
        case 'server-wins':
          resolvedData = conflict.serverData;
          confidence = 0.7;
          break;
          
        case 'timestamp-based':
          resolvedData = this.resolveByTimestamp(conflict);
          confidence = 0.9;
          break;
          
        case 'merge':
        case 'field-level-merge':
          const mergeResult = await this.performFieldLevelMerge(conflict);
          resolvedData = mergeResult.data;
          confidence = mergeResult.confidence;
          break;
          
        case 'priority-based':
          resolvedData = this.resolveByPriority(conflict);
          confidence = 0.8;
          break;
          
        case 'user-choice':
          requiresUserInput = true;
          confidence = 1.0;
          break;
          
        default:
          throw new Error(`Unknown resolution strategy: ${resolveStrategy}`);
      }
      
      if (!requiresUserInput) {
        conflict.resolution = resolvedData;
        conflict.strategy = resolveStrategy;
        conflict.status = 'resolved';
        
        // Save resolution history
        this.resolutionHistory.push({
          id: conflict.id,
          strategy: resolveStrategy,
          timestamp: new Date().toISOString(),
        });
        
        await this.saveResolutionHistory();
      } else {
        conflict.status = 'user-required';
      }
      
      await this.savePendingConflicts();
      
      const result: ConflictResolutionResult = {
        success: true,
        resolvedData: requiresUserInput ? undefined : resolvedData,
        strategy: resolveStrategy,
        confidence,
        requiresUserInput,
        metadata: {
          fieldsChanged: conflict.conflictFields,
          mergeStrategy: resolveStrategy,
          fallbackUsed: false,
        },
      };
      
      this.emit('conflictResolved', { conflict, result });
      return result;
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      conflict.status = 'failed';
      await this.savePendingConflicts();
      
      return {
        success: false,
        strategy: resolveStrategy,
        confidence: 0,
        requiresUserInput: false,
        error: errorMessage,
      };
    }
  }

  private findConflictingFields(local: any, server: any, entityType: string): string[] {
    const conflictFields: string[] = [];
    const fieldsToCheck = this.getFieldsToCheck(entityType);
    
    for (const field of fieldsToCheck) {
      if (this.valuesConflict(local[field], server[field])) {
        conflictFields.push(field);
      }
    }
    
    return conflictFields;
  }

  private valuesConflict(localValue: any, serverValue: any): boolean {
    // Handle null/undefined
    if (localValue == null && serverValue == null) return false;
    if (localValue == null || serverValue == null) return true;
    
    // Handle objects (like coordinates)
    if (typeof localValue === 'object' && typeof serverValue === 'object') {
      return JSON.stringify(localValue) !== JSON.stringify(serverValue);
    }
    
    // Handle primitives
    return localValue !== serverValue;
  }

  private getFieldsToCheck(entityType: string): string[] {
    switch (entityType) {
      case 'task':
        return ['text', 'completed', 'location', 'coordinates', 'notificationDistance', 'notificationTriggered', 'category'];
      case 'settings':
        return ['theme', 'notifications', 'defaultDistance', 'language'];
      default:
        return Object.keys({});
    }
  }

  private isAutoResolvable(conflictFields: string[], entityType: string): boolean {
    // Simple heuristic: conflicts in non-critical fields are auto-resolvable
    const criticalFields = entityType === 'task' ? ['text', 'completed'] : [];
    return !conflictFields.some(field => criticalFields.includes(field));
  }

  private calculateResolutionConfidence(local: any, server: any, conflictFields: string[]): number {
    // Base confidence
    let confidence = 0.5;
    
    // Increase confidence if timestamps are clear
    const localTime = new Date(local.updatedAt || local.createdAt).getTime();
    const serverTime = new Date(server.updatedAt || server.createdAt).getTime();
    const timeDiff = Math.abs(localTime - serverTime);
    
    if (timeDiff > 60000) { // More than 1 minute difference
      confidence += 0.3;
    }
    
    // Decrease confidence for more conflicting fields
    confidence -= (conflictFields.length - 1) * 0.1;
    
    return Math.max(0.1, Math.min(1.0, confidence));
  }

  private determineOptimalStrategy(conflict: ConflictData): ConflictResolutionStrategy {
    // Check user preferences first
    const userPref = this.userPreferences.get(conflict.type);
    if (userPref) return userPref;
    
    // Use metadata to determine best strategy
    if (conflict.metadata?.autoResolvable && conflict.metadata.confidence && conflict.metadata.confidence > 0.8) {
      return 'timestamp-based';
    }
    
    if (conflict.conflictFields.length === 1) {
      return 'field-level-merge';
    }
    
    if (conflict.metadata?.confidence && conflict.metadata.confidence > 0.7) {
      return 'merge';
    }
    
    return 'user-choice';
  }

  private resolveByTimestamp(conflict: ConflictData): any {
    const localTime = new Date(conflict.metadata?.localTimestamp || 0).getTime();
    const serverTime = new Date(conflict.metadata?.serverTimestamp || 0).getTime();
    
    return localTime > serverTime ? conflict.localData : conflict.serverData;
  }

  private async performFieldLevelMerge(conflict: ConflictData): Promise<{data: any, confidence: number}> {
    const merged = { ...conflict.serverData }; // Start with server data as base
    const rules = this.getMergeRules(conflict.type);
    let confidence = 0.8;
    
    for (const field of conflict.conflictFields) {
      const rule = rules.find(r => r.field === field);
      const localValue = conflict.localData[field];
      const serverValue = conflict.serverData[field];
      
      if (rule) {
        merged[field] = this.applyMergeRule(rule, localValue, serverValue);
      } else {
        // Default: use latest timestamp
        const localTime = new Date(conflict.metadata?.localTimestamp || 0).getTime();
        const serverTime = new Date(conflict.metadata?.serverTimestamp || 0).getTime();
        merged[field] = localTime > serverTime ? localValue : serverValue;
        confidence -= 0.1; // Reduce confidence for default resolution
      }
    }
    
    return { data: merged, confidence: Math.max(0.1, confidence) };
  }

  private getMergeRules(entityType: string): MergeRule[] {
    switch (entityType) {
      case 'task':
        return this.taskMergeRules;
      default:
        return [];
    }
  }

  private applyMergeRule(rule: MergeRule, localValue: any, serverValue: any): any {
    switch (rule.strategy) {
      case 'latest':
        // This would need timestamp comparison - simplified here
        return rule.priority === 'local' ? localValue : serverValue;
      case 'longest':
        return (localValue?.length || 0) > (serverValue?.length || 0) ? localValue : serverValue;
      case 'truthy':
        const truthyValue = localValue || serverValue;
        return rule.priority === 'local' ? (localValue || serverValue) : (serverValue || localValue);
      case 'numeric-max':
        return Math.max(Number(localValue) || 0, Number(serverValue) || 0);
      case 'numeric-min':
        return Math.min(Number(localValue) || Infinity, Number(serverValue) || Infinity);
      case 'array-union':
        return [...new Set([...(localValue || []), ...(serverValue || [])])];
      case 'custom':
        return rule.customResolver ? rule.customResolver(localValue, serverValue) : serverValue;
      default:
        return serverValue;
    }
  }

  private resolveByPriority(conflict: ConflictData): any {
    // Simple priority: completed tasks from local, others from server
    if (conflict.type === 'task' && conflict.localData.completed) {
      return conflict.localData;
    }
    return conflict.serverData;
  }

  private generateConflictId(): string {
    return `conflict-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  // Storage methods
  private async savePendingConflicts(): Promise<void> {
    try {
      await AsyncStorage.setItem(this.CONFLICTS_KEY, JSON.stringify(this.pendingConflicts));
    } catch (error) {
      console.error('Failed to save pending conflicts:', error);
    }
  }

  private async loadPendingConflicts(): Promise<void> {
    try {
      const data = await AsyncStorage.getItem(this.CONFLICTS_KEY);
      if (data) {
        this.pendingConflicts = JSON.parse(data);
      }
    } catch (error) {
      console.error('Failed to load pending conflicts:', error);
      this.pendingConflicts = [];
    }
  }

  private async saveResolutionHistory(): Promise<void> {
    try {
      // Keep only last 100 entries
      const trimmedHistory = this.resolutionHistory.slice(-100);
      await AsyncStorage.setItem(this.RESOLUTION_HISTORY_KEY, JSON.stringify(trimmedHistory));
    } catch (error) {
      console.error('Failed to save resolution history:', error);
    }
  }

  private async loadResolutionHistory(): Promise<void> {
    try {
      const data = await AsyncStorage.getItem(this.RESOLUTION_HISTORY_KEY);
      if (data) {
        this.resolutionHistory = JSON.parse(data);
      }
    } catch (error) {
      console.error('Failed to load resolution history:', error);
      this.resolutionHistory = [];
    }
  }

  private async loadUserPreferences(): Promise<void> {
    try {
      const data = await AsyncStorage.getItem(this.USER_PREFERENCES_KEY);
      if (data) {
        const prefs = JSON.parse(data);
        this.userPreferences = new Map(Object.entries(prefs));
      }
    } catch (error) {
      console.error('Failed to load user preferences:', error);
      this.userPreferences = new Map();
    }
  }

  // Public methods
  async getPendingConflicts(): Promise<ConflictData[]> {
    return [...this.pendingConflicts];
  }

  async setUserPreference(entityType: string, strategy: ConflictResolutionStrategy): Promise<void> {
    this.userPreferences.set(entityType, strategy);
    try {
      const prefs = Object.fromEntries(this.userPreferences);
      await AsyncStorage.setItem(this.USER_PREFERENCES_KEY, JSON.stringify(prefs));
    } catch (error) {
      console.error('Failed to save user preferences:', error);
    }
  }

  async resolveUserConflict(conflictId: string, userChoice: any): Promise<ConflictResolutionResult> {
    const conflict = this.pendingConflicts.find(c => c.id === conflictId);
    if (!conflict) {
      throw new Error(`Conflict ${conflictId} not found`);
    }

    conflict.resolution = userChoice;
    conflict.strategy = 'user-choice';
    conflict.status = 'resolved';

    await this.savePendingConflicts();

    return {
      success: true,
      resolvedData: userChoice,
      strategy: 'user-choice',
      confidence: 1.0,
      requiresUserInput: false,
    };
  }

  async clearResolvedConflicts(): Promise<void> {
    this.pendingConflicts = this.pendingConflicts.filter(c => c.status === 'pending' || c.status === 'user-required');
    await this.savePendingConflicts();
  }

  getResolutionStats(): {
    totalResolved: number;
    strategyCounts: Record<ConflictResolutionStrategy, number>;
    averageConfidence: number;
  } {
    const strategyCounts = this.resolutionHistory.reduce((acc, entry) => {
      acc[entry.strategy] = (acc[entry.strategy] || 0) + 1;
      return acc;
    }, {} as Record<ConflictResolutionStrategy, number>);

    return {
      totalResolved: this.resolutionHistory.length,
      strategyCounts,
      averageConfidence: 0.8, // Would calculate from actual resolution data
    };
  }
}

export const conflictResolutionService = new ConflictResolutionService();
