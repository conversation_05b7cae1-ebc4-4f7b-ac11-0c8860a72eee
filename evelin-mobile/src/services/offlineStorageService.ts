import AsyncStorage from '@react-native-async-storage/async-storage';
import { Task } from '../types/task';
import { conflictResolutionService, ConflictData, ConflictResolutionStrategy } from './conflictResolutionService';

export interface OfflineData {
  tasks: Task[];
  lastSyncTimestamp: string;
  version: number;
  checksum: string;
}

export interface SyncConflict {
  id: string;
  type: 'task' | 'settings' | 'user-data';
  localData: any;
  serverData: any;
  timestamp: string;
  resolution?: 'local' | 'server' | 'merge';
}

export interface SyncResult {
  success: boolean;
  conflicts: SyncConflict[];
  syncedItems: number;
  errors: string[];
  timestamp: string;
  resolutionStrategy?: ConflictResolutionStrategy;
  autoResolvedConflicts: number;
  userRequiredConflicts: number;
  failedResolutions: number;
}

class OfflineStorageService {
  private readonly TASKS_KEY = 'offline_tasks';
  private readonly SYNC_METADATA_KEY = 'sync_metadata';
  private readonly CONFLICTS_KEY = 'sync_conflicts';
  private readonly CACHE_EXPIRY = 24 * 60 * 60 * 1000; // 24 hours

  // Initialize the service
  async initialize(): Promise<void> {
    try {
      // Perform any initialization tasks
      console.log('📱 Offline storage service initialized');
    } catch (error) {
      console.error('Failed to initialize offline storage service:', error);
      throw error;
    }
  }

  // Task operations
  async saveTasks(tasks: Task[]): Promise<void> {
    try {
      const offlineData: OfflineData = {
        tasks,
        lastSyncTimestamp: new Date().toISOString(),
        version: Date.now(),
        checksum: this.generateChecksum(tasks),
      };

      await AsyncStorage.setItem(this.TASKS_KEY, JSON.stringify(offlineData));
      console.log(`💾 Saved ${tasks.length} tasks offline`);
    } catch (error) {
      console.error('Failed to save tasks offline:', error);
      throw error;
    }
  }

  async loadTasks(): Promise<Task[]> {
    try {
      const data = await AsyncStorage.getItem(this.TASKS_KEY);
      if (!data) return [];

      const offlineData: OfflineData = JSON.parse(data);
      
      // Check if data is expired
      const lastSync = new Date(offlineData.lastSyncTimestamp);
      const now = new Date();
      const isExpired = (now.getTime() - lastSync.getTime()) > this.CACHE_EXPIRY;

      if (isExpired) {
        console.warn('⚠️ Offline data is expired, returning empty array');
        return [];
      }

      // Verify checksum
      const expectedChecksum = this.generateChecksum(offlineData.tasks);
      if (offlineData.checksum !== expectedChecksum) {
        console.warn('⚠️ Data integrity check failed, possible corruption');
      }

      console.log(`📱 Loaded ${offlineData.tasks.length} tasks from offline storage`);
      return offlineData.tasks;
    } catch (error) {
      console.error('Failed to load tasks from offline storage:', error);
      return [];
    }
  }

  // Alias for loadTasks for consistency with tests
  async getTasksOffline(): Promise<Task[]> {
    return this.loadTasks();
  }

  async addTaskOffline(task: Task): Promise<void> {
    try {
      const existingTasks = await this.loadTasks();
      const updatedTasks = [...existingTasks, task];
      await this.saveTasks(updatedTasks);
    } catch (error) {
      console.error('Failed to add task offline:', error);
      throw error;
    }
  }

  async updateTaskOffline(taskId: string, updates: Partial<Task>): Promise<void> {
    try {
      const existingTasks = await this.loadTasks();
      const taskIndex = existingTasks.findIndex(t => t.id === taskId);
      
      if (taskIndex === -1) {
        throw new Error(`Task with id ${taskId} not found in offline storage`);
      }

      existingTasks[taskIndex] = {
        ...existingTasks[taskIndex],
        ...updates,
      };

      await this.saveTasks(existingTasks);
    } catch (error) {
      console.error('Failed to update task offline:', error);
      throw error;
    }
  }

  async deleteTaskOffline(taskId: string): Promise<void> {
    try {
      const existingTasks = await this.loadTasks();
      const filteredTasks = existingTasks.filter(t => t.id !== taskId);
      await this.saveTasks(filteredTasks);
    } catch (error) {
      console.error('Failed to delete task offline:', error);
      throw error;
    }
  }

  // Sync operations with advanced conflict resolution
  async syncWithServer(strategy?: ConflictResolutionStrategy): Promise<SyncResult> {
    const result: SyncResult = {
      success: false,
      conflicts: [],
      syncedItems: 0,
      errors: [],
      timestamp: new Date().toISOString(),
      resolutionStrategy: strategy,
      autoResolvedConflicts: 0,
      userRequiredConflicts: 0,
      failedResolutions: 0,
    };

    try {
      // Initialize conflict resolution service
      await conflictResolutionService.initialize();

      // Load offline data
      const offlineTasks = await this.loadTasks();

      // Get server data
      const { mobileTaskService } = await import('./taskService');
      const serverTasks = await mobileTaskService.getTasks();

      // Use advanced conflict detection
      const conflictData = await conflictResolutionService.detectConflicts(offlineTasks, serverTasks, 'task');

      if (conflictData.length > 0) {
        console.log(`⚠️ Found ${conflictData.length} sync conflicts`);

        let resolvedTasks = [...serverTasks];
        let autoResolved = 0;
        let userRequired = 0;
        let failed = 0;

        // Process each conflict with advanced resolution
        for (const conflict of conflictData) {
          try {
            const resolutionResult = await conflictResolutionService.resolveConflict(conflict, strategy);

            if (resolutionResult.success) {
              if (resolutionResult.requiresUserInput) {
                userRequired++;
                // Keep conflict for user resolution
                result.conflicts.push(this.convertToSyncConflict(conflict));
              } else {
                autoResolved++;
                // Apply resolved data
                const taskIndex = resolvedTasks.findIndex(t => t.id === conflict.entityId);
                if (taskIndex !== -1) {
                  resolvedTasks[taskIndex] = resolutionResult.resolvedData;
                }
              }
            } else {
              failed++;
              result.errors.push(`Failed to resolve conflict for ${conflict.entityId}: ${resolutionResult.error}`);
            }
          } catch (error) {
            failed++;
            const errorMessage = error instanceof Error ? error.message : 'Unknown resolution error';
            result.errors.push(`Conflict resolution failed for ${conflict.entityId}: ${errorMessage}`);
          }
        }

        result.autoResolvedConflicts = autoResolved;
        result.userRequiredConflicts = userRequired;
        result.failedResolutions = failed;

        await this.saveTasks(resolvedTasks);
        result.syncedItems = resolvedTasks.length;

        console.log(`✅ Conflict resolution completed: ${autoResolved} auto-resolved, ${userRequired} require user input, ${failed} failed`);
      } else {
        // No conflicts, merge data
        const mergedTasks = this.mergeTasks(offlineTasks, serverTasks);
        await this.saveTasks(mergedTasks);
        result.syncedItems = mergedTasks.length;
      }

      result.success = true;
      console.log(`✅ Sync completed: ${result.syncedItems} items synced`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown sync error';
      result.errors.push(errorMessage);
      console.error('Sync failed:', error);
    }

    return result;
  }

  private convertToSyncConflict(conflictData: ConflictData): SyncConflict {
    return {
      id: conflictData.entityId,
      type: conflictData.type,
      localData: conflictData.localData,
      serverData: conflictData.serverData,
      timestamp: conflictData.timestamp,
      resolution: conflictData.strategy === 'user-choice' ? undefined :
                 (conflictData.strategy === 'client-wins' ? 'local' : 'server'),
    };
  }

  private detectConflicts(offlineTasks: Task[], serverTasks: Task[]): SyncConflict[] {
    const conflicts: SyncConflict[] = [];
    
    for (const offlineTask of offlineTasks) {
      const serverTask = serverTasks.find(t => t.id === offlineTask.id);
      
      if (serverTask) {
        // Check if both have been modified
        const offlineModified = new Date(offlineTask.createdAt);
        const serverModified = new Date(serverTask.createdAt);
        
        // If timestamps are different, there's a potential conflict
        if (Math.abs(offlineModified.getTime() - serverModified.getTime()) > 1000) {
          // Check if actual data is different
          if (this.tasksAreDifferent(offlineTask, serverTask)) {
            conflicts.push({
              id: offlineTask.id,
              type: 'task',
              localData: offlineTask,
              serverData: serverTask,
              timestamp: new Date().toISOString(),
            });
          }
        }
      }
    }
    
    return conflicts;
  }

  private tasksAreDifferent(task1: Task, task2: Task): boolean {
    // Compare key fields that matter for conflicts
    const fields = ['text', 'completed', 'location', 'coordinates', 'notificationDistance'];
    
    return fields.some(field => {
      const val1 = (task1 as any)[field];
      const val2 = (task2 as any)[field];
      
      if (typeof val1 === 'object' && typeof val2 === 'object') {
        return JSON.stringify(val1) !== JSON.stringify(val2);
      }
      
      return val1 !== val2;
    });
  }

  private async resolveConflicts(
    conflicts: SyncConflict[],
    offlineTasks: Task[],
    serverTasks: Task[]
  ): Promise<Task[]> {
    const resolvedTasks = [...serverTasks];
    
    for (const conflict of conflicts) {
      // Default resolution strategy: server wins for completed tasks, local wins for others
      const localTask = conflict.localData as Task;
      const serverTask = conflict.serverData as Task;
      
      let resolution: 'local' | 'server' | 'merge' = 'server';
      
      // Custom resolution logic
      if (localTask.completed && !serverTask.completed) {
        resolution = 'local'; // Local completion takes precedence
      } else if (!localTask.completed && serverTask.completed) {
        resolution = 'server'; // Server completion takes precedence
      } else {
        // Use most recent creation time as fallback
        const localTime = new Date(localTask.createdAt).getTime();
        const serverTime = new Date(serverTask.createdAt).getTime();
        resolution = localTime > serverTime ? 'local' : 'server';
      }
      
      conflict.resolution = resolution;
      
      const taskIndex = resolvedTasks.findIndex(t => t.id === conflict.id);
      if (taskIndex !== -1) {
        if (resolution === 'local') {
          resolvedTasks[taskIndex] = localTask;
        }
        // 'server' resolution keeps the server task as-is
      }
    }
    
    return resolvedTasks;
  }

  private mergeTasks(offlineTasks: Task[], serverTasks: Task[]): Task[] {
    const merged = [...serverTasks];
    
    // Add offline tasks that don't exist on server
    for (const offlineTask of offlineTasks) {
      const existsOnServer = serverTasks.some(t => t.id === offlineTask.id);
      if (!existsOnServer) {
        merged.push(offlineTask);
      }
    }
    
    return merged;
  }

  // Conflict management
  async saveConflicts(conflicts: SyncConflict[]): Promise<void> {
    try {
      await AsyncStorage.setItem(this.CONFLICTS_KEY, JSON.stringify(conflicts));
    } catch (error) {
      console.error('Failed to save conflicts:', error);
    }
  }

  async loadConflicts(): Promise<SyncConflict[]> {
    try {
      const data = await AsyncStorage.getItem(this.CONFLICTS_KEY);
      return data ? JSON.parse(data) : [];
    } catch (error) {
      console.error('Failed to load conflicts:', error);
      return [];
    }
  }

  async clearConflicts(): Promise<void> {
    try {
      await AsyncStorage.removeItem(this.CONFLICTS_KEY);
    } catch (error) {
      console.error('Failed to clear conflicts:', error);
    }
  }

  // Utility methods
  private generateChecksum(data: any): string {
    const str = JSON.stringify(data);
    let hash = 0;
    
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    
    return hash.toString(36);
  }

  async getStorageInfo(): Promise<{
    tasksCount: number;
    lastSync: string | null;
    storageSize: number;
    conflicts: number;
  }> {
    try {
      const tasks = await this.loadTasks();
      const conflicts = await this.loadConflicts();
      
      // Estimate storage size
      const tasksData = await AsyncStorage.getItem(this.TASKS_KEY);
      const storageSize = tasksData ? new Blob([tasksData]).size : 0;
      
      const syncData = await AsyncStorage.getItem(this.SYNC_METADATA_KEY);
      const lastSync = syncData ? JSON.parse(syncData).lastSync : null;
      
      return {
        tasksCount: tasks.length,
        lastSync,
        storageSize,
        conflicts: conflicts.length,
      };
    } catch (error) {
      console.error('Failed to get storage info:', error);
      return {
        tasksCount: 0,
        lastSync: null,
        storageSize: 0,
        conflicts: 0,
      };
    }
  }

  async clearAllOfflineData(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([
        this.TASKS_KEY,
        this.SYNC_METADATA_KEY,
        this.CONFLICTS_KEY,
      ]);
      console.log('🗑️ Cleared all offline data');
    } catch (error) {
      console.error('Failed to clear offline data:', error);
      throw error;
    }
  }
}

export const offlineStorageService = new OfflineStorageService();
