import { useEffect } from 'react';
import { useNavigate, Link } from 'react-router-dom';
import { EvelinApp } from '@/components/EvelinApp';
import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';

const Index = () => {
  const { user, loading, signOut } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (!loading && !user) {
      navigate('/auth');
    }
  }, [user, loading, navigate]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-background to-muted p-4">
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold">Welcome to Evelin</h1>
          <p className="text-xl text-muted-foreground">Your AI-powered task assistant</p>
          <Link to="/auth">
            <Button size="lg">Get Started</Button>
          </Link>
        </div>
      </div>
    );
  }

  return <EvelinApp user={user} onSignOut={signOut} />;
};

export default Index;
