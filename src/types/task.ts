export interface Task {
  id: string;
  text: string;
  location?: string;
  category?: string;
  completed: boolean;
  createdAt: Date;
  notificationTriggered?: boolean;
  notificationDistance?: number; // Distance in meters for notifications
  coordinates?: {
    lat: number;
    lng: number;
  };
  placeDetails?: {
    name: string;
    placeId?: string;
    address?: string;
    rating?: number;
    isOpen?: boolean;
  };
}

export interface VoiceState {
  isListening: boolean;
  isProcessing: boolean;
  transcript: string;
  error?: string;
}

export interface LocationState {
  latitude: number | null;
  longitude: number | null;
  accuracy: number | null;
  error?: string;
  permission: 'granted' | 'denied' | 'prompt';
}