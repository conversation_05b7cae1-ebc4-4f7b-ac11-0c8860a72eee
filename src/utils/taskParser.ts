import { Task } from '@/types/task';

// Location keywords and their categories
const locationKeywords = {
  'lidl': { category: 'grocery', searchTerms: ['lidl'] },
  'tesco': { category: 'grocery', searchTerms: ['tesco'] },
  'asda': { category: 'grocery', searchTerms: ['asda'] },
  'sainsburys': { category: 'grocery', searchTerms: ['sainsburys', 'sainsbury'] },
  'morrisons': { category: 'grocery', searchTerms: ['morrisons'] },
  'aldi': { category: 'grocery', searchTerms: ['aldi'] },
  'waitrose': { category: 'grocery', searchTerms: ['waitrose'] },
  
  'pharmacy': { category: 'pharmacy', searchTerms: ['pharmacy', 'chemist', 'boots', 'superdrug'] },
  'boots': { category: 'pharmacy', searchTerms: ['boots'] },
  'superdrug': { category: 'pharmacy', searchTerms: ['superdrug'] },
  
  'petrol': { category: 'fuel', searchTerms: ['petrol station', 'gas station', 'shell', 'bp', 'esso', 'texaco'] },
  'shell': { category: 'fuel', searchTerms: ['shell'] },
  'bp': { category: 'fuel', searchTerms: ['bp'] },
  
  'bank': { category: 'finance', searchTerms: ['bank', 'atm', 'cash machine', 'barclays', 'lloyds', 'hsbc', 'natwest'] },
  'barclays': { category: 'finance', searchTerms: ['barclays'] },
  'lloyds': { category: 'finance', searchTerms: ['lloyds'] },
  'hsbc': { category: 'finance', searchTerms: ['hsbc'] },
  
  'post office': { category: 'postal', searchTerms: ['post office', 'royal mail'] },
  
  'gym': { category: 'fitness', searchTerms: ['gym', 'fitness', 'puregym', 'david lloyd'] },
  
  'restaurant': { category: 'food', searchTerms: ['restaurant', 'cafe', 'mcdonald', 'kfc', 'subway', 'starbucks', 'costa'] },
  'mcdonalds': { category: 'food', searchTerms: ['mcdonald', 'mcdonalds'] },
  'starbucks': { category: 'food', searchTerms: ['starbucks'] },
  'costa': { category: 'food', searchTerms: ['costa'] },
};

export const parseTaskFromSpeech = (transcript: string): Partial<Task> => {
  const lowerTranscript = transcript.toLowerCase();
  
  // Find location matches
  let detectedLocation = '';
  let detectedCategory = '';
  
  for (const [location, data] of Object.entries(locationKeywords)) {
    for (const term of data.searchTerms) {
      if (lowerTranscript.includes(term)) {
        detectedLocation = location;
        detectedCategory = data.category;
        break;
      }
    }
    if (detectedLocation) break;
  }
  
  // If no specific location found, try to detect general categories
  if (!detectedLocation) {
    if (lowerTranscript.includes('medicine') || lowerTranscript.includes('pills') || lowerTranscript.includes('prescription')) {
      detectedCategory = 'pharmacy';
      detectedLocation = 'pharmacy';
    } else if (lowerTranscript.includes('food') || lowerTranscript.includes('grocery') || lowerTranscript.includes('shopping')) {
      detectedCategory = 'grocery';
      detectedLocation = 'grocery store';
    } else if (lowerTranscript.includes('fuel') || lowerTranscript.includes('petrol') || lowerTranscript.includes('gas')) {
      detectedCategory = 'fuel';
      detectedLocation = 'petrol station';
    } else if (lowerTranscript.includes('money') || lowerTranscript.includes('cash') || lowerTranscript.includes('withdraw')) {
      detectedCategory = 'finance';
      detectedLocation = 'bank';
    }
  }
  
  return {
    id: crypto.randomUUID(),
    text: transcript,
    location: detectedLocation || undefined,
    category: detectedCategory || undefined,
    completed: false,
    createdAt: new Date(),
  };
};

export const getLocationSearchTerm = (task: Task): string => {
  if (!task.location) return '';
  
  const locationData = locationKeywords[task.location as keyof typeof locationKeywords];
  if (locationData) {
    return locationData.searchTerms[0];
  }
  
  return task.location;
};

export const getCategoryDisplayName = (category: string): string => {
  const categoryNames: Record<string, string> = {
    grocery: 'Grocery Store',
    pharmacy: 'Pharmacy',
    fuel: 'Petrol Station',
    finance: 'Bank/ATM',
    postal: 'Post Office',
    fitness: 'Gym',
    food: 'Restaurant/Cafe',
  };
  
  return categoryNames[category] || category;
};