@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    --background: 250 100% 98%;
    --foreground: 224 15% 25%;

    --card: 0 0% 100%;
    --card-foreground: 224 15% 25%;

    --popover: 0 0% 100%;
    --popover-foreground: 224 15% 25%;

    --primary: 262 76% 58%;
    --primary-foreground: 0 0% 100%;
    --primary-glow: 262 100% 75%;

    --secondary: 247 100% 96%;
    --secondary-foreground: 262 76% 58%;

    --muted: 247 100% 96%;
    --muted-foreground: 224 15% 55%;

    --accent: 262 100% 95%;
    --accent-foreground: 262 76% 58%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 100%;

    --border: 247 50% 90%;
    --input: 247 50% 90%;
    --ring: 262 76% 58%;

    --success: 142 76% 36%;
    --success-foreground: 0 0% 100%;

    --voice-active: 262 100% 75%;
    --voice-listening: 262 76% 58%;
    --voice-processing: 280 100% 70%;

    --gradient-primary: linear-gradient(135deg, hsl(262 76% 58%), hsl(280 100% 70%));
    --gradient-card: linear-gradient(145deg, hsl(0 0% 100%), hsl(247 100% 98%));
    --gradient-voice: linear-gradient(135deg, hsl(262 100% 75%), hsl(280 100% 70%));

    --shadow-soft: 0 4px 12px hsl(262 76% 58% / 0.1);
    --shadow-voice: 0 8px 32px hsl(262 76% 58% / 0.3);
    
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-voice: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}