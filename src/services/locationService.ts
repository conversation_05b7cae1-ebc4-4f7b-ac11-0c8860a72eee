import { getLocationSearchTerm } from '@/utils/taskParser';
import { Task } from '@/types/task';
import { getGoogleMapsService } from './googleMapsService';

interface Coordinates {
  lat: number;
  lng: number;
}

// For demo purposes, we'll use a more sophisticated simulation
// In a real app, this would use Google Places API or similar
export class LocationService {
  private static instance: LocationService;
  private coordinatesCache = new Map<string, Coordinates>();

  static getInstance(): LocationService {
    if (!LocationService.instance) {
      LocationService.instance = new LocationService();
    }
    return LocationService.instance;
  }

  // Resolve task location using Google Maps API
  async resolveTaskLocation(task: Task, userLocation?: { latitude: number; longitude: number }): Promise<{ coordinates: Coordinates; placeDetails?: any } | null> {
    if (!task.location) return null;

    // Check cache first
    const cacheKey = `${task.location}_${userLocation?.latitude}_${userLocation?.longitude}`;
    if (this.coordinatesCache.has(cacheKey)) {
      return { coordinates: this.coordinatesCache.get(cacheKey)! };
    }

    let coordinates: Coordinates | null = null;
    let placeDetails: any = null;

    // Try Google Maps API first if we have user location
    if (userLocation) {
      try {
        const googleMapsService = getGoogleMapsService();
        if (googleMapsService) {
          const places = await googleMapsService.searchNearbyPlaces(task.location, userLocation, 5000); // 5km radius
          if (places.length > 0) {
            const place = places[0];
            coordinates = {
              lat: place.lat,
              lng: place.lng
            };
            placeDetails = {
              name: place.name,
              placeId: place.placeId,
              address: place.address,
              rating: place.rating,
              isOpen: place.isOpen
            };
            console.log(`🗺️ Found Google Maps coordinates for "${task.location}":`, coordinates, placeDetails);
          }
        }
      } catch (error) {
        console.error('Error with Google Maps API:', error);
      }
    }

    // Fallback to OpenStreetMap geocoding
    if (!coordinates && userLocation) {
      try {
        const places = await this.searchNearbyPlaces(task.location, userLocation, 10000); // 10km radius
        if (places.length > 0) {
          coordinates = places[0]; // Use the closest/best match
          console.log(`🌍 Found OpenStreetMap coordinates for "${task.location}":`, coordinates);
        }
      } catch (error) {
        console.error('Error with OpenStreetMap geocoding:', error);
      }
    }

    // Fallback to simulated nearby locations if we have user location
    if (!coordinates && userLocation) {
      coordinates = this.simulateNearbyLocation(task.location, userLocation);
      if (coordinates) {
        console.log(`🎯 Using simulated coordinates for "${task.location}":`, coordinates);
      }
    }

    // Final fallback to default locations
    if (!coordinates) {
      coordinates = this.getDefaultCoordinates(task.location);
      if (coordinates) {
        console.log(`📍 Using default coordinates for "${task.location}":`, coordinates);
      }
    }

    // Cache the result
    if (coordinates) {
      this.coordinatesCache.set(cacheKey, coordinates);
    }

    return coordinates ? { coordinates, placeDetails } : null;
  }

  private simulateNearbyLocation(location: string, userLocation: { latitude: number; longitude: number }): Coordinates | null {
    // Simulate locations within 1-5km of user's location
    const offsetRange = 0.05; // Roughly 5km in degrees
    const minOffset = 0.01; // Roughly 1km in degrees
    
    // Generate random offset
    const latOffset = (Math.random() - 0.5) * offsetRange;
    const lngOffset = (Math.random() - 0.5) * offsetRange;
    
    // Ensure minimum distance
    const adjustedLatOffset = Math.abs(latOffset) < minOffset ? 
      (latOffset >= 0 ? minOffset : -minOffset) : latOffset;
    const adjustedLngOffset = Math.abs(lngOffset) < minOffset ? 
      (lngOffset >= 0 ? minOffset : -minOffset) : lngOffset;

    return {
      lat: userLocation.latitude + adjustedLatOffset,
      lng: userLocation.longitude + adjustedLngOffset
    };
  }

  private getDefaultCoordinates(location: string): Coordinates | null {
    // Default locations in London area for demo
    const defaultLocations: Record<string, Coordinates> = {
      'lidl': { lat: 51.5074, lng: -0.1278 },
      'tesco': { lat: 51.5076, lng: -0.1282 },
      'asda': { lat: 51.5078, lng: -0.1285 },
      'sainsburys': { lat: 51.5080, lng: -0.1288 },
      'morrisons': { lat: 51.5082, lng: -0.1290 },
      'aldi': { lat: 51.5084, lng: -0.1292 },
      'waitrose': { lat: 51.5086, lng: -0.1294 },
      
      'pharmacy': { lat: 51.5075, lng: -0.1280 },
      'boots': { lat: 51.5077, lng: -0.1283 },
      'superdrug': { lat: 51.5079, lng: -0.1286 },
      
      'bank': { lat: 51.5081, lng: -0.1289 },
      'hsbc': { lat: 51.5083, lng: -0.1291 },
      'barclays': { lat: 51.5085, lng: -0.1293 },
      'lloyds': { lat: 51.5087, lng: -0.1295 },
      'natwest': { lat: 51.5089, lng: -0.1297 },
      
      'petrol station': { lat: 51.5088, lng: -0.1296 },
      'shell': { lat: 51.5090, lng: -0.1298 },
      'bp': { lat: 51.5092, lng: -0.1300 },
      'esso': { lat: 51.5094, lng: -0.1302 },
      
      'post office': { lat: 51.5091, lng: -0.1299 },
      'gym': { lat: 51.5093, lng: -0.1301 },
      'library': { lat: 51.5095, lng: -0.1303 },
      'hospital': { lat: 51.5097, lng: -0.1305 },
      'school': { lat: 51.5099, lng: -0.1307 },
    };

    return defaultLocations[location.toLowerCase()] || null;
  }

  // Use OpenStreetMap Nominatim for free geocoding
  async searchNearbyPlaces(
    query: string,
    userLocation: { latitude: number; longitude: number },
    radius: number = 5000
  ): Promise<Coordinates[]> {
    try {
      // Search for places near user location using Nominatim
      const searchQuery = `${query} near ${userLocation.latitude},${userLocation.longitude}`;
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?` +
        `q=${encodeURIComponent(searchQuery)}&` +
        `format=json&` +
        `limit=5&` +
        `bounded=1&` +
        `viewbox=${userLocation.longitude - 0.05},${userLocation.latitude + 0.05},${userLocation.longitude + 0.05},${userLocation.latitude - 0.05}`
      );

      if (!response.ok) {
        throw new Error('Geocoding request failed');
      }

      const data = await response.json();

      const results: Coordinates[] = data
        .filter((place: any) => place.lat && place.lon)
        .map((place: any) => ({
          lat: parseFloat(place.lat),
          lng: parseFloat(place.lon)
        }))
        .filter((coords: Coordinates) => {
          // Filter by distance
          const distance = this.calculateDistance(
            userLocation.latitude,
            userLocation.longitude,
            coords.lat,
            coords.lng
          );
          return distance <= radius;
        });

      return results.slice(0, 3); // Return top 3 results
    } catch (error) {
      console.error('Error searching places:', error);

      // Fallback to simulated results
      const results: Coordinates[] = [];
      for (let i = 0; i < 3; i++) {
        const coordinates = this.simulateNearbyLocation(query, userLocation);
        if (coordinates) {
          results.push(coordinates);
        }
      }
      return results;
    }
  }

  // Helper method to calculate distance
  private calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = lat1 * Math.PI / 180;
    const φ2 = lat2 * Math.PI / 180;
    const Δφ = (lat2 - lat1) * Math.PI / 180;
    const Δλ = (lon2 - lon1) * Math.PI / 180;

    const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c; // Distance in meters
  }

  // Clear cache when user location changes significantly
  clearCache(): void {
    this.coordinatesCache.clear();
  }
}

export const locationService = LocationService.getInstance();
