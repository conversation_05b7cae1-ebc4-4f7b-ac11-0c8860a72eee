// Google Maps API integration for real location data
import { Coordinates } from '@/types/task';

interface GooglePlacesResult {
  place_id: string;
  name: string;
  geometry: {
    location: {
      lat: number;
      lng: number;
    };
  };
  vicinity?: string;
  types: string[];
  rating?: number;
  opening_hours?: {
    open_now: boolean;
  };
}

interface GooglePlacesResponse {
  results: GooglePlacesResult[];
  status: string;
  next_page_token?: string;
}

export class GoogleMapsService {
  private apiKey: string;
  private placesService: google.maps.places.PlacesService | null = null;
  private geocoder: google.maps.Geocoder | null = null;
  private isLoaded = false;

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  // Initialize Google Maps API
  async initialize(): Promise<void> {
    if (this.isLoaded) return;

    return new Promise((resolve, reject) => {
      // Check if Google Maps is already loaded
      if (window.google && window.google.maps) {
        this.initializeServices();
        resolve();
        return;
      }

      // Load Google Maps API
      const script = document.createElement('script');
      script.src = `https://maps.googleapis.com/maps/api/js?key=${this.apiKey}&libraries=places`;
      script.async = true;
      script.defer = true;
      
      script.onload = () => {
        this.initializeServices();
        resolve();
      };
      
      script.onerror = () => {
        reject(new Error('Failed to load Google Maps API'));
      };

      document.head.appendChild(script);
    });
  }

  private initializeServices(): void {
    if (window.google && window.google.maps) {
      // Create a dummy map element for PlacesService (required by Google)
      const mapDiv = document.createElement('div');
      const map = new google.maps.Map(mapDiv);
      
      this.placesService = new google.maps.places.PlacesService(map);
      this.geocoder = new google.maps.Geocoder();
      this.isLoaded = true;
    }
  }

  // Search for places near a location
  async searchNearbyPlaces(
    query: string,
    userLocation: { latitude: number; longitude: number },
    radius: number = 2000
  ): Promise<Coordinates[]> {
    await this.initialize();
    
    if (!this.placesService) {
      throw new Error('Google Places service not initialized');
    }

    return new Promise((resolve, reject) => {
      const request: google.maps.places.PlaceSearchRequest = {
        location: new google.maps.LatLng(userLocation.latitude, userLocation.longitude),
        radius: radius,
        keyword: query,
        type: this.getPlaceType(query)
      };

      this.placesService!.nearbySearch(request, (results, status) => {
        if (status === google.maps.places.PlacesServiceStatus.OK && results) {
          const coordinates = results
            .slice(0, 5) // Limit to 5 results
            .map(place => ({
              lat: place.geometry!.location!.lat(),
              lng: place.geometry!.location!.lng(),
              name: place.name,
              placeId: place.place_id,
              address: place.vicinity,
              rating: place.rating,
              isOpen: place.opening_hours?.open_now,
              types: place.types
            }));
          
          console.log(`🗺️ Found ${coordinates.length} real places for "${query}":`, coordinates);
          resolve(coordinates);
        } else {
          console.error('Google Places search failed:', status);
          reject(new Error(`Places search failed: ${status}`));
        }
      });
    });
  }

  // Get more accurate location using Google's Geolocation API
  async getCurrentPosition(): Promise<{ latitude: number; longitude: number }> {
    // First try browser geolocation
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation not supported'));
        return;
      }

      navigator.geolocation.getCurrentPosition(
        (position) => {
          resolve({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude
          });
        },
        (error) => {
          console.error('Geolocation error:', error);
          reject(error);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 60000 // 1 minute cache
        }
      );
    });
  }

  // Geocode an address to coordinates
  async geocodeAddress(address: string): Promise<Coordinates | null> {
    await this.initialize();
    
    if (!this.geocoder) {
      throw new Error('Google Geocoder not initialized');
    }

    return new Promise((resolve, reject) => {
      this.geocoder!.geocode({ address }, (results, status) => {
        if (status === google.maps.GeocoderStatus.OK && results && results[0]) {
          const location = results[0].geometry.location;
          resolve({
            lat: location.lat(),
            lng: location.lng()
          });
        } else {
          console.error('Geocoding failed:', status);
          resolve(null);
        }
      });
    });
  }

  // Map common search terms to Google Places types
  private getPlaceType(query: string): string | undefined {
    const queryLower = query.toLowerCase();
    
    if (queryLower.includes('grocery') || queryLower.includes('supermarket') || 
        queryLower.includes('lidl') || queryLower.includes('tesco') || 
        queryLower.includes('asda') || queryLower.includes('sainsbury')) {
      return 'supermarket';
    }
    
    if (queryLower.includes('bank') || queryLower.includes('atm')) {
      return 'bank';
    }
    
    if (queryLower.includes('pharmacy') || queryLower.includes('chemist')) {
      return 'pharmacy';
    }
    
    if (queryLower.includes('restaurant') || queryLower.includes('food')) {
      return 'restaurant';
    }
    
    if (queryLower.includes('gas') || queryLower.includes('petrol') || queryLower.includes('fuel')) {
      return 'gas_station';
    }
    
    if (queryLower.includes('hospital') || queryLower.includes('doctor')) {
      return 'hospital';
    }
    
    return undefined; // Let Google decide
  }

  // Calculate distance between two points (Haversine formula)
  calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371e3; // Earth's radius in meters
    const φ1 = lat1 * Math.PI / 180;
    const φ2 = lat2 * Math.PI / 180;
    const Δφ = (lat2 - lat1) * Math.PI / 180;
    const Δλ = (lon2 - lon1) * Math.PI / 180;

    const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c; // Distance in meters
  }
}

// Global instance
let googleMapsService: GoogleMapsService | null = null;

export const initializeGoogleMaps = (apiKey: string): GoogleMapsService => {
  if (!googleMapsService) {
    googleMapsService = new GoogleMapsService(apiKey);
  }
  return googleMapsService;
};

export const getGoogleMapsService = (): GoogleMapsService | null => {
  return googleMapsService;
};

// Type declarations for Google Maps
declare global {
  interface Window {
    google: typeof google;
  }
}
