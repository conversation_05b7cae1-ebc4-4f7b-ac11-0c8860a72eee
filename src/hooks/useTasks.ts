import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Task } from '@/types/task';
import { User } from '@supabase/supabase-js';

export const useTasks = (user: User | null) => {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);

  // Load tasks from database
  useEffect(() => {
    if (!user) {
      setTasks([]);
      setLoading(false);
      return;
    }

    const loadTasks = async () => {
      try {
        const { data, error } = await supabase
          .from('tasks')
          .select('*')
          .eq('user_id', user.id)
          .order('created_at', { ascending: false });

        if (error) throw error;

        const formattedTasks: Task[] = data.map(task => ({
          id: task.id,
          text: task.text,
          location: task.location || undefined,
          category: task.category || undefined,
          completed: task.completed,
          createdAt: new Date(task.created_at),
          notificationTriggered: task.notification_triggered || false,
          notificationDistance: task.notification_distance || undefined,
          coordinates: task.coordinates || undefined,
        }));

        setTasks(formattedTasks);
      } catch (error) {
        console.error('Error loading tasks:', error);
      } finally {
        setLoading(false);
      }
    };

    loadTasks();
  }, [user]);

  const addTask = async (task: Task) => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    try {
      console.log('Adding task:', task);
      console.log('User ID:', user.id);

      // Test connection first
      const { error: connectionError } = await supabase
        .from('tasks')
        .select('count')
        .limit(1);

      if (connectionError) {
        console.error('Connection test failed:', connectionError);
        throw new Error(`Cannot connect to database: ${connectionError.message}`);
      }

      console.log('Database connection successful');

      const insertData = {
        id: task.id,
        user_id: user.id,
        text: task.text,
        location: task.location || null,
        category: task.category || null,
        completed: task.completed,
        created_at: task.createdAt.toISOString(),
        notification_triggered: task.notificationTriggered || false,
        notification_distance: task.notificationDistance || null,
        coordinates: task.coordinates || null,
      };

      console.log('Insert data:', insertData);

      const { data, error } = await supabase
        .from('tasks')
        .insert(insertData)
        .select();

      if (error) {
        console.error('Supabase error details:', {
          message: error.message,
          details: error.details,
          hint: error.hint,
          code: error.code
        });
        throw new Error(`Database error: ${error.message || 'Unknown database error'} (Code: ${error.code || 'N/A'})`);
      }

      console.log('Task inserted successfully:', data);
      setTasks(prev => [task, ...prev]);
    } catch (error) {
      console.error('Error adding task:', error);
      throw error;
    }
  };

  const updateTask = async (taskId: string, updates: Partial<Task>) => {
    if (!user) return;

    try {
      // Prepare the update object with proper field mapping
      const updateData: any = {};

      if (updates.text !== undefined) updateData.text = updates.text;
      if (updates.location !== undefined) updateData.location = updates.location || null;
      if (updates.category !== undefined) updateData.category = updates.category || null;
      if (updates.completed !== undefined) updateData.completed = updates.completed;
      if (updates.createdAt !== undefined) updateData.created_at = updates.createdAt.toISOString();
      if (updates.notificationTriggered !== undefined) updateData.notification_triggered = updates.notificationTriggered;
      if (updates.notificationDistance !== undefined) updateData.notification_distance = updates.notificationDistance || null;
      if (updates.coordinates !== undefined) updateData.coordinates = updates.coordinates || null;

      const { error } = await supabase
        .from('tasks')
        .update(updateData)
        .eq('id', taskId)
        .eq('user_id', user.id);

      if (error) throw error;

      setTasks(prev => prev.map(task =>
        task.id === taskId ? { ...task, ...updates } : task
      ));
    } catch (error) {
      console.error('Error updating task:', error);
      throw error;
    }
  };

  const deleteTask = async (taskId: string) => {
    if (!user) return;

    try {
      const { error } = await supabase
        .from('tasks')
        .delete()
        .eq('id', taskId)
        .eq('user_id', user.id);

      if (error) throw error;

      setTasks(prev => prev.filter(task => task.id !== taskId));
    } catch (error) {
      console.error('Error deleting task:', error);
      throw error;
    }
  };

  return {
    tasks,
    loading,
    addTask,
    updateTask,
    deleteTask,
  };
};