import { useState, useEffect, useRef } from 'react';
import { VoiceState } from '@/types/task';

declare global {
  interface Window {
    SpeechRecognition: any;
    webkitSpeechRecognition: any;
  }
}

export const useVoiceRecognition = () => {
  const [voiceState, setVoiceState] = useState<VoiceState>({
    isListening: false,
    isProcessing: false,
    transcript: '',
  });
  
  const recognitionRef = useRef<any>(null);
  const isSupported = useRef(false);

  useEffect(() => {
    // Check if speech recognition is supported
    if ('SpeechRecognition' in window || 'webkitSpeechRecognition' in window) {
      isSupported.current = true;
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      
      try {
        recognitionRef.current = new SpeechRecognition();
        
        recognitionRef.current.continuous = false;
        recognitionRef.current.interimResults = false;
        recognitionRef.current.lang = 'en-US';

      recognitionRef.current.onstart = () => {
        setVoiceState(prev => ({ ...prev, isListening: true, error: undefined }));
      };

      recognitionRef.current.onresult = (event: any) => {
        const transcript = event.results[0][0].transcript;
        setVoiceState(prev => ({ 
          ...prev, 
          transcript, 
          isListening: false, 
          isProcessing: true 
        }));
        
        // Process the transcript after a short delay
        setTimeout(() => {
          setVoiceState(prev => ({ ...prev, isProcessing: false }));
        }, 1000);
      };

      recognitionRef.current.onerror = (event: any) => {
        setVoiceState(prev => ({ 
          ...prev, 
          isListening: false, 
          isProcessing: false,
          error: `Speech recognition error: ${event.error}` 
        }));
      };

      recognitionRef.current.onend = () => {
        setVoiceState(prev => ({ ...prev, isListening: false }));
      };
      } catch (error) {
        console.error('Speech recognition initialization error:', error);
        setVoiceState(prev => ({ 
          ...prev, 
          error: 'Speech recognition not available' 
        }));
        isSupported.current = false;
      }
    } else {
      setVoiceState(prev => ({ 
        ...prev, 
        error: 'Speech recognition not supported' 
      }));
    }

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.abort();
      }
    };
  }, []);

  const startListening = () => {
    if (recognitionRef.current && !voiceState.isListening) {
      try {
        recognitionRef.current.start();
      } catch (error) {
        setVoiceState(prev => ({ 
          ...prev, 
          error: 'Could not start voice recognition' 
        }));
      }
    }
  };

  const stopListening = () => {
    if (recognitionRef.current && voiceState.isListening) {
      recognitionRef.current.stop();
    }
  };

  const clearTranscript = () => {
    setVoiceState(prev => ({ ...prev, transcript: '', error: undefined }));
  };

  return {
    ...voiceState,
    isSupported: isSupported.current,
    startListening,
    stopListening,
    clearTranscript,
  };
};