import { useState, useEffect } from 'react';
import { LocationState } from '@/types/task';

export const useGeolocation = () => {
  const [location, setLocation] = useState<LocationState>({
    latitude: null,
    longitude: null,
    accuracy: null,
    permission: 'prompt',
  });

  const [watchId, setWatchId] = useState<number | null>(null);

  useEffect(() => {
    if (!navigator.geolocation) {
      setLocation(prev => ({ 
        ...prev, 
        error: 'Geolocation is not supported by this browser',
        permission: 'denied'
      }));
      return;
    }

    // Check current permission state
    if (navigator.permissions) {
      navigator.permissions.query({ name: 'geolocation' }).then((result) => {
        setLocation(prev => ({ ...prev, permission: result.state as any }));
        
        // If permission is denied, set appropriate error
        if (result.state === 'denied') {
          setLocation(prev => ({ 
            ...prev, 
            error: 'Location access denied. Please enable location in your browser settings.' 
          }));
        }
      }).catch(() => {
        // Fallback if permissions API not available
        setLocation(prev => ({ ...prev, permission: 'prompt' }));
      });
    } else {
      // Fallback if permissions API not available
      setLocation(prev => ({ ...prev, permission: 'prompt' }));
    }
  }, []);

  const getCurrentPosition = () => {
    if (!navigator.geolocation) return;

    navigator.geolocation.getCurrentPosition(
      (position) => {
        setLocation({
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy,
          permission: 'granted',
          error: undefined,
        });
      },
      (error) => {
        let errorMessage = 'Unable to retrieve location';
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'Location access denied by user';
            setLocation(prev => ({ ...prev, permission: 'denied', error: errorMessage }));
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Location information unavailable';
            break;
          case error.TIMEOUT:
            errorMessage = 'Location request timed out';
            break;
        }
        setLocation(prev => ({ ...prev, error: errorMessage }));
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000, // 5 minutes
      }
    );
  };

  const startWatching = () => {
    if (!navigator.geolocation || watchId) return;

    const id = navigator.geolocation.watchPosition(
      (position) => {
        setLocation({
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy,
          permission: 'granted',
          error: undefined,
        });
      },
      (error) => {
        console.error('Geolocation watch error:', error);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000, // 1 minute
      }
    );

    setWatchId(id);
  };

  const stopWatching = () => {
    if (watchId) {
      navigator.geolocation.clearWatch(watchId);
      setWatchId(null);
    }
  };

  // Calculate distance between two coordinates (Haversine formula)
  const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number) => {
    const R = 6371; // Earth's radius in kilometers
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const distance = R * c;
    return distance * 1000; // Convert to meters
  };

  return {
    ...location,
    getCurrentPosition,
    startWatching,
    stopWatching,
    calculateDistance,
    isWatching: watchId !== null,
  };
};