// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://cwsygtogkpqdijbkvvns.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImN3c3lndG9na3BxZGlqYmt2dm5zIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQwNTA5NDAsImV4cCI6MjA2OTYyNjk0MH0.89zyE13N2XXWLMpvw25twaNikl98M29ryMTt79uHO_c";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});