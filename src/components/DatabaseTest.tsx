import React, { useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useAuth } from '@/hooks/useAuth';

export const DatabaseTest: React.FC = () => {
  const [testResult, setTestResult] = useState<string>('');
  const [isLoading, setIsLoading] = useState(false);
  const { user } = useAuth();

  const testConnection = async () => {
    setIsLoading(true);
    setTestResult('Testing connection...');

    try {
      // Test 1: Basic connection
      const { data, error } = await supabase
        .from('tasks')
        .select('count')
        .limit(1);

      if (error) {
        setTestResult(`❌ Connection failed: ${error.message} (Code: ${error.code})`);
        return;
      }

      setTestResult(prev => prev + '\n✅ Basic connection successful');

      // Test 2: Authentication
      if (!user) {
        setTestResult(prev => prev + '\n❌ User not authenticated');
        return;
      }

      setTestResult(prev => prev + `\n✅ User authenticated: ${user.email}`);

      // Test 3: Try to insert a test record
      const testTask = {
        id: crypto.randomUUID(),
        user_id: user.id,
        text: 'Test task - will be deleted',
        location: 'test',
        category: 'test',
        completed: false,
        created_at: new Date().toISOString(),
        notification_triggered: false,
        notification_distance: 200,
        coordinates: null,
      };

      const { data: insertData, error: insertError } = await supabase
        .from('tasks')
        .insert(testTask)
        .select();

      if (insertError) {
        setTestResult(prev => prev + `\n❌ Insert failed: ${insertError.message} (Code: ${insertError.code})`);
        console.error('Insert error details:', insertError);
        return;
      }

      setTestResult(prev => prev + '\n✅ Insert successful');

      // Test 4: Clean up - delete the test record
      const { error: deleteError } = await supabase
        .from('tasks')
        .delete()
        .eq('id', testTask.id);

      if (deleteError) {
        setTestResult(prev => prev + `\n⚠️ Cleanup failed: ${deleteError.message}`);
      } else {
        setTestResult(prev => prev + '\n✅ Cleanup successful');
      }

      setTestResult(prev => prev + '\n\n🎉 All tests passed! Database is working correctly.');

    } catch (error: any) {
      setTestResult(prev => prev + `\n❌ Unexpected error: ${error.message}`);
      console.error('Test error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="mb-4">
      <CardHeader>
        <CardTitle>Database Connection Test</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <Button onClick={testConnection} disabled={isLoading}>
          {isLoading ? 'Testing...' : 'Test Database Connection'}
        </Button>
        
        {testResult && (
          <div className="bg-muted p-4 rounded-lg">
            <pre className="text-sm whitespace-pre-wrap">{testResult}</pre>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
