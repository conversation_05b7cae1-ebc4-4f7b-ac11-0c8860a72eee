import React, { useState, useEffect } from 'react';
import { User } from '@supabase/supabase-js';
import { Task } from '@/types/task';
import { VoiceButton } from './VoiceButton';
import { TaskList } from './TaskList';
import { LocationPermission } from './LocationPermission';
import { ManualTaskInput } from './ManualTaskInput';
import { DistanceSettings } from './DistanceSettings';
import { TaskEditModal } from './TaskEditModal';
// import { LocationDebugger } from './LocationDebugger';

import { useVoiceRecognition } from '@/hooks/useVoiceRecognition';
import { useGeolocation } from '@/hooks/useGeolocation';
import { useTasks } from '@/hooks/useTasks';
import { parseTaskFromSpeech } from '@/utils/taskParser';
import { locationService } from '@/services/locationService';
import { initializeGoogleMaps, getGoogleMapsService } from '@/services/googleMapsService';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { Sparkles, MapPin, CheckCircle, LogOut, Loader2 } from 'lucide-react';

interface EvelinAppProps {
  user: User;
  onSignOut: () => void;
}

export const EvelinApp: React.FC<EvelinAppProps> = ({ user, onSignOut }) => {
  const [defaultNotificationDistance, setDefaultNotificationDistance] = useState(200);
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);
  const [showManualInput, setShowManualInput] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [editingTask, setEditingTask] = useState<Task | null>(null);
  const [googleMapsInitialized, setGoogleMapsInitialized] = useState(false);
  const { toast } = useToast();

  // Use the useTasks hook for persistent task management
  const { tasks, loading: tasksLoading, addTask, updateTask, deleteTask } = useTasks(user);
  
  const {
    isListening,
    isProcessing,
    transcript,
    isSupported,
    startListening,
    stopListening,
    clearTranscript,
    error: voiceError,
  } = useVoiceRecognition();

  const {
    latitude,
    longitude,
    permission,
    getCurrentPosition,
    startWatching,
    stopWatching,
    calculateDistance,
    isWatching,
    error: locationError,
  } = useGeolocation();

  // Handle new voice transcript
  useEffect(() => {
    if (transcript && !isProcessing) {
      const handleVoiceTask = async () => {
        const newTask = parseTaskFromSpeech(transcript);

        if (newTask.text) {
          const fullTask: Task = {
            ...newTask,
            id: newTask.id!,
            text: newTask.text,
            completed: false,
            createdAt: new Date(),
            notificationDistance: defaultNotificationDistance,
          };

          // Resolve coordinates if location is specified
          if (fullTask.location && latitude && longitude) {
            try {
              const result = await locationService.resolveTaskLocation(
                fullTask,
                { latitude, longitude }
              );
              if (result) {
                fullTask.coordinates = result.coordinates;
                if (result.placeDetails) {
                  fullTask.placeDetails = result.placeDetails;
                }
              }
            } catch (error) {
              console.error('Error resolving location:', error);
            }
          }

          // Use the addTask function from useTasks hook
          try {
            await addTask(fullTask);
            toast({
              title: "Task Added!",
              description: `"${fullTask.text}"${fullTask.location ? ` - Will remind you near ${fullTask.location}` : ''}`,
            });
          } catch (error) {
            console.error('Error adding task:', error);
            toast({
              title: "Error",
              description: "Failed to save task. Please try again.",
              variant: "destructive",
            });
          }
        }
      };

      handleVoiceTask();
      clearTranscript();
    }
  }, [transcript, isProcessing, clearTranscript, toast, defaultNotificationDistance, addTask, latitude, longitude]);

  // Start location watching when permission granted
  useEffect(() => {
    if (permission === 'granted' && !isWatching) {
      startWatching();
    }
    return () => {
      if (isWatching) {
        stopWatching();
      }
    };
  }, [permission, isWatching, startWatching, stopWatching]);

  // Initialize Google Maps API
  useEffect(() => {
    const initGoogleMaps = async () => {
      const apiKey = import.meta.env.VITE_GOOGLE_MAPS_API_KEY;
      if (apiKey && apiKey !== 'YOUR_GOOGLE_MAPS_API_KEY') {
        try {
          const googleMapsService = initializeGoogleMaps(apiKey);
          await googleMapsService.initialize();
          setGoogleMapsInitialized(true);
          console.log('🗺️ Google Maps API initialized successfully');
        } catch (error) {
          console.error('Failed to initialize Google Maps API:', error);
          console.log('📍 Falling back to OpenStreetMap geocoding');
        }
      } else {
        console.log('📍 No Google Maps API key found, using OpenStreetMap geocoding');
      }
    };

    initGoogleMaps();
  }, []);

  // Check for nearby tasks and show notifications
  useEffect(() => {
    if (latitude && longitude && tasks.length > 0 && notificationsEnabled) {
      console.log('🔍 Checking for nearby tasks...', {
        userLocation: { latitude, longitude },
        totalTasks: tasks.length,
        activeTasks: tasks.filter(t => !t.completed).length
      });

      const nearbyTasks = tasks.filter(task => {
        if (task.completed || task.notificationTriggered) {
          return false;
        }

        // Use task's stored coordinates if available
        let taskCoordinates = task.coordinates;

        // If no coordinates stored, try to resolve them
        if (!taskCoordinates && task.location) {
          // For immediate notification checking, use fallback coordinates
          taskCoordinates = getSimulatedCoordinates(task.location);

          console.log(`📍 Task "${task.text}" at "${task.location}":`, {
            hasStoredCoordinates: !!task.coordinates,
            usingFallback: !task.coordinates,
            coordinates: taskCoordinates
          });

          // Asynchronously resolve and update coordinates for future use
          locationService.resolveTaskLocation(task, { latitude, longitude })
            .then(result => {
              if (result && JSON.stringify(result.coordinates) !== JSON.stringify(taskCoordinates)) {
                console.log(`🔄 Updating coordinates for "${task.location}":`, result);
                const updateData: Partial<Task> = { coordinates: result.coordinates };
                if (result.placeDetails) {
                  updateData.placeDetails = result.placeDetails;
                }
                updateTask(task.id, updateData).catch(console.error);
              }
            })
            .catch(console.error);
        }

        if (!taskCoordinates) {
          console.log(`❌ No coordinates found for task: "${task.text}"`);
          return false;
        }

        const distance = calculateDistance(
          latitude,
          longitude,
          taskCoordinates.lat,
          taskCoordinates.lng
        );

        const taskNotificationDistance = task.notificationDistance || defaultNotificationDistance;
        const isNearby = distance < taskNotificationDistance;

        console.log(`📏 Distance check for "${task.text}":`, {
          distance: Math.round(distance),
          threshold: taskNotificationDistance,
          isNearby,
          coordinates: taskCoordinates
        });

        return isNearby;
      });

      console.log(`🔔 Found ${nearbyTasks.length} nearby tasks that should trigger notifications`);

      nearbyTasks.forEach(async (task) => {
        if (!task.notificationTriggered) {
          console.log(`🚨 Triggering notification for: "${task.text}"`);

          // Calculate actual distance for this task
          let actualDistance = 0;
          if (task.coordinates) {
            actualDistance = calculateDistance(
              latitude,
              longitude,
              task.coordinates.lat,
              task.coordinates.lng
            );
          }

          // Generate enhanced notification message
          const notificationMsg = generateNotificationMessage(task, actualDistance);

          // Mark as notified to prevent spam
          try {
            await updateTask(task.id, { notificationTriggered: true });

            // Show enhanced toast notification with action button
            toast({
              title: notificationMsg.title,
              description: notificationMsg.shortBody,
              duration: 10000,
              action: task.coordinates ? {
                label: "Show on Map",
                onClick: () => openInGoogleMaps(task)
              } : undefined,
            });

            // Show enhanced browser notification if permission granted
            if (Notification.permission === 'granted') {
              const notification = new Notification(notificationMsg.title, {
                body: notificationMsg.body,
                icon: '/favicon.ico',
                tag: `task-${task.id}`, // Prevent duplicate notifications
                requireInteraction: true, // Keep notification visible until user interacts
              });

              // Auto-close after 15 seconds
              setTimeout(() => {
                notification.close();
              }, 15000);

              console.log('✅ Enhanced browser notification sent:', notificationMsg);
            } else {
              console.log('❌ Browser notification permission not granted');
            }
          } catch (error) {
            console.error('Error updating task notification status:', error);
          }
        }
      });
    }
  }, [latitude, longitude, tasks, calculateDistance, toast, notificationsEnabled, defaultNotificationDistance, updateTask]);

  // Request notification permission on mount and start location watching
  useEffect(() => {
    if (Notification.permission === 'default') {
      Notification.requestPermission();
    }
  }, []);

  // Start location watching when permission is granted
  useEffect(() => {
    if (permission === 'granted' && !isWatching) {
      startWatching();
    } else if (permission === 'denied' && isWatching) {
      stopWatching();
    }
  }, [permission, isWatching, startWatching, stopWatching]);

  const handleCompleteTask = async (taskId: string) => {
    const task = tasks.find(t => t.id === taskId);
    if (task) {
      try {
        await updateTask(taskId, { completed: !task.completed });
      } catch (error) {
        console.error('Error updating task:', error);
        toast({
          title: "Error",
          description: "Failed to update task. Please try again.",
          variant: "destructive",
        });
      }
    }
  };

  const handleDeleteTask = async (taskId: string) => {
    try {
      await deleteTask(taskId);
    } catch (error) {
      console.error('Error deleting task:', error);
      toast({
        title: "Error",
        description: "Failed to delete task. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleAddManualTask = async (task: Task) => {
    try {
      // Resolve coordinates if location is specified
      if (task.location && latitude && longitude) {
        try {
          const coordinates = await locationService.resolveTaskLocation(
            task,
            { latitude, longitude }
          );
          if (coordinates) {
            task.coordinates = coordinates;
          }
        } catch (error) {
          console.error('Error resolving location:', error);
        }
      }

      await addTask(task);
      toast({
        title: "Task Added!",
        description: `"${task.text}"${task.location ? ` - Will remind you near ${task.location}` : ''}`,
      });
      setShowManualInput(false);
    } catch (error) {
      console.error('Error adding task:', error);
      toast({
        title: "Error",
        description: "Failed to save task. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleEditTask = (task: Task) => {
    setEditingTask(task);
  };

  const handleSaveEditedTask = async (taskId: string, updates: Partial<Task>) => {
    try {
      // Resolve coordinates if location changed
      if (updates.location && latitude && longitude) {
        try {
          const result = await locationService.resolveTaskLocation(
            { ...editingTask!, ...updates } as Task,
            { latitude, longitude }
          );
          if (result) {
            updates.coordinates = result.coordinates;
            if (result.placeDetails) {
              updates.placeDetails = result.placeDetails;
            }
          }
        } catch (error) {
          console.error('Error resolving location:', error);
        }
      }

      await updateTask(taskId, updates);
      toast({
        title: "Task Updated!",
        description: "Your task has been successfully updated.",
      });
      setEditingTask(null);
    } catch (error) {
      console.error('Error updating task:', error);
      toast({
        title: "Error",
        description: "Failed to update task. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Generate enhanced notification message
  const generateNotificationMessage = (task: Task, distance: number) => {
    const distanceText = distance < 1000
      ? `${Math.round(distance)}m away`
      : `${(distance / 1000).toFixed(1)}km away`;

    const placeName = task.placeDetails?.name || task.location || 'the location';
    const action = task.text.toLowerCase().includes('buy') ? 'buy' : 'complete your task';

    let message = `There is ${placeName} ${distanceText}`;

    if (task.text.toLowerCase().includes('buy')) {
      const item = task.text.replace(/buy\s+/i, '').replace(/\s+from.*/i, '');
      message += ` - you can go and buy ${item}`;
    } else {
      message += ` - you can go and ${action}`;
    }

    message += '. If you need directions, click "Show on Map" to navigate with Google Maps.';

    return {
      title: `📍 ${placeName} Nearby!`,
      body: message,
      shortBody: `${placeName} is ${distanceText} - ${task.text}`
    };
  };

  // Open location in Google Maps
  const openInGoogleMaps = (task: Task) => {
    if (task.coordinates) {
      const { lat, lng } = task.coordinates;
      const placeName = task.placeDetails?.name || task.location || 'location';
      const url = `https://www.google.com/maps/dir/?api=1&destination=${lat},${lng}&destination_place_id=${task.placeDetails?.placeId || ''}&travelmode=walking`;
      window.open(url, '_blank');

      toast({
        title: "Opening Google Maps",
        description: `Getting directions to ${placeName}`,
      });
    }
  };

  const handleTestNotification = (task: Task) => {
    console.log(`🧪 Testing notification for: "${task.text}"`);

    // Generate enhanced notification message
    const notificationMsg = generateNotificationMessage(task, 250); // Simulate 250m distance

    // Show enhanced toast notification with action button
    toast({
      title: notificationMsg.title,
      description: notificationMsg.shortBody,
      duration: 8000,
      action: task.coordinates ? {
        label: "Show on Map",
        onClick: () => openInGoogleMaps(task)
      } : undefined,
    });

    // Show browser notification if permission granted
    if (Notification.permission === 'granted') {
      new Notification(notificationMsg.title, {
        body: notificationMsg.body,
        icon: '/favicon.ico',
        tag: `test-${task.id}`,
        requireInteraction: true,
      });
    } else {
      toast({
        title: "Notification Permission Required",
        description: "Please enable browser notifications to test this feature.",
        variant: "destructive",
      });
    }
  };

  const handleDistanceChange = (distance: number) => {
    setDefaultNotificationDistance(distance);
  };

  const handleNotificationsToggle = (enabled: boolean) => {
    setNotificationsEnabled(enabled);
    if (enabled) {
      // Re-request notification permission if needed
      if (Notification.permission === 'default') {
        Notification.requestPermission();
      }
    }
  };

  const getSimulatedCoordinates = (location?: string) => {
    // Simulate some locations for demo purposes
    const locationCoords: Record<string, { lat: number; lng: number }> = {
      'lidl': { lat: 51.5074, lng: -0.1278 },
      'pharmacy': { lat: 51.5075, lng: -0.1280 },
      'tesco': { lat: 51.5076, lng: -0.1282 },
      'bank': { lat: 51.5077, lng: -0.1284 },
    };
    
    return location ? locationCoords[location] : null;
  };

  const activeTasks = tasks.filter(t => !t.completed);
  const completedTasks = tasks.filter(t => t.completed);

  // Show loading state while tasks are being loaded
  if (tasksLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background p-4">
      <div className="max-w-md mx-auto space-y-6">
        {/* Header */}
        <div className="text-center py-6 relative">
          <Button
            variant="ghost"
            size="sm"
            onClick={onSignOut}
            className="absolute top-0 right-0"
          >
            <LogOut className="w-4 h-4" />
          </Button>
          
          <h1 className="text-3xl font-bold bg-gradient-primary bg-clip-text text-transparent flex items-center justify-center gap-2">
            <Sparkles className="w-8 h-8 text-primary" />
            Evelin
          </h1>
          <p className="text-muted-foreground mt-2">
            Your voice-activated location assistant
          </p>
          <p className="text-xs text-muted-foreground mt-1">
            Welcome, {user.email}
          </p>
        </div>

        {/* Voice Interface */}
        <Card className="bg-gradient-card text-center py-8">
          <CardContent className="flex flex-col items-center space-y-6">
            <VoiceButton
              isListening={isListening}
              isProcessing={isProcessing}
              isSupported={isSupported}
              onStart={startListening}
              onStop={stopListening}
              error={voiceError}
            />
            
            {!isSupported && (
              <p className="text-sm text-destructive">
                Voice recognition is not supported in this browser
              </p>
            )}
            
            {transcript && (
              <div className="p-3 bg-accent rounded-lg max-w-full">
                <p className="text-sm text-accent-foreground">
                  "{transcript}"
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Enhanced Debug Information */}
        <Card className="mb-4">
          <CardHeader>
            <CardTitle className="text-sm">🔧 Debug Info</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-sm">
            <div>Location Permission: <Badge variant={permission === 'granted' ? 'default' : 'destructive'}>{permission}</Badge></div>
            <div>Location Watching: <Badge variant={isWatching ? 'default' : 'secondary'}>{isWatching ? 'Yes' : 'No'}</Badge></div>
            <div>Notifications: <Badge variant={Notification.permission === 'granted' ? 'default' : 'destructive'}>{Notification.permission}</Badge></div>
            <div>Google Maps API: <Badge variant={googleMapsInitialized ? 'default' : 'secondary'}>{googleMapsInitialized ? 'Initialized' : 'Not Available'}</Badge></div>
            {latitude && longitude && (
              <div>Current Location: {latitude.toFixed(4)}, {longitude.toFixed(4)}</div>
            )}
            <div>Active Location Tasks: {tasks.filter(t => t.location && !t.completed).length}</div>
            <div className="flex gap-2">
              <Button
                size="sm"
                onClick={() => {
                  if (Notification.permission === 'granted') {
                    new Notification('Test Notification', {
                      body: 'This is a test notification from Evelin',
                      icon: '/favicon.ico'
                    });
                  } else {
                    toast({
                      title: "Enable Notifications",
                      description: "Please enable browser notifications first",
                      variant: "destructive",
                    });
                  }
                }}
              >
                Test Notification
              </Button>
              {googleMapsInitialized && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={async () => {
                    if (latitude && longitude) {
                      try {
                        const googleMapsService = getGoogleMapsService();
                        if (googleMapsService) {
                          const places = await googleMapsService.searchNearbyPlaces('Lidl', { latitude, longitude }, 2000);
                          console.log('🗺️ Google Maps test search results:', places);
                          toast({
                            title: "Google Maps Test",
                            description: `Found ${places.length} Lidl stores nearby. Check console for details.`,
                          });
                        }
                      } catch (error) {
                        console.error('Google Maps test failed:', error);
                        toast({
                          title: "Google Maps Test Failed",
                          description: "Check console for error details",
                          variant: "destructive",
                        });
                      }
                    }
                  }}
                >
                  Test Google Maps
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Location Permission */}
        <LocationPermission
          permission={permission}
          onRequestPermission={getCurrentPosition}
          error={locationError}
        />

        {/* Manual Task Input */}
        <ManualTaskInput
          onAddTask={handleAddManualTask}
          defaultDistance={defaultNotificationDistance}
        />

        {/* Distance Settings */}
        <DistanceSettings
          defaultDistance={defaultNotificationDistance}
          onDistanceChange={handleDistanceChange}
          notificationsEnabled={notificationsEnabled}
          onNotificationsToggle={handleNotificationsToggle}
          isExpanded={showSettings}
          onToggleExpanded={() => setShowSettings(!showSettings)}
        />

        {/* Status Bar */}
        <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
          <div className="flex items-center space-x-2">
            <MapPin className={`w-4 h-4 ${permission === 'granted' ? 'text-success' : 'text-muted-foreground'}`} />
            <span className="text-sm">
              {permission === 'granted' ? `Location tracking (${defaultNotificationDistance}m)` : 'Location disabled'}
            </span>
          </div>
          
          <div className="flex items-center space-x-4">
            <Badge variant="secondary">
              {activeTasks.length} active
            </Badge>
            {completedTasks.length > 0 && (
              <Badge variant="outline">
                {completedTasks.length} done
              </Badge>
            )}
          </div>
        </div>

        {/* Tasks */}
        <div className="space-y-4">
          <TaskList
            tasks={tasks}
            onCompleteTask={handleCompleteTask}
            onDeleteTask={handleDeleteTask}
            onEditTask={handleEditTask}
            onShowOnMap={openInGoogleMaps}
            userLocation={latitude && longitude ? { latitude, longitude } : undefined}
            calculateDistance={calculateDistance}
          />
        </div>

        {/* Task Edit Modal */}
        <TaskEditModal
          task={editingTask}
          isOpen={editingTask !== null}
          onClose={() => setEditingTask(null)}
          onSave={handleSaveEditedTask}
        />

        {/* Demo Instructions */}
        {tasks.length === 0 && (
          <Card className="bg-accent/50 border-accent">
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Sparkles className="w-5 h-5" />
                Try saying:
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <p className="text-sm">"Buy sugar free pepsi from Lidl"</p>
              <p className="text-sm">"Pick up medicine from the pharmacy"</p>
              <p className="text-sm">"Get cash from the bank"</p>
              <p className="text-sm">"Buy groceries from Tesco"</p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};