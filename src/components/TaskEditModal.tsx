import React, { useState, useEffect } from 'react';
import { Task } from '@/types/task';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { MapPin, Clock } from 'lucide-react';

interface TaskEditModalProps {
  task: Task | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: (taskId: string, updates: Partial<Task>) => Promise<void>;
}

const locationOptions = [
  { value: 'custom', label: 'Type custom location...', category: 'custom' },
  { value: 'lidl', label: 'Lidl', category: 'grocery' },
  { value: 'tesco', label: 'Tesco', category: 'grocery' },
  { value: 'asda', label: 'ASDA', category: 'grocery' },
  { value: 'sainsburys', label: 'Sainsburys', category: 'grocery' },
  { value: 'morrisons', label: 'Morrisons', category: 'grocery' },
  { value: 'aldi', label: 'Aldi', category: 'grocery' },
  { value: 'waitrose', label: 'Waitrose', category: 'grocery' },
  { value: 'pharmacy', label: 'Any Pharmacy', category: 'pharmacy' },
  { value: 'boots', label: 'Boots', category: 'pharmacy' },
  { value: 'superdrug', label: 'Superdrug', category: 'pharmacy' },
  { value: 'bank', label: 'Any Bank/ATM', category: 'finance' },
  { value: 'barclays', label: 'Barclays', category: 'finance' },
  { value: 'lloyds', label: 'Lloyds', category: 'finance' },
  { value: 'hsbc', label: 'HSBC', category: 'finance' },
  { value: 'petrol station', label: 'Petrol Station', category: 'fuel' },
  { value: 'shell', label: 'Shell', category: 'fuel' },
  { value: 'bp', label: 'BP', category: 'fuel' },
  { value: 'post office', label: 'Post Office', category: 'postal' },
  { value: 'gym', label: 'Gym', category: 'fitness' },
  { value: 'restaurant', label: 'Restaurant', category: 'food' },
  { value: 'mcdonalds', label: 'McDonalds', category: 'food' },
  { value: 'starbucks', label: 'Starbucks', category: 'food' },
];

const distanceOptions = [
  { value: 50, label: '50 meters' },
  { value: 100, label: '100 meters' },
  { value: 200, label: '200 meters' },
  { value: 500, label: '500 meters' },
  { value: 1000, label: '1 kilometer' },
  { value: 2000, label: '2 kilometers' },
];

export const TaskEditModal: React.FC<TaskEditModalProps> = ({
  task,
  isOpen,
  onClose,
  onSave,
}) => {
  const [taskText, setTaskText] = useState('');
  const [selectedLocation, setSelectedLocation] = useState('');
  const [customLocation, setCustomLocation] = useState('');
  const [notificationDistance, setNotificationDistance] = useState(200);
  const [isLoading, setIsLoading] = useState(false);

  // Reset form when task changes
  useEffect(() => {
    if (task) {
      setTaskText(task.text);

      // Check if the location is a predefined option or custom
      const predefinedLocation = locationOptions.find(opt => opt.value === task.location);
      if (predefinedLocation && predefinedLocation.value !== 'custom') {
        setSelectedLocation(task.location || '');
        setCustomLocation('');
      } else {
        setSelectedLocation('custom');
        setCustomLocation(task.location || '');
      }

      setNotificationDistance(task.notificationDistance || 200);
    }
  }, [task]);

  const handleSave = async () => {
    if (!task || !taskText.trim() || !selectedLocation) return;

    // If custom location is selected, custom location text is required
    if (selectedLocation === 'custom' && !customLocation.trim()) return;

    setIsLoading(true);
    try {
      const selectedLocationData = locationOptions.find(opt => opt.value === selectedLocation);
      const finalLocation = selectedLocation === 'custom' ? customLocation.trim() : selectedLocation;
      const finalCategory = selectedLocation === 'custom' ? 'custom' : selectedLocationData?.category;

      const updates: Partial<Task> = {
        text: taskText.trim(),
        location: finalLocation,
        category: finalCategory,
        notificationDistance: notificationDistance,
        // Reset coordinates when location changes so they get resolved again
        coordinates: task.location !== finalLocation ? undefined : task.coordinates,
      };

      await onSave(task.id, updates);
      onClose();
    } catch (error) {
      console.error('Error saving task:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading) {
      onClose();
    }
  };

  if (!task) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MapPin className="w-5 h-5" />
            Edit Task
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <Label htmlFor="task-text">Task Description</Label>
            <Textarea
              id="task-text"
              placeholder="What do you need to do?"
              value={taskText}
              onChange={(e) => setTaskText(e.target.value)}
              className="min-h-[80px]"
              disabled={isLoading}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="location">Location <span className="text-destructive">*</span></Label>
            <Select value={selectedLocation} onValueChange={setSelectedLocation} disabled={isLoading} required>
              <SelectTrigger>
                <SelectValue placeholder="Select a location" />
              </SelectTrigger>
              <SelectContent>
                {locationOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {selectedLocation === 'custom' && (
            <div className="space-y-2">
              <Label htmlFor="custom-location">Custom Location <span className="text-destructive">*</span></Label>
              <Input
                id="custom-location"
                type="text"
                placeholder="Enter location name (e.g., 'Westfield Shopping Centre', 'Local Coffee Shop')"
                value={customLocation}
                onChange={(e) => setCustomLocation(e.target.value)}
                disabled={isLoading}
                required
              />
            </div>
          )}

          {selectedLocation && (
            <div className="space-y-2">
              <Label htmlFor="distance" className="flex items-center gap-2">
                <Clock className="w-4 h-4" />
                Notification Distance
              </Label>
              <Select 
                value={notificationDistance.toString()} 
                onValueChange={(value) => setNotificationDistance(parseInt(value))}
                disabled={isLoading}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {distanceOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value.toString()}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={
              isLoading ||
              !taskText.trim() ||
              !selectedLocation ||
              (selectedLocation === 'custom' && !customLocation.trim())
            }
          >
            {isLoading ? 'Saving...' : 'Save Changes'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
