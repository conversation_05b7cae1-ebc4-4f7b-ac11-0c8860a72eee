import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Plus, MapPin } from 'lucide-react';
import { parseTaskFromSpeech } from '@/utils/taskParser';
import { Task } from '@/types/task';

interface ManualTaskInputProps {
  onAddTask: (task: Task) => void;
  defaultDistance: number;
}

const locationOptions = [
  { value: 'custom', label: 'Type custom location...', category: 'custom' },
  { value: 'lidl', label: 'Lidl', category: 'grocery' },
  { value: 'tesco', label: 'Tesco', category: 'grocery' },
  { value: 'asda', label: 'ASDA', category: 'grocery' },
  { value: 'sainsburys', label: 'Sainsburys', category: 'grocery' },
  { value: 'morrisons', label: 'Morrisons', category: 'grocery' },
  { value: 'aldi', label: 'Aldi', category: 'grocery' },
  { value: 'waitrose', label: 'Waitrose', category: 'grocery' },
  { value: 'pharmacy', label: 'Any Pharmacy', category: 'pharmacy' },
  { value: 'boots', label: 'Boots', category: 'pharmacy' },
  { value: 'superdrug', label: 'Superdrug', category: 'pharmacy' },
  { value: 'bank', label: 'Any Bank/ATM', category: 'finance' },
  { value: 'barclays', label: 'Barclays', category: 'finance' },
  { value: 'lloyds', label: 'Lloyds', category: 'finance' },
  { value: 'hsbc', label: 'HSBC', category: 'finance' },
  { value: 'petrol station', label: 'Petrol Station', category: 'fuel' },
  { value: 'shell', label: 'Shell', category: 'fuel' },
  { value: 'bp', label: 'BP', category: 'fuel' },
  { value: 'post office', label: 'Post Office', category: 'postal' },
  { value: 'gym', label: 'Gym', category: 'fitness' },
  { value: 'restaurant', label: 'Restaurant', category: 'food' },
  { value: 'mcdonalds', label: 'McDonalds', category: 'food' },
  { value: 'starbucks', label: 'Starbucks', category: 'food' },
];

export const ManualTaskInput: React.FC<ManualTaskInputProps> = ({
  onAddTask,
  defaultDistance,
}) => {
  const [taskText, setTaskText] = useState('');
  const [selectedLocation, setSelectedLocation] = useState('');
  const [customLocation, setCustomLocation] = useState('');
  const [customDistance, setCustomDistance] = useState(defaultDistance.toString());
  const [isExpanded, setIsExpanded] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!taskText.trim()) return;

    // Location is now required
    if (!selectedLocation) return;

    // If custom location is selected, custom location text is required
    if (selectedLocation === 'custom' && !customLocation.trim()) return;

    const selectedLocationData = locationOptions.find(opt => opt.value === selectedLocation);
    const finalLocation = selectedLocation === 'custom' ? customLocation.trim() : selectedLocation;
    const finalCategory = selectedLocation === 'custom' ? 'custom' : selectedLocationData?.category;

    const newTask: Task = {
      id: crypto.randomUUID(),
      text: taskText.trim(),
      location: finalLocation,
      category: finalCategory,
      completed: false,
      createdAt: new Date(),
      notificationDistance: parseInt(customDistance) || defaultDistance,
    };

    onAddTask(newTask);
    setTaskText('');
    setSelectedLocation('');
    setCustomLocation('');
    setCustomDistance(defaultDistance.toString());
    setIsExpanded(false);
  };

  if (!isExpanded) {
    return (
      <Card className="bg-gradient-card">
        <CardContent className="p-4">
          <Button
            onClick={() => setIsExpanded(true)}
            variant="outline"
            className="w-full flex items-center gap-2"
          >
            <Plus className="w-4 h-4" />
            Add Task Manually
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-gradient-card">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center gap-2">
          <Plus className="w-5 h-5" />
          Add New Task
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="task-text">Task Description</Label>
            <Input
              id="task-text"
              value={taskText}
              onChange={(e) => setTaskText(e.target.value)}
              placeholder="e.g., Buy milk, Pick up prescription..."
              className="w-full"
              autoFocus
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="location-select" className="flex items-center gap-2">
              <MapPin className="w-4 h-4" />
              Location <span className="text-destructive">*</span>
            </Label>
            <Select value={selectedLocation} onValueChange={setSelectedLocation} required>
              <SelectTrigger>
                <SelectValue placeholder="Choose a location..." />
              </SelectTrigger>
              <SelectContent className="bg-popover z-50">
                {locationOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {selectedLocation === 'custom' && (
            <div className="space-y-2">
              <Label htmlFor="custom-location">Custom Location <span className="text-destructive">*</span></Label>
              <Input
                id="custom-location"
                type="text"
                placeholder="Enter location name (e.g., 'Westfield Shopping Centre', 'Local Coffee Shop')"
                value={customLocation}
                onChange={(e) => setCustomLocation(e.target.value)}
                required
              />
            </div>
          )}

          {selectedLocation && (
            <div className="space-y-2">
              <Label htmlFor="distance">Notification Distance (meters)</Label>
              <Select value={customDistance} onValueChange={setCustomDistance}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-popover z-50">
                  <SelectItem value="50">50 meters</SelectItem>
                  <SelectItem value="100">100 meters</SelectItem>
                  <SelectItem value="200">200 meters</SelectItem>
                  <SelectItem value="500">500 meters</SelectItem>
                  <SelectItem value="1000">1 kilometer</SelectItem>
                </SelectContent>
              </Select>
              <Input
                type="number"
                value={customDistance}
                onChange={(e) => setCustomDistance(e.target.value)}
                placeholder="Custom distance in meters"
                min="10"
                max="5000"
                className="mt-2"
              />
            </div>
          )}

          <div className="flex gap-2 pt-2">
            <Button
              type="submit"
              className="flex-1"
              disabled={
                !taskText.trim() ||
                !selectedLocation ||
                (selectedLocation === 'custom' && !customLocation.trim())
              }
            >
              Add Task
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsExpanded(false)}
            >
              Cancel
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};