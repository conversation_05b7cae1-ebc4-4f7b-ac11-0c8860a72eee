import React from 'react';
import { Task } from '@/types/task';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Check, MapPin, Trash2, Edit, Navigation } from 'lucide-react';
import { cn } from '@/lib/utils';
import { getCategoryDisplayName } from '@/utils/taskParser';

interface TaskCardProps {
  task: Task;
  onComplete: (taskId: string) => void;
  onDelete: (taskId: string) => void;
  onEdit: (task: Task) => void;
  onShowOnMap?: (task: Task) => void;
  distance?: number;
}

export const TaskCard: React.FC<TaskCardProps> = ({
  task,
  onComplete,
  onDelete,
  onEdit,
  onShowOnMap,
  distance,
}) => {
  const notificationDistance = task.notificationDistance || 500;
  const isNearby = distance !== undefined && distance < notificationDistance;

  return (
    <Card className={cn(
      "bg-gradient-card transition-all duration-300 hover:shadow-soft",
      "animate-fade-in-up",
      task.completed && "opacity-60",
      isNearby && "ring-2 ring-primary border-primary/20 shadow-voice"
    )}>
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <p className={cn(
              "text-base font-medium mb-2",
              task.completed && "line-through text-muted-foreground"
            )}>
              {task.text}
            </p>
            
            {task.location && (
              <div className="flex items-center space-x-2 mb-2">
                <MapPin className="w-4 h-4 text-primary" />
                <span className="text-sm text-muted-foreground">
                  {getCategoryDisplayName(task.category || task.location)}
                </span>
                {task.notificationDistance && (
                  <span className="text-xs px-2 py-1 rounded-full bg-accent text-accent-foreground">
                    Notify at {task.notificationDistance}m
                  </span>
                )}
                {distance !== undefined && (
                  <span className={cn(
                    "text-xs px-2 py-1 rounded-full",
                    isNearby 
                      ? "bg-primary text-primary-foreground" 
                      : "bg-muted text-muted-foreground"
                  )}>
                    {distance < 1000 
                      ? `${Math.round(distance)}m away`
                      : `${(distance / 1000).toFixed(1)}km away`
                    }
                  </span>
                )}
              </div>
            )}
            
            <p className="text-xs text-muted-foreground">
              {task.createdAt.toLocaleDateString()} at {task.createdAt.toLocaleTimeString()}
            </p>
          </div>
          
          <div className="flex space-x-2">
            {task.coordinates && onShowOnMap && (
              <Button
                size="sm"
                variant="outline"
                onClick={() => onShowOnMap(task)}
                className="w-8 h-8 p-0"
                title={`Navigate to ${task.placeDetails?.name || task.location}`}
              >
                <Navigation className="w-4 h-4" />
              </Button>
            )}
            <Button
              size="sm"
              variant="outline"
              onClick={() => onEdit(task)}
              className="w-8 h-8 p-0"
              title="Edit task"
            >
              <Edit className="w-4 h-4" />
            </Button>
            <Button
              size="sm"
              variant={task.completed ? "secondary" : "default"}
              onClick={() => onComplete(task.id)}
              className="w-8 h-8 p-0"
              title={task.completed ? "Mark as incomplete" : "Mark as complete"}
            >
              <Check className="w-4 h-4" />
            </Button>
            <Button
              size="sm"
              variant="destructive"
              onClick={() => onDelete(task.id)}
              className="w-8 h-8 p-0"
              title="Delete task"
            >
              <Trash2 className="w-4 h-4" />
            </Button>
          </div>
        </div>
        
        {isNearby && (
          <div className="mt-3 p-3 bg-primary/10 rounded-lg border border-primary/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-primary">
                  📍 {task.placeDetails?.name || task.location} is nearby!
                </p>
                {distance !== undefined && (
                  <p className="text-xs text-primary/70 mt-1">
                    {distance < 1000 ? `${Math.round(distance)}m away` : `${(distance / 1000).toFixed(1)}km away`}
                  </p>
                )}
              </div>
              {task.coordinates && onShowOnMap && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => onShowOnMap(task)}
                  className="text-xs px-2 py-1 h-auto"
                >
                  <Navigation className="w-3 h-3 mr-1" />
                  Navigate
                </Button>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};