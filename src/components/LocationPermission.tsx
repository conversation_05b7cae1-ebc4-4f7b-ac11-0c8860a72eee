import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { MapPin, AlertCircle } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface LocationPermissionProps {
  permission: 'granted' | 'denied' | 'prompt';
  onRequestPermission: () => void;
  error?: string;
}

export const LocationPermission: React.FC<LocationPermissionProps> = ({
  permission,
  onRequestPermission,
  error,
}) => {
  if (permission === 'granted') {
    return null;
  }

  return (
    <Card className="bg-gradient-card">
      <CardHeader className="text-center">
        <div className="mx-auto w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mb-4">
          <MapPin className="w-6 h-6 text-primary" />
        </div>
        <CardTitle className="text-lg">Location Access Required</CardTitle>
      </Card<PERSON><PERSON>er>
      <CardContent className="space-y-4">
        <p className="text-sm text-muted-foreground text-center">
          <PERSON><PERSON> needs access to your location to notify you about tasks when you're nearby relevant places.
        </p>
        
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        <div className="space-y-3">
          <Button 
            onClick={onRequestPermission} 
            className="w-full"
            disabled={permission === 'denied'}
          >
            {permission === 'denied' ? 'Permission Denied' : 'Allow Location Access'}
          </Button>
          
          {permission === 'denied' && (
            <p className="text-xs text-muted-foreground text-center">
              Location access was denied. Please enable it in your browser settings to use location-based reminders.
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
};