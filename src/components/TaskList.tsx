import React from 'react';
import { Task } from '@/types/task';
import { TaskCard } from './TaskCard';
import { Badge } from '@/components/ui/badge';
import { getCategoryDisplayName } from '@/utils/taskParser';

interface TaskListProps {
  tasks: Task[];
  onCompleteTask: (taskId: string) => void;
  onDeleteTask: (taskId: string) => void;
  onEditTask: (task: Task) => void;
  onShowOnMap?: (task: Task) => void;
  userLocation?: {
    latitude: number;
    longitude: number;
  };
  calculateDistance?: (lat1: number, lon1: number, lat2: number, lon2: number) => number;
}

export const TaskList: React.FC<TaskListProps> = ({
  tasks,
  onCompleteTask,
  onDeleteTask,
  onEditTask,
  onShowOnMap,
  userLocation,
  calculateDistance,
}) => {
  // Group tasks by category
  const tasksByCategory = tasks.reduce((acc, task) => {
    const category = task.category || 'general';
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(task);
    return acc;
  }, {} as Record<string, Task[]>);

  // Calculate distances for tasks with coordinates
  const getTaskDistance = (task: Task): number | undefined => {
    if (!userLocation || !task.coordinates || !calculateDistance) {
      return undefined;
    }
    
    return calculateDistance(
      userLocation.latitude,
      userLocation.longitude,
      task.coordinates.lat,
      task.coordinates.lng
    );
  };

  // Sort tasks by completion status and proximity
  const sortTasks = (tasks: Task[]): Task[] => {
    return tasks.sort((a, b) => {
      // Completed tasks go to bottom
      if (a.completed !== b.completed) {
        return a.completed ? 1 : -1;
      }
      
      // Sort by proximity if location data available
      const distanceA = getTaskDistance(a);
      const distanceB = getTaskDistance(b);
      
      if (distanceA !== undefined && distanceB !== undefined) {
        return distanceA - distanceB;
      }
      
      // Sort by creation date (newest first)
      return b.createdAt.getTime() - a.createdAt.getTime();
    });
  };

  if (tasks.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-lg text-muted-foreground mb-2">No tasks yet</p>
        <p className="text-sm text-muted-foreground">
          Tap the microphone to add your first task with voice!
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {Object.entries(tasksByCategory).map(([category, categoryTasks]) => (
        <div key={category} className="space-y-3">
          <div className="flex items-center space-x-2">
            <Badge variant="secondary" className="text-sm">
              {getCategoryDisplayName(category)}
            </Badge>
            <span className="text-xs text-muted-foreground">
              {categoryTasks.filter(t => !t.completed).length} active
            </span>
          </div>
          
          <div className="space-y-3">
            {sortTasks(categoryTasks).map((task) => (
              <TaskCard
                key={task.id}
                task={task}
                onComplete={onCompleteTask}
                onDelete={onDeleteTask}
                onEdit={onEditTask}
                onShowOnMap={onShowOnMap}
                distance={getTaskDistance(task)}
              />
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};