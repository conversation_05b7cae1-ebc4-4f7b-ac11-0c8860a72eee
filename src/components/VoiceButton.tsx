import React from 'react';
import { Mi<PERSON>, MicOff, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface VoiceButtonProps {
  isListening: boolean;
  isProcessing: boolean;
  isSupported: boolean;
  onStart: () => void;
  onStop: () => void;
  error?: string;
}

export const VoiceButton: React.FC<VoiceButtonProps> = ({
  isListening,
  isProcessing,
  isSupported,
  onStart,
  onStop,
  error,
}) => {
  const handleClick = () => {
    if (isListening) {
      onStop();
    } else {
      onStart();
    }
  };

  if (!isSupported) {
    return (
      <Button
        variant="secondary"
        size="lg"
        disabled
        className="relative w-20 h-20 rounded-full"
      >
        <MicOff className="w-8 h-8" />
      </Button>
    );
  }

  return (
    <div className="relative">
      {/* Ripple effect for listening state */}
      {isListening && (
        <div className="absolute inset-0 rounded-full bg-voice-active animate-ripple" />
      )}
      
      <Button
        onClick={handleClick}
        size="lg"
        disabled={isProcessing}
        className={cn(
          "relative w-20 h-20 rounded-full transition-all duration-300 shadow-voice",
          "hover:scale-105 active:scale-95",
          isListening && "bg-voice-listening animate-pulse-voice shadow-voice",
          isProcessing && "bg-voice-processing",
          error && "bg-destructive"
        )}
      >
        {isProcessing ? (
          <Loader2 className="w-8 h-8 animate-spin" />
        ) : isListening ? (
          <Mic className="w-8 h-8 text-primary-foreground animate-pulse" />
        ) : (
          <Mic className="w-8 h-8" />
        )}
      </Button>
      
      {/* Status text */}
      <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-center">
        <p className="text-sm font-medium text-muted-foreground">
          {isProcessing 
            ? 'Processing...' 
            : isListening 
            ? 'Listening...' 
            : error
            ? 'Error'
            : 'Tap to speak'
          }
        </p>
      </div>
    </div>
  );
};