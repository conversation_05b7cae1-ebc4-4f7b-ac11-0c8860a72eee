import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Settings, MapPin, Bell, VolumeX } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface DistanceSettingsProps {
  defaultDistance: number;
  onDistanceChange: (distance: number) => void;
  notificationsEnabled: boolean;
  onNotificationsToggle: (enabled: boolean) => void;
  isExpanded?: boolean;
  onToggleExpanded?: () => void;
}

export const DistanceSettings: React.FC<DistanceSettingsProps> = ({
  defaultDistance,
  onDistanceChange,
  notificationsEnabled,
  onNotificationsToggle,
  isExpanded = false,
  onToggleExpanded,
}) => {
  const [customDistance, setCustomDistance] = useState(defaultDistance.toString());
  const { toast } = useToast();

  const handleDistanceChange = (value: string) => {
    const distance = parseInt(value);
    if (distance && distance >= 10 && distance <= 5000) {
      onDistanceChange(distance);
      setCustomDistance(value);
      toast({
        title: "Settings Updated",
        description: `Default notification distance set to ${distance}m`,
      });
    }
  };

  const handleCustomDistanceSubmit = () => {
    const distance = parseInt(customDistance);
    if (distance && distance >= 10 && distance <= 5000) {
      onDistanceChange(distance);
      toast({
        title: "Settings Updated",
        description: `Custom notification distance set to ${distance}m`,
      });
    } else {
      toast({
        title: "Invalid Distance",
        description: "Distance must be between 10 and 5000 meters",
        variant: "destructive",
      });
      setCustomDistance(defaultDistance.toString());
    }
  };

  if (!isExpanded) {
    return (
      <Card className="bg-gradient-card">
        <CardContent className="p-4">
          <Button
            onClick={onToggleExpanded}
            variant="outline"
            size="sm"
            className="w-full flex items-center gap-2"
          >
            <Settings className="w-4 h-4" />
            Notification Settings
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="bg-gradient-card">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center gap-2">
          <Settings className="w-5 h-5" />
          Notification Settings
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between space-x-2">
          <div className="flex items-center space-x-2">
            {notificationsEnabled ? (
              <Bell className="w-4 h-4 text-primary" />
            ) : (
              <VolumeX className="w-4 h-4 text-muted-foreground" />
            )}
            <Label htmlFor="notifications-toggle">Enable Notifications</Label>
          </div>
          <Switch
            id="notifications-toggle"
            checked={notificationsEnabled}
            onCheckedChange={onNotificationsToggle}
          />
        </div>

        {notificationsEnabled && (
          <>
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <MapPin className="w-4 h-4" />
                Default Notification Distance
              </Label>
              <Select 
                value={defaultDistance.toString()} 
                onValueChange={handleDistanceChange}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-popover z-50">
                  <SelectItem value="50">50 meters (very close)</SelectItem>
                  <SelectItem value="100">100 meters (close)</SelectItem>
                  <SelectItem value="200">200 meters (nearby)</SelectItem>
                  <SelectItem value="500">500 meters (moderate)</SelectItem>
                  <SelectItem value="1000">1 kilometer (far)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="custom-distance">Custom Distance (10-5000m)</Label>
              <div className="flex gap-2">
                <Input
                  id="custom-distance"
                  type="number"
                  value={customDistance}
                  onChange={(e) => setCustomDistance(e.target.value)}
                  placeholder="Custom distance"
                  min="10"
                  max="5000"
                  className="flex-1"
                />
                <Button
                  onClick={handleCustomDistanceSubmit}
                  size="sm"
                  variant="outline"
                >
                  Set
                </Button>
              </div>
            </div>

            <div className="p-3 bg-muted rounded-lg">
              <p className="text-sm text-muted-foreground">
                <strong>Current setting:</strong> You'll be notified when you're within{' '}
                <span className="text-primary font-medium">{defaultDistance}m</span> of locations 
                mentioned in your tasks.
              </p>
            </div>
          </>
        )}

        {onToggleExpanded && (
          <Button
            onClick={onToggleExpanded}
            variant="outline"
            size="sm"
            className="w-full"
          >
            Close Settings
          </Button>
        )}
      </CardContent>
    </Card>
  );
};